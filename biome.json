{"$schema": "./node_modules/@biomejs/biome/configuration_schema.json", "organizeImports": {"enabled": false}, "linter": {"enabled": true, "rules": {"recommended": true, "a11y": {"all": false}, "performance": {"noDelete": "warn"}, "suspicious": {"noExplicitAny": "warn", "noArrayIndexKey": "warn"}, "style": {"useSelfClosingElements": "off", "noNonNullAssertion": "warn", "useExponentiationOperator": "warn", "noParameterAssign": "warn"}, "complexity": {"noForEach": "off"}}}, "formatter": {"lineWidth": 110, "indentStyle": "space", "indentWidth": 3}, "files": {"ignore": ["**/*.js", "**/*.d.ts", "shared/thirdparty/*", "shared/languages/*", "src/scripts/Version.json"]}}