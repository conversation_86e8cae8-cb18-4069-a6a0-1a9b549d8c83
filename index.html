<!DOCTYPE html>
<html lang="en">

<head>
   <meta charset="UTF-8" />
   <meta name="viewport" content="width=device-width, initial-scale=1.0" />
   <link rel="icon" type="image/png" sizes="32x32" href="/favicon.png" />
   <title>CivIdle</title>
   <script>
      (function (w, d, a, m) { var s = 'script'; var g = 'GameAnalytics'; w[g] = w[g] || function () { (w[g].q = w[g].q || []).push(arguments) }, a = d.createElement(s), m = d.getElementsByTagName(s)[0]; a.async = 1; a.src = 'https://download.gameanalytics.com/js/GameAnalytics-4.4.5.min.js'; m.parentNode.insertBefore(a, m) })(window, document);
      GameAnalytics("initialize", "4ab316de0a0fae55eafb978c328d3a3f", "4aa92321e7f5eacfedcf68cdd1522e2e525f2ae4");
   </script>
</head>

<body>
   <div id="game-canvas"></div>
   <div id="ui-root"></div>
   <script type="module" src="/src/scripts/main.tsx"></script>
</body>

</html>