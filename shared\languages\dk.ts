export const DK = {
   About: "Om CivIdle",
   AbuSimbel: "Abu Simbel",
   AbuSimbelDesc: "Dobbelt effekt af Ramesses II. Alle tilstødende vidundere får +1 Lykke",
   AccountActiveTrade: "Aktiv Handlende",
   AccountChatBadge: "Chat-ikon",
   AccountCustomColor: "Brugerdefineret farve",
   AccountCustomColorDefault: "Standard",
   AccountGreatPeopleLevelRequirement: "Required Great People Level",
   AccountLevel: "Konto Rang",
   AccountLevelAedile: "Aedil",
   AccountLevelConsul: "Konsul",
   AccountLevelMod: "Moderator",
   AccountLevelPlayTime: "Aktiv Online Spilletid > %{requiredTime} (Din spilletid er %{actualTime})",
   AccountLevelPraetor: "Prætor",
   AccountLevelQuaestor: "Kvestor",
   AccountLevelSupporterPack: "<PERSON>jer Supporterpakke",
   AccountLevelTribune: "Tribun",
   AccountLevelUpgradeConditionAnyHTML: "For at opgradere din konto skal du kun opfylde <b>et af følgende</b> kriterier:",
   AccountPlayTimeRequirement: "Required Play Time",
   AccountRankUp: "Upgrade Account Rank",
   AccountRankUpDesc: "All your progress will be carried over to your new rank",
   AccountRankUpTip: "Congratulations, your account is eligible for a higher rank - click here to upgrade!",
   AccountSupporter: "Supporterpakkeejere",
   AccountTradePriceRange: "Handelsprisområde",
   AccountTradeTileReservationTime: "Handels område Reservation",
   AccountTradeTileReservationTimeDesc: "Dette er den tid, din spiller handels område vil være reserveret for dig, siden du sidst var online. Efter reservationsperioden er slut, vil din område blive tilgængelig for andre spillere",
   AccountTradeValuePerMinute: "Handelsværdi Per Minut",
   AccountTypeShowDetails: "Vis Kontodetaljer",
   AccountUpgradeButton: "Opgrader til Kvestor Rang",
   AccountUpgradeConfirm: "Konto opgradering",
   AccountUpgradeConfirmDescV2: "Opgradering af din konto vil <b>nulstille dit nuværende spil</b> og overføre permanente store personer inden for de tilladte niveauer. Dette <b>kan ikke</b> fortrydes. Er du sikker på, at du vil fortsætte?",
   Acknowledge: "Acknowledge",
   Acropolis: "Akropolis",
   ActorsGuild: "Theater forening",
   AdaLovelace: "Ada Lovelace",
   AdamSmith: "Adam Smith",
   AdjustBuildingCapacity: "Produktionskapacitet",
   AdvisorElectricityContent:
      "Power Plants provide two new systems to you. The first, 'Power' is indicated by the lightning bolt tiles adjacent to the power plant. Some buildings (starting with Radio in World Wars) have a 'requires power' indicator in their list of inputs. <b>This means they must be built on a lightning bolt tile to function</b>. Buildings that require power and have it, will also transmit power to the tiles adjacent to that building, so you can power them from each other as long as at least one is touching a power plant.<br><br>The other system 'electrification' can be applied to <b>any building anywhere</b> on the map as long as it doesn't produce science or workers. This uses up the power generated by the power plant to increase both the consumption and production of the building. More levels of electrification require larger and larger amounts of power. Electrifying buildings that also have 'requires power' is more efficient than electrifying the ones that don't.",
   AdvisorElectricityTitle: "Power and Electrification",
   AdvisorGreatPeopleContent:
      "Each time you enter a new age of technology, you will be able to select a Great Person from that age, and each previous age. These Great People give global bonuses that can increase production, science, happiness, and many other things.<br><br>These bonuses are permanent for the rest of the rebirth. When you rebirth, all of your Great People become permanent, and their bonus lasts forever.<br><br>Picking the same one in a later run will stack your permanent and in-run bonus, and when you rebirth with duplicates, the extras are stored and can be used to upgrade the permanent bonus. That is accessed in the <b>Manage Permanent Great People</b> menu in your Home Building.",
   AdvisorGreatPeopleTitle: "Great People",
   AdvisorHappinessContent:
      "Happiness is the core mechanic in CivIdle that limits expansion. You gain happiness by unlocking new technology, advancing to new ages, building wonders, from Great People who provide it, and a few other ways you can discover as you learn. <b>Each new building costs 1 happiness</b>. For each point above/below 0 happiness, you get a 2% bonus or penalty to your total workers (Capping at -50 and +50 Happiness). You can see a detailed breakdown of your happiness in your <b>Home Building's Happiness section</b>.",
   AdvisorHappinessTitle: "Keep Your People Happy",
   AdvisorOkay: "Got it, thanks!",
   AdvisorScienceContent:
      "Your busy workers generate science, which allows you to unlock new technology and advance your civilization. You can access the research menu a number of ways. By clicking on the science meter, by accessing your unlockable technologies in your Home Building, or by using the 'View' menu. These will all bring you to the tech tree, showing you all the technologies, as well as how much science is required for each. If you have enough science to learn a new technology, simply click on it and press 'unlock' in the sidebar menu. <b>Each new tier and age of technology requires more and more science, but you will unlock new and better ways to gain science as well.</b>",
   AdvisorScienceTitle: "Scientific Discovery!",
   AdvisorSkipAllTutorials: "Skip All Tutorials",
   AdvisorStorageContent:
      "While buildings have a decent amount of storage, they can fill up especially if left idle for a long time. <b>When buildings are full, they can no longer produce</b>. This isn't always an issue, since you clearly have a large stockpile since the building is full. But keeping things producing is generally better.<br><br>One way to address full storage is via a warehouse. When you build a warehouse, you get a menu of every product you've discovered, and you can set the warehouse to pull any products in any amounts as long as the total for all products is within what the warehouse can pull based on its level and storage multiplier.<br><br>An easy way to set up a warehouse is to check off each product you want to import into the warehouse, and use the 'redistribute among selected' buttons to split your import rate and storage equally. If you want buildings to also be able to pull out of the warehouse, make sure to turn on the 'export below max amount' option as well.",
   AdvisorStorageTitle: "Storage and Warehouses",
   AdvisorTraditionContent:
      "Some wonders (Chogha Zanbil, Luxor Temple, Big Ben) provide access to a new set of options, allowing you to customize the path of your rebirth. Each one allows you to choose from 1 of 4 options for your civilization's tradtion, religion and ideology respectively.<br><br>Once you choose one, that choice is locked in for that rebirth, though you can pick others in future rebirths. Once chosen, each one can also be upgraded a number of times by providing the necessary resources. The rewards in each tier are cumulative, so Tier 1 giving +1 production to X and Tier 2 giving +1 production to X means at Tier 2 you will have +2 production to X in total.",
   AdvisorTraditionTitle: "Choosing Paths and Upgradeable Wonders",
   AdvisorWonderContent:
      "Wonders are special buildings that provide global effects which can have a significant impact on your gameplay. In addition to their listed functions, all Wonders give +1 Happiness as well. You need to be careful though, as <b>Wonders require a LOT of materials, and have a higher than normal Builder Capacity as well</b>. This means that they can easily clear out your stockpiles of needed inputs, leaving your other buildings starving. <b>You can turn each input on and off freely</b>, allowing you to build it in stages while you stockpile enough materials to keep everything running.",
   AdvisorWonderTitle: "Wonders Of The World",
   AdvisorWorkerContent:
      "Every time a building produces or transports goods, this requires workers. If you don't have enough workers available, some buildings will fail to run that cycle. The obvious fix for this is to increase your total available workers by building or upgrading structures that make workers (Hut/House/Apartment/Condo).<br><br><b>Be aware though, that buildings turn off while upgrading, and can't provide any of their resources, which includes workers, so you might want to only upgrade one housing building at a time.</b> A good goal for the early stages of the game is to keep aboput 70% of your workers busy. If more than 70% are busy, upgrade/build housing. If fewer than 70% are busy, expand production.",
   AdvisorWorkerTitle: "Worker Management",
   Aeschylus: "Æskylus",
   Agamemnon: "Agamemnon",
   AgeWisdom: "Age Wisdom",
   AgeWisdomDescHTML: "Each level of Age Wisdom provides <b>an equivalent level</b> of eligible Permanent Great People of that age - it can be upgraded with eligible Permanent Great People shards",
   AgeWisdomGreatPeopleShardsNeeded: "You need %{amount} more great people shards for the next Age Wisdom upgrade",
   AgeWisdomGreatPeopleShardsSatisfied: "You have enough great people shards for the next Age Wisdom upgrade",
   AgeWisdomNeedMoreGreatPeopleShards: "Need More Great People Shards",
   AgeWisdomNotEligible: "This Great Person is not eligible for Age Wisdom",
   AgeWisdomSource: "%{age} Wisdom: %{person}",
   AgeWisdomUpgradeWarningHTMLV3: "Age Wisdom <b>does not carry over</b> when upgrading from Tribune to Quaestor",
   AGreatPersonIsBorn: "En stor person er født",
   AircraftCarrier: "Aircraft Carrier",
   AircraftCarrierYard: "Aircraft Carrier Yard",
   Airplane: "Airplane",
   AirplaneFactory: "Airplane Factory",
   Akitu: "Akitu: Ziggurat Of Ur and Euphrates River apply to buildings unlocked in the current age",
   AlanTuring: "Alan Turing",
   AlanTuringDesc: "+%{value} Videnskab fra inaktive arbejdere",
   AlbertEinstein: "Albert Einstein",
   Alcohol: "Alkohol",
   AldersonDisk: "Alderson Disk",
   AldersonDiskDesc: "+25 Happiness. This wonder can be upgraded and each additional upgrade provides +5 Happiness",
   Alloy: "Legering",
   Alps: "Alperne",
   AlpsDesc: "Hvert 10. niveau af en bygning får +1 Produktionskapacitet (+1 Forbrug multiplikator, +1 Produktions multiplikator)",
   Aluminum: "Aluminium",
   AluminumSmelter: "Aluminiums Støberi",
   AmeliaEarhart: "Amelia Earhart",
   American: "American",
   AndrewCarnegie: "Andrew Carnegie",
   AngkorWat: "Angkor Wat",
   AngkorWatDesc: "Alle tilstødende bygninger får +1 Arbejdskapacitet Multiplikator. Giver 1000 Arbejdere",
   AntiCheatFailure: "Din kontorang er blevet begrænset på grund af <b>mangel på beståelse af anti-snyd</b> tjek. Kontakt udvikleren, hvis du ønsker at appellere dette",
   AoiMatsuri: "Aoi Matsuri: Mount Fuji generates double the warp",
   Apartment: "Lejlighed",
   Aphrodite: "Afrodite",
   AphroditeDescV2: "+1 Builder Capacity Multiplier for each level when upgrading buildings over Level 20. All unlocked Classical Age permanent great people get +1 level this run",
   ApolloProgram: "Apollo Program",
   ApolloProgramDesc: "All rocket factories get +2 Production, Worker Capacity and Storage Multiplier. Satellite factories, spaceship factories and nuclear missile silos get +1 Production Multiplier for each adjacent rocket factory",
   ApplyToAll: "Anvend på alle",
   ApplyToAllBuilding: "Anvend på alle %{building}",
   ApplyToBuildingInTile: "Anvend på alle %{building} inden for %{tile} område",
   ApplyToBuildingsToastHTML: "Anvendt med succes på <b>%{count} %{building}</b>",
   Aqueduct: "Akvedukt",
   ArcDeTriomphe: "Triumfbuen",
   ArcDeTriompheDescV2: "Every 1 happiness (capped) provides +1 builder capacity to all buildings",
   Archimedes: "Arkimedes",
   Architecture: "Arkitektur",
   Aristophanes: "Aristofanes",
   AristophanesDesc: "+%{value} Lykke",
   Aristotle: "Aristotle",
   Arithmetic: "Aritmetik",
   Armor: "Rustning",
   Armory: "Arsenal",
   ArtificialIntelligence: "Kunstig Intelligens",
   Artillery: "Artilleri",
   ArtilleryFactory: "Artilleri fabrik",
   AshokaTheGreat: "Ashoka the Great",
   Ashurbanipal: "Ashurbanipal",
   Assembly: "Masse production",
   Astronomy: "Astronomi",
   AtomicBomb: "Atom bombe",
   AtomicFacility: "Atom facilitet",
   AtomicTheory: "Atomteori",
   Atomium: "Atomium",
   AtomiumDescV2: "All buildings that produce science within 2 tile range get +5 Production Multiplier. Generate science that is equal to the science production within 2 tile range. When completed, generate one-time science equivalent to the cost of the most expensive unlocked technology",
   Autocracy: "Autokrati",
   Aviation: "Luftfart",
   Babylonian: "Babylonian",
   BackToCity: "Tilbage til byen",
   BackupRecovery: "Backup Gendannelse",
   Bakery: "Bageri",
   Ballistics: "Ballistik",
   Bank: "Bankvirksomhed",
   Banking: "Bank",
   BankingAdditionalUpgrade: "Alle bygninger, der er niveau 10 eller højere, får +1 Opbevarings multiplikator",
   Banknote: "Sedler",
   BaseCapacity: "Base Capacity",
   BaseConsumption: "Basisforbrug",
   BaseMultiplier: "Basis multiplikator",
   BaseProduction: "Basis produktion",
   BastilleDay: "Bastille Day: Double the effect of Centre Pompidou and Arc de Triomphe. Double the Culture generation from Mont Saint-Michel",
   BatchModeTooltip: "%{count} bygninger er i øjeblikket valgt. Opgradering vil gælde for alle valgte bygninger",
   BatchSelectAllSameType: "Alle samme type",
   BatchSelectAnyType1Tile: "Enhver type i 1 område",
   BatchSelectAnyType2Tile: "Enhver type i 2 område",
   BatchSelectAnyType3Tile: "Enhver type i 3 område",
   BatchSelectSameType1Tile: "Samme type i 1 område",
   BatchSelectSameType2Tile: "Samme type i 2 område",
   BatchSelectSameType3Tile: "Samme type i 3 område",
   BatchSelectSameTypeSameLevel: "Same Type Same Level",
   BatchSelectThisBuilding: "Denne bygning",
   BatchStateSelectActive: "Active",
   BatchStateSelectAll: "All",
   BatchStateSelectTurnedFullStorage: "Full Storage",
   BatchStateSelectTurnedOff: "Turned Off",
   BatchUpgrade: "Batch opgradering",
   Battleship: "Slagskib",
   BattleshipBuilder: "Skibs Værft",
   BigBen: "Big Ben",
   BigBenDesc: "+2 Science From Busy Workers. Choose an empire ideology, unlock more boost with each choice",
   Biplane: "Flyvemaskine",
   BiplaneFactory: "Flyvemaskine Fabrik",
   Bitcoin: "Bitcoin",
   BitcoinMiner: "Bitcoin Miner",
   BlackForest: "Black Forest",
   BlackForestDesc: "When discovered, reveals all wood tiles on the map. Spawn wood on adjacent tiles. All buildings that consume Wood or Lumber get +5 Production Multiplier",
   Blacksmith: "Smed",
   Blockchain: "Blockchain",
   BlueMosque: "Blue Mosque",
   BlueMosqueDesc: "All wonders provide +1 Production, Worker Capacity and Storage Multiplier to adjacent buildings. When constructed next to Hagia Sophia, provide extra +1 Production, Worker Capacity and Storage Multiplier",
   BobHope: "Bob Hope",
   BobHopeDesc: "+%{value} Happiness",
   Bond: "Obligation",
   BondMarket: "Obligationsmarked",
   Book: "Bog",
   BoostCyclesLeft: "Boost Cycles Left",
   BoostDescription: "+%{value} %{multipliers} for %{buildings}",
   Borobudur: "Borobudur",
   BorobudurDesc: "Borobudur",
   BranCastle: "Bran Slot",
   BranCastleDesc: "Bran Slot",
   BrandenburgGate: "Brandenburger Tor",
   BrandenburgGateDesc: "Alle kulminer og oliebrønde får +1 Produktions-, Lager- og Arbejdskapacitetsmultiplikator. Olieraffinaderi får +1 Produktions-, Lager- og Arbejdskapacitetsmultiplikator for hver tilstødende olieområde",
   Bread: "Brød",
   Brewery: "Bryggeri",
   Brick: "Mursten",
   Brickworks: "Murværk",
   BritishMuseum: "British Museum",
   BritishMuseumChooseWonder: "Choose a Wonder",
   BritishMuseumDesc: "After constructed, can transform into to a unique wonder from other civilizations",
   BritishMuseumTransform: "Transform",
   Broadway: "Broadway",
   BroadwayCurrentlySelected: "Currently selected",
   BroadwayDesc: "A great person of the current age and a great person of the previous age are born. Select a great person and double his/her effect",
   BronzeAge: "Bronzealder",
   BronzeTech: "Bronze",
   BuddhismLevelX: "Buddhism %{level}",
   Build: "Byg",
   BuilderCapacity: "Byggerkapacitet",
   BuildingColor: "Bygningsfarve",
   BuildingColorMatchBuilding: "Kopier farve fra bygning",
   BuildingColorMatchBuildingTooltip: "Kopier ressourcefarven fra bygningen, der producerer denne ressource. Hvis flere bygninger producerer denne ressource, vælges en tilfældig",
   BuildingDefaults: "Bygningsstandarder",
   BuildingDefaultsCount: "%{count} egenskaber overskrives i bygningsstandarden",
   BuildingDefaultsRemove: "Ryd alle egenskabsoverræs",
   BuildingEmpireValue: "Bygningsimperiets værdi / Ressourceimperiets værdi",
   BuildingMultipliers: "Boost",
   BuildingName: "Navn",
   BuildingNoMultiplier: "%{building} påvirkes <b>ikke</b> af nogen multiplikatorer (produktion, arbejdskapacitet, lager osv.)",
   BuildingSearchText: "Indtast et bygnings- eller ressourcenavn for at søge",
   BuildingTier: "Tier",
   Cable: "Kabel",
   CableFactory: "Kabelfabrik",
   Calendar: "Kalender",
   CambridgeUniversity: "Cambridge University",
   CambridgeUniversityDesc: "+1 Age Wisdom level for Renaissance and ages after",
   CambridgeUniversitySource: "Cambridge University (%{age})",
   Cancel: "Annuller",
   CancelAllUpgradeDesc: "Cancel all %{building} upgrades",
   CancelUpgrade: "Annuller Opgradering",
   CancelUpgradeDesc: "Alle ressourcer, der allerede er transporteret, forbliver i lageret",
   Cannon: "Kanon",
   CannonWorkshop: "Kanonværksted",
   CannotEarnPermanentGreatPeopleDesc: "Da dette er et prøveløb, kan permanente store personer ikke optjenes",
   Capitalism: "Kapitalisme",
   Cappadocia: "Cappadocia",
   CappadociaDesc: "All buildings within 3 tile range get +1 Production, Worker Capacity and Storage Multiplier for every level above Level 30",
   Car: "Bil",
   Caravansary: "Karavanseraj",
   CaravansaryDesc: "Handel med andre spillere og tilbyd ekstra lagerplads",
   Caravel: "Karavel",
   CaravelBuilder: "Karavelværft",
   CarFactory: "Bilfabrik",
   CarlFriedrichGauss: "Carl Friedrich Gauss",
   CarlFriedrichGaussDesc: "+%{idle} Science from Idle Workers. +%{busy} Science from Busy Workers",
   CarlSagan: "Carl Sagan",
   Census: "Census",
   CentrePompidou: "Centre Pompidou",
   CentrePompidouDesc:
      "Once constructed, all buildings get +1 Production and +2 Storage Multiplier. The wonder will persist if the current run reaches Information Age and the next run is a different civilization. The wonder gets +1 level at rebirth for each run that reaches Information Age with a unique civilization. Each level provides +1 Production and +2 Storage Multiplier. The value of this wonder is excluded from total empire value and British Museum cannot transform into this wonder",
   CentrePompidouWarningHTML: "Centre Pompidou will disappear if you rebirth as <b>%{civ}</b>",
   CerneAbbasGiant: "Cerne Abbas Giant",
   CerneAbbasGiantDesc: "A great person of the current age is born when a wonder is constructed",
   ChangePlayerHandle: "Skift",
   ChangePlayerHandleCancel: "Annuller",
   ChangePlayerHandledDesc: "Dit spillernavn kan kun indeholde 5 ~ 16 bogstaver og tal, skal være unikt",
   Chariot: "Stridsvogn",
   ChariotWorkshop: "Stridsvognsværksted",
   Charlemagne: "Charlemagne",
   CharlesDarwin: "Charles Darwin",
   CharlesDarwinDesc: "+%{value} Videnskab fra beskæftigede arbejdere",
   CharlesMartinHall: "Charles Martin Hall",
   CharlesParsons: "Charles Parsons",
   CharlieChaplin: "Charlie Chaplin",
   CharlieChaplinDesc: "+%{value} Happiness",
   Chat: "Chat",
   ChatChannel: "Chat kanal",
   ChatChannelLanguage: "Sprog",
   ChatHideLatestMessage: "Skjul seneste beskedindhold",
   ChatNoMessage: "Ingen chatbeskeder",
   ChatReconnect: "Afbrydt, genforbinder...",
   ChatSend: "Send",
   CheckInAndExit: "Check In And Exit",
   CheckInCloudSave: "Check In Save",
   CheckOutCloudSave: "Check Out Save",
   Cheese: "Ost",
   CheeseMaker: "Mejeri",
   Chemistry: "Kemi",
   ChesterWNimitz: "Chester W. Nimitz",
   ChichenItza: "Chichen Itza",
   ChichenItzaDesc: "Alle tilstødende bygninger får +1 Produktionsmultiplikator, Lager- og Arbejdskapacitetsmultiplikator",
   Chinese: "Chinese",
   ChoghaZanbil: "Chogha Zanbil",
   ChoghaZanbilDescV2: "Choose an empire tradition, unlock more boost with each choice",
   ChooseGreatPersonChoicesLeft: "Du har %{count} valg tilbage",
   ChristianityLevelX: "Christianity %{level}",
   Church: "Kirke",
   CircusMaximus: "Cirkus Maximus",
   CircusMaximusDescV2: "+5 Lykke. Alle Musiker-, Forfatter- og Maler foreninger får +1 Produktions- og Lagermultiplikator",
   CityState: "Bystat",
   CityViewMap: "By",
   CivGPT: "CivGPT",
   CivIdle: "CivIdle",
   CivIdleInfo: "Stolt præsenteret af Fish Pond Studio",
   Civilization: "Civilization",
   CivilService: "Civilservice",
   CivOasis: "CivOasis",
   CivTok: "CivTok",
   ClaimedGreatPeople: "Valgte store personer",
   ClaimedGreatPeopleTooltip: "Du har %{total} store personer ved genfødslen, %{claimed} af dem er allerede valgt",
   ClassicalAge: "Klassisk tid",
   ClearAfterUpdate: "Ryd alle handler efter markedets opdatering",
   ClearSelected: "Ryd valgte",
   ClearSelection: "Ryd",
   ClearTransportPlanCache: "Clear Transport Plan Cache",
   Cleopatra: "Cleopatra",
   CloneFactory: "Clone Factory",
   CloneFactoryDesc: "Clone any resources",
   CloneFactoryInputDescHTML: "Clone Factory can only clone <b>%{res}</b> directly transported from <b>%{buildings}</b>",
   CloneLab: "Clone Lab",
   CloneLabDesc: "Convert any resources into Science",
   CloneLabScienceMultiplierHTML: "Production multipliers that <b>only apply to science production buildings</b> (e.g. production multipliers from Atomium) <b>do not apply</b> to Clone Lab",
   Cloth: "Tøj",
   CloudComputing: "Cloud komputering",
   CloudSaveRefresh: "Refresh",
   CloudSaveReturnToGame: "Return To Game",
   CNTower: "CN Tower",
   CNTowerDesc: "All movie studios, radio stations and TV stations are exempt from -1 Happiness. All buildings unlocked in World Wars and Cold War get +N Production, Worker Capacity and Storage Multiplier. N = Difference between the tier and the age of the building",
   Coal: "Kul",
   CoalMine: "Kulmine",
   CoalPowerPlant: "Kulkraftværk",
   Coin: "Mønt",
   CoinMint: "Mønttryk",
   ColdWarAge: "Den Kolde Krig",
   CologneCathedral: "Cologne Cathedral",
   CologneCathedralDesc:
      "When constructed, generate one-time science equivalent to the cost of the most expensive technology in the current age. All buildings that produce science (excluding Clone Lab) get +1 Production Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Production Multiplier to all buildings that produce science (excluding Clone Lab)",
   Colonialism: "Kolonialisme",
   Colosseum: "Colosseum",
   ColosseumDescV2: "Chariot Workshops are exempt from -1 happiness. Consumes 10 chariots and produce 10 happiness. Each unlocked age gives 2 extra happiness",
   ColossusOfRhodes: "Kolossen på Rhodos",
   ColossusOfRhodesDesc: "Alle tilstødende bygninger, der ikke producerer arbejdere, får +1 lykke",
   Combustion: "Forbrænding",
   Commerce4UpgradeHTMLV2: "When unlocked, all <b>adjacent banks</b> get free upgrade to <b>level 30</b>",
   CommerceLevelX: "Commerce %{level}",
   Communism: "Communism",
   CommunismLevel4DescHTML: "A great person of <b>Industrial Age</b> and a great person of <b>World Wars Age</b> are born",
   CommunismLevel5DescHTML: "A great person of <b>Cold War Age</b> is born. When entering a new age, get <b>2 additional</b> great people of that age",
   CommunismLevelX: "Communism Level %{level}",
   Computer: "komputer",
   ComputerFactory: "Computer Factory",
   ComputerLab: "Computer Lab",
   Concrete: "Beton",
   ConcretePlant: "Betonfabrik",
   Condo: "Condo",
   ConfirmDestroyResourceContent: "Du er ved at ødelægge %{amount} %{resource}. Dette kan ikke fortrydes",
   ConfirmNo: "Nej",
   ConfirmYes: "Ja",
   Confucius: "Konfucius",
   ConfuciusDescV2: "+%{value} Science from All Workers if more than 50% of workers are busy and less than 50% of busy workers work in transportation",
   ConnectToADevice: "Connect To A Device",
   Conservatism: "Conservatism",
   ConservatismLevelX: "Conservatism Level %{level}",
   Constitution: "Forfatning",
   Construction: "Konstruktion",
   ConstructionBuilderBaseCapacity: "Basis Kapacitet",
   ConstructionBuilderCapacity: "Bygger Kapacitet",
   ConstructionBuilderMultiplier: "Kapacitetsmultiplikator",
   ConstructionBuilderMultiplierFull: "Byggerkapacitetsmultiplikator",
   ConstructionCost: "Konstruktionsomkostninger: %{cost}",
   ConstructionDelivered: "Delivered",
   ConstructionPriority: "Bygningsprioritet",
   ConstructionProgress: "Fremgang",
   ConstructionResource: "Resource",
   Consume: "Forbrug",
   ConsumeResource: "Forbrug: %{resource}",
   ConsumptionMultiplier: "Forbrugsmultiplikator",
   ContentInDevelopment: "Indhold I Udvikling",
   ContentInDevelopmentDesc: "Dette spilindhold er stadig i udvikling og vil være tilgængeligt i en fremtidig spilopdatering, så hold øje!",
   Copper: "Kobber",
   CopperMiningCamp: "Kobbermine",
   CosimoDeMedici: "Cosimo de' Medici",
   Cotton: "Bomuld",
   CottonMill: "Bomuldsmølle",
   CottonPlantation: "Bomuldsplantage",
   Counting: "Tælling",
   Courthouse: "Domhus",
   CristoRedentor: "Cristo Redentor",
   CristoRedentorDesc: "Cristo Redentor",
   CrossPlatformAccount: "Platform Account",
   CrossPlatformConnect: "Connect",
   CrossPlatformSave: "Cross Platform Save",
   CrossPlatformSaveLastCheckIn: "Last Check In",
   CrossPlatformSaveStatus: "Current Status",
   CrossPlatformSaveStatusCheckedIn: "Checked In",
   CrossPlatformSaveStatusCheckedOut: "Checked Out on %{platform}",
   CrossPlatformSaveStatusCheckedOutTooltip: "Your cross platform save has been checked out on another platform, you have to check in on that platform before you can check out on this platform",
   Cultivation4UpgradeHTML: "A great person of <b>Renaissance Age</b> is born",
   CultivationLevelX: "Cultivation %{level}",
   Culture: "Kultur",
   Culus: "Cülus: Double the effect of Cappadocia. Mount Ararat's effect becomes based on square root of Effective Great People Level, instead of cubic root",
   CurrentLanguage: "Dansk",
   CurrentPlatform: "Current Platform",
   CursorBigOldFashioned: "3D (Big)",
   CursorOldFashioned: "3D",
   CursorStyle: "Cursor Style",
   CursorStyleDescHTML: "Change the style of the cursor. <b>Require restarting your game to take effect</b>",
   CursorSystem: "System",
   Cycle: "Cycle",
   CyrusII: "Kong Kyros II",
   DairyFarm: "Dairy Farm",
   DefaultBuildingLevel: "Standardbygningsniveau",
   DefaultConstructionPriority: "Standard Bygningsprioritet",
   DefaultProductionPriority: "Standard Produktionsprioritet",
   DefaultStockpileMax: "Standard Max Lagerbeholdning",
   DefaultStockpileSettings: "Standardindgangskapacitet til lager",
   DeficitResources: "Deficit Resources",
   Democracy: "Demokrati",
   DemolishAllBuilding: "Demolish All %{building} Within %{tile} Tile",
   DemolishAllBuildingConfirmContent: "Are you sure about demolishing %{count} %{name}?",
   DemolishAllBuildingConfirmTitle: "Demolish %{count} Building(s)?",
   DemolishBuilding: "Ødelæg Bygning",
   DennisRitchie: "Dennis Ritchie",
   Deposit: "Indskud",
   DepositTileCountDesc: "%{count} område(r) af %{deposit} kan findes i %{city}",
   Dido: "Dido",
   Diplomacy: "Diplomati",
   DistanceInfinity: "Ubegrænset",
   DistanceInTiles: "Afstand (I områder)",
   DolmabahcePalace: "Dolmabahçe Palace",
   Drilling: "Boring",
   DukeOfZhou: "Hertug af Zhou",
   DuneOfPilat: "Dune of Pilat",
   DuneOfPilatDesc: "In each age, double the age wisdom for the previous age",
   DynamicMultiplierTooltip: "This multiplier is dynamic - it will not affect workers and storage",
   Dynamite: "Dynamit",
   DynamiteWorkshop: "Dynamitværksted",
   DysonSphere: "Dyson Sphere",
   DysonSphereDesc: "All buildings get +5 Production Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Production Multiplier to all buildings",
   EasterBunny: "Easter Bunny",
   EasterBunnyDesc: "Once constructed, 10% of the extra Great People at Rebirth from this run will carry forward to the next run and are born after building the Easter Bunny in the new run. This wonder can only be constructed during April",
   EastIndiaCompany: "East India Company",
   EastIndiaCompanyDescV2:
      "This wonder accumulates the total value of your completed player trade transactions. For every 2,000 trade value, all buildings adjacent to caravansaries get a +0.5 Production Multiplier for 1 cycle. This wonder can be upgraded and each upgrade provides an additional +0.5 Production Multiplier. A trade transaction counts when you either fulfill another player's trade request or when your own trade request is fulfilled. Multiple boosts stack by extending the duration",
   Education: "Uddannelse",
   EffectiveGreatPeopleLevel: "Effective Great People Level",
   EffectiveGreatPeopleLevelDesc: "Effective great people level is the sum of all permanent great people level and age wisdom level. It measures the effect boost provided by great people and age wisdom",
   Egyptian: "Egyptian",
   EiffelTower: "Eiffeltårnet",
   EiffelTowerDesc: "Alle tilstødende stålværker får +N Produktions-, Lager- og Arbejdermultiplikator. N = Antal tilstødende stålværker",
   Elbphilharmonie: "Elbphilharmonie",
   ElbphilharmonieDesc: "All buildings within 3 tile range get +1 Production Multiplier for each adjacent working building that has different tier",
   Electricity: "Elektricitet",
   Electrification: "Elektrificering",
   ElectrificationPowerRequired: "Krævet strøm",
   ElectrificationStatusActive: "Aktiv",
   ElectrificationStatusDesc: "Both buildings that require power and buildings that do not require power can be electrified. However, buildings that require power provides higher electrification efficiency",
   ElectrificationStatusNoPowerV2: "Not Enough Power",
   ElectrificationStatusNotActive: "Ikke Aktiv",
   ElectrificationStatusV2: "Electrification Status",
   ElectrificationUpgrade: "Lås op for elektrificering. Tillad bygninger at forbruge strøm for at øge produktionen",
   Electrolysis: "Elektrolyse",
   ElvisPresley: "Elvis Presley",
   ElyseePalace: "Élysée Palace",
   EmailDeveloper: "E-mail udvikleren",
   Embassy: "Ambassade",
   EmperorWuOfHan: "Kejser Wu af Han",
   EmpireValue: "Imperieværdi",
   EmpireValueByHour: "Empire Value By Hour",
   EmpireValueFromBuilding: "Imperieværdi fra Bygning",
   EmpireValueFromBuildingsStat: "Fra Bygninger",
   EmpireValueFromResources: "Fra Ressourcer",
   EmpireValueFromResourcesStat: "Fra Ressourcer",
   EmpireValueIncrease: "Empire Value Increase",
   EmptyTilePageBuildLastBuilding: "Byg Sidste Bygning",
   EndConstruction: "Afslut Konstruktion",
   EndConstructionDescHTML: "Når du afslutter konstruktionen, vil alle ressourcer, der allerede er blevet brugt, <b>ikke blive returneret</b>",
   Engine: "Motor",
   Engineering: "Ingeniørarbejde",
   English: "English",
   Enlightenment: "Oplysningstid",
   Enrichment: "Berigelse",
   EnricoFermi: "Enrico Fermi",
   EstimatedTimeLeft: "Estimeret tid tilbage",
   EuphratesRiver: "Euphrates River",
   EuphratesRiverDesc:
      "Every 10% of busy workers that in production (not transporting) provides +1 Production Multiplier to all buildings that do not produce workers (max = number of unlocked ages / 2). When the Hanging Garden is built next to it, the Hanging Garden gets +1 effect for each age after the Hanging Garden is unlocked. When discovered, spawn water on all adjacent tiles that do not have deposits",
   ExpansionLevelX: "Expansion %{level}",
   Exploration: "Ekspeditioner",
   Explorer: "Opdagelsesrejsende",
   ExplorerRangeUpgradeDesc: "Increase the explorer's range to %{range}",
   ExploreThisTile: "Send en Opdagelsesrejsende",
   ExploreThisTileHTML: "En Opdagelsesrejsende vil udforske <b>denne område og dens tilstødende områder</b>. Opdagelsesrejsende genereres i %{name}. Du har %{count} Opdagelsesrejsende tilbage",
   ExtraGreatPeople: "%{count} Ekstra Store Personer",
   ExtraGreatPeopleAtReborn: "Ekstra Store Personer Ved Genfødsel",
   ExtraTileInfoType: "Extra Tile Info",
   ExtraTileInfoTypeDesc: "Choose what information is shown below each tile",
   ExtraTileInfoTypeEmpireValue: "Empire Value",
   ExtraTileInfoTypeNone: "None",
   ExtraTileInfoTypeStoragePercentage: "Storage Percentage",
   Faith: "Tro",
   Farming: "Landbrug",
   FavoriteBuildingAdd: "Tilføj til Favorit",
   FavoriteBuildingEmptyToast: "Du har ingen favoritbygninger",
   FavoriteBuildingRemove: "Fjern fra Favorit",
   FeatureRequireQuaestorOrAbove: "This feature requires Quaestor rank or above",
   Festival: "Festival",
   FestivalCycle: "Festival Cycle",
   FestivalTechTooltipV2: "Positive Happiness (max. 50) is converted into festival points. For every %{point} festival points, your empire enters a festival cycle, granting a significant map-specific boost. The festival on this map is %{desc}",
   FestivalTechV2: "Unlock festival - positive Happiness (max. 50) is converted into festival points. For every %{point} festival points, your empire enters a festival cycle, granting a significant map-specific boost",
   Feudalism: "Føydalisme",
   Fibonacci: "Fibonacci",
   FibonacciDescV2: "+%{idle} Videnskab fra inaktive arbejdere. +%{busy} Videnskab fra beskæftigede arbejdere. Fibonaccis permanente opgraderingsomkostninger følger Fibonaccis sekvens",
   FighterJet: "Fighter Jet",
   FighterJetPlant: "Fighter Jet Plant",
   FilterByAge: "Filter by Age",
   FinancialArbitrage: "Financial Arbitrage",
   FinancialLeverage: "Financial Leverage",
   Fire: "Ild",
   Firearm: "Skydevåben",
   FirstTimeGuideNext: "Næste",
   FirstTimeTutorialWelcome: "Welcome to CivIdle",
   FirstTimeTutorialWelcome1HTML:
      "Welcome to CivIdle. In this game, you will run your own empire: <b>manage productions, unlock technologies, trade resources with other players, create great people and build world wonders</b>.<br><br>Drag your mouse to move around. Use the scroll wheel to zoom in or out. Click an empty tile to build new buildings, click a building to inspect it.<br><br>Certain buildings like Stone Quarry and Logging Camp need to be built on top of the resource tile. I recommend placing a Hut, which provides worker, next to the fog - the building will take some time to build. After the completion, it will reveal the fog nearby.",
   FirstTimeTutorialWelcome2HTML:
      "Buildings can be upgraded - it costs resources and takes time. When a buildings is being upgraded, <b>it will no longer produce</b>. This includes buildings that provide workers, <b>so never upgrade all your buildings at the same time!</b><br><br>As your empire grows, you will get more science and unlock new technologies. I will tell you more about it when we get there but you can go to View -> Research to take a quick look<br><br>",
   FirstTimeTutorialWelcome3HTML: "Now you know all the basics of the game, you can start building your empire. But before I let you go, you should <b>choose yourself a player handle</b> and say hi in the in-game chat. We have an amazingly helpful community: if you get lost, don't be afraid to ask!",
   Fish: "Fisk",
   FishPond: "Fiske Dam",
   FlorenceNightingale: "Florence Nightingale",
   FlorenceNightingaleDesc: "+%{value} Lykke",
   Flour: "Mel",
   FlourMill: "Mølle",
   FontSizeScale: "Skriftstørrelse Skala",
   FontSizeScaleDescHTML: "Skift skriftstørrelsesskalaen for spillets brugergrænseflade. <b>Indstilling af skalaen til mere end 1x kan ødelægge nogle brugergrænseflade-layouts</b>",
   ForbiddenCity: "Den Forbudte By",
   ForbiddenCityDesc: "Alle papirfabrikker, skriverforening og trykkerier får +1 Produktion Multiplikator, Arbejderkapacitetsmultiplikator og Lagerkapacitetsmultiplikator",
   Forex: "Forex",
   ForexMarket: "Forex Market",
   FrankLloydWright: "Frank Lloyd Wright",
   FrankLloydWrightDesc: "+%{value} Builder Capacity Multiplier",
   FrankWhittle: "Frank Whittle",
   FreeThisWeek: "Free This Week",
   FreeThisWeekDescHTMLV2: "<b>Every week</b>, one of the premium civilizations is free to play. This week's free civilization is <b>%{city}</b>",
   French: "French",
   Frigate: "Fregat",
   FrigateBuilder: "Fregat Værft",
   Furniture: "Møbler",
   FurnitureWorkshop: "Møbelsnedkeri",
   Future: "Future",
   GabrielGarciaMarquez: "Gabriel García Márquez",
   GabrielGarciaMarquezDesc: "+%{value} Happiness",
   GalileoGalilei: "Galileo Galilei",
   GalileoGalileiDesc: "+%{value} Videnskab Fra Inaktive Arbejdere",
   Galleon: "Galeon",
   GalleonBuilder: "Galeon Værft",
   Gameplay: "Gameplay",
   Garment: "Beklædning",
   GarmentWorkshop: "Beklædningsværksted",
   GasPipeline: "Gasrørledning",
   GasPowerPlant: "Gaskraftværk",
   GatlingGun: "Gatling Pistol",
   GatlingGunFactory: "Gatling Pistol Arsenal",
   Genetics: "Genetik",
   Geography: "Geografi",
   GeorgeCMarshall: "George C. Marshall",
   GeorgeWashington: "George Washington",
   GeorgiusAgricola: "Georgius Agricola",
   German: "German",
   Glass: "Glas",
   Glassworks: "Glasværk",
   GlobalBuildingDefault: "Global Bygning Standard",
   Globalization: "Globalisering",
   GoBack: "Gå Tilbage",
   Gold: "Guld",
   GoldenGateBridge: "Golden Gate Bro",
   GoldenGateBridgeDesc: "Golden Gate Broen",
   GoldenPavilion: "Golden Pavilion",
   GoldenPavilionDesc: "All buildings within 3 tile range get +1 Production Multiplier for each adjacent building that produces any of its consumed resources (excluding Clone Lab and Clone Factory and the building cannot be turned off)",
   GoldMiningCamp: "Guldmine",
   GordonMoore: "Gordon Moore",
   GrandBazaar: "Stor Basar",
   GrandBazaarDesc: "Kontroller alle markeder et sted!. Alle tilstødende bygninger får +5 Lagerkapacitetsmultiplikator",
   GrandBazaarFilters: "Filtre",
   GrandBazaarFilterWarningHTML: "Du skal vælge et filter, før nogen markedshandler vises",
   GrandBazaarFilterYouGet: "Du Får",
   GrandBazaarFilterYouPay: "Du Betaler",
   GrandBazaarSeach: "Search",
   GrandBazaarSearchGet: "Get",
   GrandBazaarSearchPay: "Pay",
   GrandBazaarTabActive: "Aktiv",
   GrandBazaarTabTrades: "Handler",
   GrandCanyon: "Grand Canyon",
   GrandCanyonDesc: "Buildings unlocked in the current age get +2 Production Multiplier. Double the effect of J.P. Morgan",
   GraphicsDriver: "Grafikkort: %{driver}",
   GreatDagonPagoda: "Store Dagon Pagode",
   GreatDagonPagodaDescV2: "All pagodas are exempt from -1 happiness. Generate science based on faith production of all pagodas",
   GreatMosqueOfSamarra: "Store Moské i Samarra",
   GreatMosqueOfSamarraDescV2: "+1 bygningssynsrækkevidde. Afslør 5 tilfældige uudforskede råmatrialer og byg en niveau 10 ressourceudvindingsbygning på hver",
   GreatPeople: "Store Personer",
   GreatPeopleEffect: "Effekt",
   GreatPeopleFilter: "Type name or age to filter great people",
   GreatPeopleName: "Navn",
   GreatPeoplePermanentColumn: "Permanent",
   GreatPeoplePermanentShort: "Permanent",
   GreatPeoplePickPerRoll: "Great People Pick Per Roll",
   GreatPeopleThisRun: "Store Personer Fra Dette Spil",
   GreatPeopleThisRunColumn: "Dette Spil",
   GreatPeopleThisRunShort: "Dette Spil",
   GreatPersonLevelRequired: "Nødvendige Permanent Store Personer Niveau",
   GreatPersonLevelRequiredDescV2: "%{city} civilization requires %{required} permanent great people levels. You currently have %{current}",
   GreatPersonPromotionPromote: "Promote",
   GreatPersonThisRunEffectiveLevel: "Du har i øjeblikket %{count} %{person} fra dette spil. En ekstra %{person} vil have 1/%{effect} af effekten",
   GreatPersonWildCardBirth: "Fødsel",
   GreatSphinx: "Store Sphinx",
   GreatSphinxDesc: "Alle bygninger af tier II eller derover inden for 2 områder får +N Forbrug, Produktionsmultiplikator. N = Antal af dens tilstødende bygninger af samme type",
   GreatWall: "Den Kinesiske Mur",
   GreatWallDesc:
      "Alle bygninger inden for 1 områdeområde får +N Produktion, Arbejderkapacitet og Lagerkapacitetsmultiplikator. N = antallet af de forskellige tidsaldre mellem den aktuelle alder og alderen, hvor bygningen først låses op. Når den er konstrueret ved siden af Den Forbudte By, øges rækkevidden til 2 områder",
   GreedyTransport: "Construction/Upgrade Greedy Transport",
   GreedyTransportDescHTML: "This will make buildings keep transporting resources even if it has enough resources for the current upgrade, which can make upgrading multiple levels <b>faster</b> but end up transport <b>more resources than needed</b>",
   Greek: "Greek",
   GrottaAzzurra: "Grotta Azzurra",
   GrottaAzzurraDescV2: "When discovered, all your Tier I buildings get +5 Level and +1 Production, Worker Capacity and Storage Multiplier",
   Gunpowder: "Krudt",
   GunpowderMill: "Krudtmølle",
   GuyFawkesNightV2: "Guy Fawkes Night: East India Company provides double the Production Multiplier to buildings adjacent to caravansaries. Tower Bridge generates great people 20% faster",
   HagiaSophia: "Hagia Sophia",
   HagiaSophiaDescV2: "+5 Happiness. Buildings with 0% Production Capacity are exempt from -1 happiness. During the game bootstrap, provide extra happiness to avoid production halt",
   HallOfFame: "Hall of Fame",
   HallOfSupremeHarmony: "Hall of Supreme Harmony",
   Hammurabi: "Hammurabi",
   HangingGarden: "Babylons hængende haver",
   HangingGardenDesc: "+1 Byggerkapacitetsmultiplikator. Tilstødende akvædukter får +1 Produktion, Lager og Arbejderkapacitetsmultiplikator",
   Happiness: "Lykke",
   HappinessFromBuilding: "Fra Bygninger (ekskl. Vidundere)",
   HappinessFromBuildingTypes: "Fra Velassorterede Bygningstyper",
   HappinessFromHighestTierBuilding: "Fra Højeste Tier Arbejdsbygning",
   HappinessFromUnlockedAge: "Fra Ulåst Alder",
   HappinessFromUnlockedTech: "Fra Ulåst Teknologi",
   HappinessFromWonders: "Fra Vidunere (inkl. Naturlige)",
   HappinessUncapped: "Lykke (Ukappet)",
   HarryMarkowitz: "Harry Markowitz",
   HarunAlRashid: "Harun al-Rashid",
   Hatshepsut: "Hatshepsut",
   HatshepsutTemple: "Hatschepsut-templet",
   HatshepsutTempleDesc: "Afslør alle vandområder på kortet, når det er færdigt. Hvedefarme får +1 Produktionsmultiplikator for hver tilstødende vandområde",
   Headquarter: "Hovedkvarter",
   HedgeFund: "Hedge Fund",
   HelpMenu: "Hjælp",
   HenryFord: "Henry Ford",
   Herding: "Hyrdning",
   Herodotus: "Herodotus",
   HighlightBuilding: "Highlight %{building}",
   HimejiCastle: "Himeji-borgen",
   HimejiCastleDesc: "Alle Caravel Værfter, Galeon Værfter og Fregat Værfter får +1 Produktionsmultiplikator, Arbejderkapacitetsmultiplikator og Lagerkapacitetsmultiplikator",
   Hollywood: "Hollywood",
   HollywoodDesc: "+5 Lykke. +1 Lykke for hver velassorteret bygning, der forbruger eller producerer kultur inden for 2 områdeområde",
   HolyEmpire: "Det Hellige Rige",
   Homer: "Homer",
   Honor4UpgradeHTML: "Double the effect of <b>Zheng He</b> (Great Person)",
   HonorLevelX: "Honor %{level}",
   Horse: "Hest",
   HorsebackRiding: "Ridning",
   House: "Hus",
   Housing: "Boliger",
   Hut: "Hytte",
   HydroDam: "Dæmning",
   Hydroelectricity: "Vandkraft",
   HymanGRickover: "Hyman G. Rickover",
   IdeologyDescHTML: "Choose from <b>Liberalism, Conservatism, Socialism or Communism</b> as your empire ideology. You <b>cannot switch ideology</b> after it is chosen. You can unlock more boost within each ideology",
   IMPei: "I. M. Pei",
   IMPeiDesc: "+%{value} Builder Capacity Multiplier",
   Imperialism: "Imperialisme",
   ImperialPalace: "Imperial Palace",
   IndustrialAge: "Den industrielle revolution",
   InformationAge: "Informationsalder",
   InputResourceForCloning: "Input Resource For Cloning",
   InternationalSpaceStation: "International Space Station",
   InternationalSpaceStationDesc: "All buildings get +5 Storage Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Storage Multiplier to all buildings",
   Internet: "Internet",
   InternetServiceProvider: "Internet Service Provider",
   InverseSelection: "Omvendt Valg",
   Iron: "Jern",
   IronAge: "Jernalder",
   Ironclad: "Panserskib",
   IroncladBuilder: "Panserskib Værft",
   IronForge: "Jern Støberi",
   IronMiningCamp: "Jernmine",
   IronTech: "Jern",
   IsaacNewton: "Isaac Newton",
   IsaacNewtonDescV2: "+%{value} Science from All Workers if more than 50% of workers are busy and less than 50% of busy workers work in transportation",
   IsambardKingdomBrunel: "Isambard Kingdom Brunel",
   IsidoreOfMiletus: "Isidore af Milet",
   IsidoreOfMiletusDesc: "+%{value} Byggerkapacitetsmultiplikator",
   Islam5UpgradeHTML: "When unlocked, generate one-time science equivalent to the cost of the most expensive <b>Industrial</b> technology",
   IslamLevelX: "Islam %{level}",
   ItsukushimaShrine: "Itsukushima-helligdommen",
   ItsukushimaShrineDescV2: "When all technologies within an age are unlocked, generate one-time science equivalent to the cost of the cheapest technology in the next age",
   JamesWatson: "James Watson",
   JamesWatsonDesc: "+%{value} Science From Busy Workers",
   JamesWatt: "James Watt",
   Japanese: "Japanese",
   JetPropulsion: "Jet Propulsion",
   JohannesGutenberg: "Johannes Gutenberg",
   JohannesKepler: "Johannes Kepler",
   JohnCarmack: "John Carmack",
   JohnDRockefeller: "John D. Rockefeller",
   JohnMcCarthy: "John McCarthy",
   JohnVonNeumann: "John von Neumann",
   JohnVonNeumannDesc: "+%{value} Science From Busy Workers",
   JoinDiscord: "Deltag i Discord",
   JosephPulitzer: "Joseph Pulitzer",
   Journalism: "Journalistik",
   JPMorgan: "J.P. Morgan",
   JRobertOppenheimer: "J. Robert Oppenheimer",
   JuliusCaesar: "Julius Caesar",
   Justinian: "Justinian",
   Kanagawa: "Kanagawa",
   KanagawaDesc: "All great people of the current age get an additional level for this run (excluding Zenobia)",
   KarlMarx: "Karl Marx",
   Knight: "Ridder",
   KnightCamp: "Ridderlejr",
   Koti: "Koti",
   KotiInStorage: "Koti In Storage",
   KotiProduction: "Koti Production",
   LandTrade: "Landhandel",
   Language: "Sprog",
   Lapland: "Lapland",
   LaplandDesc: "When discovered, reveal the whole map. All buildings within 2-tile range get +5 Production Multiplier. This natural wonder can only be discovered in December",
   LargeHadronCollider: "Large Hadron Collider",
   LargeHadronColliderDescV2: "All Information Age great people get +2 level for this run. This wonder can be upgraded and each additional upgrade provides +1 level to all Information Age great people for this run",
   Law: "Lov",
   Lens: "Lænse",
   LensWorkshop: "Lænseværksted",
   LeonardoDaVinci: "Leonardo da Vinci",
   Level: "Niveau",
   LevelX: "Niveau %{level}",
   Liberalism: "Liberalism",
   LiberalismLevel3DescHTML: "Free transport <b>from</b> and <b>to</b> warehouses",
   LiberalismLevel5DescHTML: "<b>Double</b> the electrification effect",
   LiberalismLevelX: "Liberalism Level %{level}",
   Library: "Bibliotek",
   LighthouseOfAlexandria: "Alexandrias Fyrtårn",
   LighthouseOfAlexandriaDesc: "Alle tilstødende bygninger får +5 Lagerkapacitetsmultiplikator",
   LinusPauling: "Linus Pauling",
   LinusPaulingDesc: "+%{value} Science From Idle Workers",
   Literature: "Litteratur",
   LiveData: "Live Value",
   LocomotiveFactory: "Togfabrik",
   Logging: "Skovbrug",
   LoggingCamp: "Skovhugger",
   LouisSullivan: "Louis Sullivan",
   LouisSullivanDesc: "+%{value} Byggerkapacitetsmultiplikator",
   Louvre: "Louvre",
   LouvreDesc: "For every 10 Extra Great People at Rebirth, one great person from all unlocked ages is born",
   Lumber: "Tømmer",
   LumberMill: "Savværk",
   LunarNewYear: "Lunar New Year: Great Wall provides double the boost to buildings. Porcelain Tower provides +1 level to all great people from this run",
   LuxorTemple: "Luxor Templet",
   LuxorTempleDescV2: "+1 Science From Busy Workers. Choose an empire religion, unlock more boost with each choice",
   Machinery: "Maskineri",
   Magazine: "Magasin",
   MagazinePublisher: "Magasin Forlag",
   Maglev: "Maglev",
   MaglevFactory: "Maglev Factory",
   MahatmaGandhi: "Mahatma Gandhi",
   ManageAgeWisdom: "Manage Age Wisdom",
   ManagedImport: "Administreret Import",
   ManagedImportDescV2: "Denne bygning importerer automatisk ressourcer, der er produceret inden for %{range} områdeområde. Ressourcetransporter til denne bygning kan ikke ændres manuelt. Maksimal transportafstand vil blive ignoreret",
   ManageGreatPeople: "Administrer Store Personer",
   ManagePermanentGreatPeople: "Administrer Permanente Store Personer",
   ManageSave: "Manage Save",
   ManageWonders: "Administrer Vidundere",
   Manhattan: "Manhattan",
   ManhattanProject: "Manhattan Projektet",
   ManhattanProjectDesc: "Manhattan Projektet",
   Marble: "Marmor",
   Marbleworks: "Marmormine",
   MarcoPolo: "Marco Polo",
   MarieCurie: "Marie Curie",
   MarinaBaySands: "Marina Bay Sands",
   MarinaBaySandsDesc: "All buildings get +5 Worker Capacity Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Worker Capacity Multiplier to all buildings",
   Market: "Marked",
   MarketDesc: "Byt en ressource til en anden, tilgængelige ressourcer opdateres hver time",
   MarketRefreshMessage: "Trades in %{count} markets has been refreshed",
   MarketSell: "Sælg",
   MarketSettings: "Markedsindstillinger",
   MarketValueDesc: "%{value} sammenlignet med gennemsnitsprisen",
   MarketYouGet: "Du Får",
   MarketYouPay: "Du Betaler",
   MartinLuther: "Martin Luther",
   MaryamMirzakhani: "Maryam Mirzakhani",
   MaryamMirzakhaniDesc: "+%{value} Science From Idle Workers",
   Masonry: "Murerarbejde",
   MatrioshkaBrain: "Matrioshka Brain",
   MatrioshkaBrainDescV2: "Allow Science to be counted when calculating empire value (5 Science = 1 Empire Value). +5 Science Per Busy and Idle Worker. This wonder can be upgraded and each additional upgrade provides +1 Science Per Busy and Idle Worker and +1 Production Multiplier for buildings that produce Science",
   MausoleumAtHalicarnassus: "Mausoleum i Halikarnassos",
   MausoleumAtHalicarnassusDescV2: "Transports from or to buildings within 2 tile range do not cost workers",
   MaxExplorers: "Maksimum Opdagelsesrejsende",
   MaxTransportDistance: "Maksimum Transportafstand",
   Meat: "Kød",
   Metallurgy: "Metalurgi",
   Michelangelo: "Michelangelo",
   MiddleAge: "Middelalder",
   MilitaryTactics: "Military Tactics",
   Milk: "Mælk",
   Moai: "Moai",
   MoaiDesc: "Moai",
   MobileOverride: "Mobile Override",
   MogaoCaves: "Mogao Hulerne",
   MogaoCavesDescV3: "+1 happiness for every 10% of busy workers. All adjacent buildings that produce faith are exempt from -1 happiness",
   MonetarySystem: "Monetary System",
   MontSaintMichel: "Mont Saint-Michel",
   MontSaintMichelDesc: "Generate Culture from Idle Workers. Provide +1 Storage Multiplier to all buildings within 2-tile range. This wonder can be upgraded using the generated Culture and each level provides addtional +1 Storage Multiplier",
   Mosque: "Moske",
   MotionPicture: "Video",
   MountArarat: "Mount Ararat",
   MountAraratDesc: "All buildings within 2 tile range get +X Production, Worker Capacity and Storage Multiplier. X = cubic root of Effective Great People Level",
   MountFuji: "Mount Fuji",
   MountFujiDescV2: "When Petra is built next to it, Petra gets +8h Warp storage. When the game is running, generate 20 warp every minute in Petra (not accelerated by Petra itself, not generating when the game is offline)",
   MountSinai: "Mount Sinai",
   MountSinaiDesc: "Når det opdages, fødes en stor person i den aktuelle alder. Alle bygninger, der producerer tro, får +5 Lagerkapacitetsmultiplikator",
   MountTai: "Mount Tai",
   MountTaiDesc: "Alle bygninger, der producerer videnskab, får +1 Produktion Multiplikator. Fordobl effekten af Konfucius (Stor Person). Når det opdages, generer engangvidenskab svarende til omkostningerne ved den dyreste låste teknologi",
   MoveBuilding: "Move Building",
   MoveBuildingFail: "Selected tile is not valid",
   MoveBuildingNoTeleport: "You don't have enough teleport",
   MoveBuildingSelectTile: "Select An Tile...",
   MoveBuildingSelectTileToastHTML: "Select <b>an empty explored tile</b> on the map as the target",
   Movie: "Film",
   MovieStudio: "Film studie",
   Museum: "Museum",
   Music: "Musik",
   MusiciansGuild: "Musikeres forening",
   MutualAssuredDestruction: "Mutual Assured Destruction",
   MutualFund: "Mutual Fund",
   Name: "Navn",
   Nanotechnology: "Nanoteknologi",
   NapoleonBonaparte: "Napoleon Bonaparte",
   NaturalGas: "Naturgas",
   NaturalGasWell: "Naturgasbrønd",
   NaturalWonderName: "Naturvidunder: %{name}",
   NaturalWonders: "Naturvidundere",
   Navigation: "Navigation",
   NebuchadnezzarII: "Nebukadnezar II",
   Neuschwanstein: "Neuschwanstein",
   NeuschwansteinDesc: "+10 Byggerkapacitetsmultiplikator, når du bygger vidundere",
   Newspaper: "Avis",
   NextExplorersIn: "Næste Opdagelsesrejsende Om",
   NextMarketUpdateIn: "Næste Markedsopdatering Om",
   NiagaraFalls: "Niagara Falls",
   NiagaraFallsDescV2: "All warehouses, markets and caravansaries get +N storage multiplier. N = number of unlocked ages. Albert Einstein provides +1 Production Multiplier to Research Fund (not affected by other boosts like Broadway)",
   NielsBohr: "Niels Bohr",
   NielsBohrDescV2: "+%{value} Science from All Workers if more than 50% of workers are busy and less than 50% of busy workers work in transportation",
   NileRiver: "Nilen",
   NileRiverDesc: "Fordobbel effekten af Hatshepsut. Alle hvedefarme får +1 Produktion og Lagerkapacitetsmultiplikator. Alle tilstødende hvedefarme får +5 Produktion og Lagerkapacitetsmultiplikator",
   NoPowerRequired: "This building does not require power",
   NothingHere: "Ingenting her",
   NotProducingBuildings: "Bygninger, der ikke producerer",
   NuclearFission: "Nuklear fission",
   NuclearFuelRod: "Atombrændselsstang",
   NuclearMissile: "Nuclear Missile",
   NuclearMissileSilo: "Nuclear Missile Silo",
   NuclearPowerPlant: "Nuclear Power Plant",
   NuclearReactor: "Nuclear Reactor",
   NuclearSubmarine: "Nuclear Submarine",
   NuclearSubmarineYard: "Nuclear Submarine Yard",
   OdaNobunaga: "Oda Nobunaga",
   OfflineErrorMessage: "Du er i øjeblikket offline. Denne operation kræver en internetforbindelse",
   OfflineProduction: "Offline Produktion",
   OfflineProductionTime: "Offline Production Time",
   OfflineProductionTimeDescHTML: "For the <b>first %{time} offline time</b>, you can choose either offline production or time warp - you can set the split here. The <b>rest of the offline time</b> can only be converted to time warp",
   OfflineTime: "Offline Tid",
   Oil: "Olie",
   OilPress: "Oliepresse",
   OilRefinery: "Olieraffinaderi",
   OilWell: "Oliebrønd",
   Ok: "OK",
   Oktoberfest: "Oktoberfest: Double the effect of Zugspitze",
   Olive: "Oliven",
   OlivePlantation: "Olivenplantage",
   Olympics: "De Olympiske Lege",
   OnlyAvailableWhenPlaying: "Only available when playing %{city}",
   OpenLogFolder: "Åbn Logmappe",
   OpenSaveBackupFolder: "Åbn Backupmappe",
   OpenSaveFolder: "Åbn Gem Mappe",
   Opera: "Opera",
   OperationNotAllowedError: "Denne operation er ikke tilladt",
   Opet: "Opet: Great Sphinx no longer increases Consumption Multiplier",
   OpticalFiber: "OpticalFiber",
   OpticalFiberPlant: "Optical Fiber Factory",
   Optics: "Optik",
   OptionsMenu: "Indstillinger",
   OptionsUseModernUIV2: "Brug Anti-aliaset Skrifttype",
   OsakaCastle: "Osaka Castle",
   OsakaCastleDesc: "Provide power to all tiles within 2 tile range. Allow electrification of science producing buildings (including Clone Lab)",
   OtherPlatform: "Other Platform",
   Ottoman: "Ottoman",
   OttoVonBismarck: "Otto von Bismarck",
   OxfordUniversity: "Oxford Universitet",
   OxfordUniversityDescV3: "+10% videnskabsproduktion for bygninger, der producerer videnskab. Når det er færdigt, generer engangvidenskab svarende til omkostningerne ved den dyreste låste teknologi",
   PabloPicasso: "Pablo Picasso",
   Pagoda: "Pagoda",
   PaintersGuild: "Maler forening",
   Painting: "Maleri",
   PalmJumeirah: "Palm Jumeirah",
   PalmJumeirahDesc: "+10 Builder Capacity. This wonder can be upgraded and each additional upgrade provides +2 Builder Capacity",
   Pamukkale: "Pamukkale",
   PamukkaleDesc: "When discovered, convert each one of the Permanent Great People Shards (except for Promotion and Wildcard) to the same Great Person From This Run",
   Panathenaea: "Panathenaea: Poseidon provides +1 Production Multiplier to all buildings",
   Pantheon: "Pantheon",
   PantheonDescV2: "All buildings within 2 tile range get +1 Worker Capaicity and Storage Multiplier. Generate science based on faith production of all shrines",
   Paper: "Papir",
   PaperMaker: "Papir fabrik",
   Parliament: "Parlament",
   Parthenon: "Parthenon",
   ParthenonDescV2: "To store personer i klassisk alder fødes, og du får 4 valgmuligheder for hver. Musiker- og Malerforening får +1 Produktion, Arbejderkapacitet og Lagerkapacitetsmultiplikator og er undtaget fra -1 Lykke",
   Passcode: "Passcode",
   PasscodeToastHTML: "<b>%{code}</b> is your passcode and it's valid for 30 minutes",
   PatchNotes: "Versionsnoter",
   Peace: "Peace",
   Peacekeeper: "Peacekeeper",
   Penthouse: "Penthouse",
   PercentageOfProductionWorkers: "Percentage of Production Workers",
   Performance: "Performance",
   PermanentGreatPeople: "Permanente Store Personer",
   PermanentGreatPeopleAcquired: "Permanente Store Personer Erhvervet",
   PermanentGreatPeopleUpgradeUndo: "Undo permanent great people upgrade: this will convert upgraded level back to shards - you will get %{amount} shards",
   Persepolis: "Persepolis",
   PersepolisDesc: "Alle kobberminer, trahuggere og stenmine får +1 Produktion Multiplikator, Arbejderkapacitetsmultiplikator og Lagerkapacitetsmultiplikator",
   PeterHiggs: "Peter Higgs",
   PeterHiggsDesc: "+%{value} Science From Busy Workers",
   Petra: "Petra",
   PetraDesc: "Generer tidsbøjning, når du er offline, som du kan bruge til at fremskynde dit imperium",
   PetraOfflineTimeReconciliation: "Du har fået %{count} tidsbøjning efter serverens offline tidsafstemning",
   Petrol: "Benzin",
   PhiloFarnsworth: "Philo Farnsworth",
   Philosophy: "Filosofi",
   Physics: "Fysik",
   PierreDeCoubertin: "Pierre de Coubertin",
   Pizza: "Pizza",
   Pizzeria: "Pizzeria",
   PlanetaryRover: "Planetary Rover",
   Plastics: "Plastik",
   PlasticsFactory: "Plastikfabrik",
   PlatformAndroid: "Android",
   PlatformiOS: "iOS",
   PlatformSteam: "Steam",
   PlatformSyncInstructionHTML: "If you want to sync your progress on this device to a new device, click <b>Sync To A New Device</b> and get a one-time passcode. On your new device, click <b>Connect To A Device</b> and type in the one-time passcode",
   Plato: "Platon",
   PlayerHandle: "Spiller navn",
   PlayerHandleOffline: "Du er i øjeblikket offline",
   PlayerMapClaimThisTile: "Overtag Dette område",
   PlayerMapClaimTileCondition2: "Du er ikke blevet bortvist af snyd",
   PlayerMapClaimTileCondition3: "Du har låst den nødvendige teknologi op: %{tech}",
   PlayerMapClaimTileCondition4: "Du har ikke krævet et område eller har passeret ventetiden for at flytte dit område",
   PlayerMapClaimTileCooldownLeft: "Ventetid tilbage: %{time}",
   PlayerMapClaimTileNoLongerReserved: "Dette område er ikke længere reserveret. Du kan bortvise <b>%{name}</b> og kræve dette område til dig selv",
   PlayerMapEstablishedSince: "Etableret Siden",
   PlayerMapLastSeenAt: "Sidst Set",
   PlayerMapMapTileBonus: "Trade Tile Bonus",
   PlayerMapMenu: "Handels kortet",
   PlayerMapOccupyThisTile: "Occupy This Tile",
   PlayerMapOccupyTileCondition1: "This tile is adjacent to your home or occupied tiles",
   PlayerMapPageGoBackToCity: "Gå Tilbage Til Byen",
   PlayerMapSetYourTariff: "Indstil Din Tarif",
   PlayerMapTariff: "Tarif",
   PlayerMapTariffApply: "Anvend denne Tarif",
   PlayerMapTariffDesc: "Enhver handel, der går gennem din område, vil betale tarif til dig. Det er en balance: Hvis du øger tarifen, vil du få mere fra hver handel, men færre handler vil gå gennem dit område.",
   PlayerMapTileAvailableTilePoint: "Available Tile Point",
   PlayerMapTileFromOccupying: "From Owned/Occupied Tiles",
   PlayerMapTileFromOccupyingTooltipHTML: "An owned/occupied tile generates <b>%{point}</b> tile point per hour (up to %{max} days from the first claimed tile)",
   PlayerMapTileFromRank: "From Account Rank",
   PlayerMapTileTilePoint: "Tile Point",
   PlayerMapTileUsedTilePoint: "Used Tile Point",
   PlayerMapTileUsedTilePointTooltipHTML: "You need <b>1 tile point</b> to own/occupy a tile",
   PlayerMapTradesFrom: "Handler fra %{name}",
   PlayerMapUnclaimedTile: "Frit område",
   PlayerMapYourTile: "Dit område",
   PlayerTrade: "Handel Med Spillere",
   PlayerTradeAddSuccess: "Handelen er blevet tilføjet",
   PlayerTradeAddTradeCancel: "Annuller",
   PlayerTradeAmount: "Beløb",
   PlayerTradeCancelDescHTML: "You will get <b>%{res}</b> back after cancelling this trade: <b>%{percent}</b> charged for refund and <b>%{discard}</b> discarded due to storage overflow<br><b>Are you sure you want to cancel?</b>",
   PlayerTradeCancelTrade: "Annuller Handel",
   PlayerTradeClaim: "Kræv",
   PlayerTradeClaimAll: "Kræv Alle",
   PlayerTradeClaimAllFailedMessageV2: "Det lykkedes ikke at kræve nogen handler - er lageret fuldt?",
   PlayerTradeClaimAllMessageV2: "Du har krævet: <b>%{resources}</b>",
   PlayerTradeClaimAvailable: "%{count} handel(er) er blevet udfyldt og kan kræves",
   PlayerTradeClaimTileFirst: "Kræv Dit område På Handelskortet",
   PlayerTradeClaimTileFirstWarning: "Du kan kun handle med andre spillere, efter du har krævet dit område på handelskortet",
   PlayerTradeClearAll: "Ryd Alle Beløb",
   PlayerTradeClearFilter: "Clear Filters",
   PlayerTradeDisabledBeta: "You can ony create player trades once the beta version is released",
   PlayerTradeFill: "Udfyld",
   PlayerTradeFill50: "Fill 50%",
   PlayerTradeFill95: "Fill 95%",
   PlayerTradeFillAmount: "Udfyld Beløb",
   PlayerTradeFillAmountMaxV2: "Fyld Max",
   PlayerTradeFillBy: "Udfyld Ved",
   PlayerTradeFillPercentage: "Fyld Procent",
   PlayerTradeFillSuccessV2: "<b>%{success}/%{total}</b> handler er blevet udfyldt. Du betalte <b>%{fillAmount} %{fillResource}</b> og modtog <b>%{receivedAmount} %{receivedResource}</b>",
   PlayerTradeFillTradeButton: "Udfyld Handel",
   PlayerTradeFillTradeTitle: "Udfyld Handel",
   PlayerTradeFilters: "Filtre",
   PlayerTradeFiltersApply: "Anvend",
   PlayerTradeFiltersClear: "Ryd",
   PlayerTradeFilterWhatIHave: "Filter By What I Have",
   PlayerTradeFrom: "Fra",
   PlayerTradeIOffer: "Jeg Tilbyder",
   PlayerTradeIWant: "Jeg Vil Købe",
   PlayerTradeMaxAll: "Maksimer Beløb",
   PlayerTradeMaxTradeAmountFilter: "Max Amount",
   PlayerTradeMaxTradeExceeded: "Du har overskredet det maksimale antal aktive handler for din rangering",
   PlayerTradeNewTrade: "Ny Handel",
   PlayerTradeNoFillBecauseOfResources: "No trade has been filled due to insufficient resources",
   PlayerTradeNoValidRoute: "Kan ikke finde en gyldig handelsrute mellem dig og %{name}",
   PlayerTradeOffer: "Tilbud",
   PlayerTradePlaceTrade: "Placer Handel",
   PlayerTradePlayerNameFilter: "Player Name",
   PlayerTradeResource: "Ressource",
   PlayerTradeStorageRequired: "Lagerplads Krævet",
   PlayerTradeTabImport: "Import",
   PlayerTradeTabPendingTrades: "Pending Trades",
   PlayerTradeTabTrades: "Trades",
   PlayerTradeTariffTooltip: "Indsamlet fra en handelstarif",
   PlayerTradeWant: "Ønskes",
   PlayerTradeYouGetGross: "Du Får (Før Tarif): %{res}",
   PlayerTradeYouGetNet: "Du Får (Efter Tarif): %{res}",
   PlayerTradeYouPay: "Du Betaler: %{res}",
   Poem: "Digt",
   PoetrySchool: "Poetry School",
   Politics: "Politik",
   PolytheismLevelX: "Polytheism %{level}",
   PorcelainTower: "Porcelænstårnet",
   PorcelainTowerDesc: "+5 Lykke. Når det er bygget, bliver alle dine ekstra store personer ved genfødsel tilgængelige for denne kørsel (de rulles efter samme regel som permanente store personer)",
   PorcelainTowerMaxPickPerRoll: "Prefer Max Pick Per Roll",
   PorcelainTowerMaxPickPerRollDescHTML: "When choosing great people after Porcelain Tower completed, prefer max pick per roll for the available amount",
   Poseidon: "Poseidon",
   PoseidonDescV2: "All adjacent buildings get free upgrades to Level 25 and +N Production, Worker Capacity and Storage Multiplier. N = Tier of the building",
   PoultryFarm: "Poultry Farm",
   Power: "Strøm",
   PowerAvailable: "Tilgængelig Strøm",
   PowerUsed: "Brugt Strøm",
   PreciousMetal: "Ædelmetal",
   Printing: "Trykning",
   PrintingHouse: "Trykkeri",
   PrintingPress: "Trykpresse",
   PrivateOwnership: "Privat Eje",
   Produce: "Producer",
   ProduceResource: "Producer: %{resource}",
   ProductionMultiplier: "Produktionsmultiplikator",
   ProductionPriority: "Produktionsprioritet",
   ProductionPriorityDescV4: "Priority determins the order that buildings transport and produce - a bigger number means a building transports and produces before other buildings",
   ProductionWorkers: "Produktionsarbejdere",
   Progress: "Fremskridt",
   ProgressTowardsNextGreatPerson: "Fremskridt Mod Næste Store Person Ved Genfødsel",
   ProgressTowardsTheNextGreatPerson: "Progress Towards the Next Great Person",
   PromotionGreatPersonDescV2: "When consumed, promote any permanent great people of the same age to the next age",
   ProphetsMosque: "Prophet's Mosque",
   ProphetsMosqueDesc: "Double the effect of Harun al-Rashid. Generate science based on faith production of all mosques",
   Province: "Provins",
   ProvinceAegyptus: "Ægypten",
   ProvinceAfrica: "Afrika",
   ProvinceAsia: "Asien",
   ProvinceBithynia: "Bithynia",
   ProvinceCantabri: "Cantabri",
   ProvinceCappadocia: "Cappadocia",
   ProvinceCilicia: "Cilicia",
   ProvinceCommagene: "Commagene",
   ProvinceCreta: "Creta",
   ProvinceCyprus: "Cypern",
   ProvinceCyrene: "Cyrene",
   ProvinceGalatia: "Galatia",
   ProvinceGallia: "Gallia",
   ProvinceGalliaCisalpina: "Gallia Cisalpina",
   ProvinceGalliaTransalpina: "Gallia Transalpina",
   ProvinceHispania: "Hispania",
   ProvinceIllyricum: "Illyricum",
   ProvinceItalia: "Italien",
   ProvinceJudia: "Judia",
   ProvinceLycia: "Lycia",
   ProvinceMacedonia: "Makedonien",
   ProvinceMauretania: "Mauretanien",
   ProvinceNumidia: "Numidia",
   ProvincePontus: "Pontus",
   ProvinceSardiniaAndCorsica: "Sardinien Og Corsica",
   ProvinceSicillia: "Sicillien",
   ProvinceSophene: "Sophene",
   ProvinceSyria: "Syrien",
   PublishingHouse: "Forlagsbygning",
   PyramidOfGiza: "Pyramiden i Giza",
   PyramidOfGizaDesc: "Alle bygninger, der producerer arbejdere, får +1 Produktion Multiplikator",
   QinShiHuang: "Qin Shi Huang",
   Radio: "Radio",
   RadioStation: "Radiostation",
   Railway: "Jernbane",
   RamessesII: "Ramses II",
   RamessesIIDesc: "+%{value} Bygger Kapacitet Multiplikator",
   RandomColorScheme: "Random Color Scheme",
   RapidFire: "Maskingevær",
   ReadFullPatchNotes: "Læs Patch Noter",
   RebirthHistory: "Rebirth History",
   RebirthTime: "Rebirth Time",
   Reborn: "Genfødt",
   RebornModalDescV3: "You will start a new empire but all your great people <b>from this run</b> becomes permanent shards, which can be used to upgrade your <b>permanent great people level</b>. You will also get extra great people shards based on your <b>total empire value</b>",
   RebornOfflineWarning: "Du er i øjeblikket offline. Du kan kun genfødes, når du er tilsluttet serveren",
   RebornTradeWarning: "Du har handler, der er aktive eller kan kræves. <b>Genfødsel vil slette dem</b> - du bør overveje at annullere eller kræve dem først",
   RedistributeAmongSelected: "Omdeling Blandt Valgte",
   RedistributeAmongSelectedCap: "Begrænsning",
   RedistributeAmongSelectedImport: "Import",
   Refinery: "Raffinaderi",
   Reichstag: "Reichstag",
   Religion: "Religion",
   ReligionBuddhism: "Buddhism",
   ReligionChristianity: "Christianity",
   ReligionDescHTML: "Choose from <b>Christianity, Islam, Buddhism or Polytheism</b> as your empire religion. You <b>cannot switch religion</b> after it is chosen. You can unlock more boost within each religion",
   ReligionIslam: "Islam",
   ReligionPolytheism: "Polytheism",
   Renaissance: "Renessance",
   RenaissanceAge: "Renessance",
   ReneDescartes: "René Descartes",
   RequiredDeposit: "Påkrævet Indskud",
   RequiredWorkersTooltipV2: "Required number of workers for production is equal to the sum of all resources consumed and produced after multipliers (excluding dynamic multipliers)",
   RequirePower: "Kræver Strøm",
   RequirePowerDesc: "Denne bygning skal opføres på en område med strøm og kan forlænge strømmen til dens tilstødende områder",
   Research: "Forskning",
   ResearchFund: "Research Fund",
   ResearchLab: "Forskningslaboratorium",
   ResearchMenu: "Forskning",
   ResourceAmount: "Mængde",
   ResourceBar: "Ressource Bar",
   ResourceBarExcludeStorageFullHTML: "Ekskluder bygninger med <b>fuld lagring</b> fra Ikke Producerende Bygninger",
   ResourceBarExcludeTurnedOffOrNoActiveTransportHTML: "Ekskluder bygninger, der er <b>slukket</b> fra Ikke Producerende Bygninger",
   ResourceBarShowUncappedHappiness: "Vis Ukapret Lykke",
   ResourceCloneTooltip: "The production multiplier only applies to the cloned resource (i.e. the extra copy)",
   ResourceColor: "Ressource Farve",
   ResourceExportBelowCap: "Eksport Under Begrænsning",
   ResourceExportBelowCapTooltip: "Tillad andre bygninger at transportere en ressource fra denne bygning, selv når dens mængde er under begrænsningen",
   ResourceExportToSameType: "Eksport til Samme Type",
   ResourceExportToSameTypeTooltip: "Tillad andre bygninger af samme type at transportere en ressource fra denne bygning",
   ResourceFromBuilding: "%{resource} fra %{building}",
   ResourceImport: "Ressource Transport",
   ResourceImportCapacity: "Ressource Transport Kapacitet",
   ResourceImportImportCapV2: "Maksimum Beløb",
   ResourceImportImportCapV2Tooltip: "Denne bygning vil stoppe med at transportere denne ressource, når maksimumbeløbet er nået",
   ResourceImportImportPerCycleV2: "Per Cyklus",
   ResourceImportImportPerCycleV2ToolTip: "Mængden af denne ressource, der transporteres per cyklus",
   ResourceImportPartialWarningHTML: "The total resource transport capacity has exceeds the maximum capacity: <b>each resource transport will only transport partially per cycle</b>",
   ResourceImportResource: "Ressource",
   ResourceImportSettings: "Ressource Transport: %{res}",
   ResourceImportStorage: "Lager",
   ResourceNeeded: "Extra %{resource} x%{amount} Needed",
   ResourceTransportPreference: "Transport Præference",
   RevealDeposit: "Afslør",
   Revolution: "Revolution",
   RhineGorge: "Rhine Gorge",
   RhineGorgeDesc: "+2 Happiness for each wonder within 2 tile range",
   RichardFeynman: "Richard Feynman",
   RichardFeynmanDesc: "+%{value} Science from All Workers if more than 50% of workers are busy and less than 50% of busy workers work in transportation",
   RichardJordanGatling: "Richard Jordan Gatling",
   Rifle: "Riffel",
   RifleFactory: "Riffelfabrik",
   Rifling: "Rifling",
   Rijksmuseum: "Rijksmuseum",
   RijksmuseumDesc: "+5 Lykke. Alle bygninger, der forbruger eller producerer Kultur, får +1 Produktions-, Lager- og Arbejderkapacitet",
   RoadAndWheel: "Veje & Hjul",
   RobertNoyce: "Robert Noyce",
   Robocar: "Robocar",
   RobocarFactory: "Robocar Factory",
   Robotics: "Robotteknologi",
   RockefellerCenterChristmasTree: "Rockefeller Center Christmas Tree",
   RockefellerCenterChristmasTreeDesc: "+3 Happiness for each unlocked age. This natural wonder can only be discovered in December",
   Rocket: "Raket",
   RocketFactory: "Raketfabrik",
   Rocketry: "Raketvidenskab",
   Roman: "Roman",
   RomanForum: "Det Romerske Forum",
   RudolfDiesel: "Rudolf Diesel",
   Rurik: "Rurik",
   RurikDesc: "+%{value} Lykke",
   SagradaFamilia: "Sagrada Família",
   SagradaFamiliaDesc: "Sagrada Família",
   SaintBasilsCathedral: "Sankt Basil's Katedral",
   SaintBasilsCathedralDescV2: "Tillad råstofudvindingsbygninger at arbejde ved siden af et ressource område. Alle Tier I bygninger får +1 Produktionsmultiplikator, Arbejderkapacitetsmultiplikator og Lagermultiplikator",
   Saladin: "Saladin",
   Samsuiluna: "Samsu-iluna",
   Sand: "Sand",
   Sandpit: "Sandgrav",
   SantaClausVillage: "Santa Claus Village",
   SantaClausVillageDesc: "When completed, a great person of the current age is born. This wonder can be upgraded and each additional upgrade provides an extra great person. When choosing great people from this wonder, 4 choices are provided. This wonder can only be constructed in December",
   SargonOfAkkad: "Sargon Af Akkad",
   Satellite: "Satellite",
   SatelliteFactory: "Satellite Factory",
   SatoshiNakamoto: "Satoshi Nakamoto",
   Saturnalia: "Saturnalia: Alps no longer increases Consumption Multiplier",
   SaveAndExit: "Gem og Afslut",
   School: "Skole",
   Science: "Videnskab",
   ScienceFromBusyWorkers: "Videnskab Fra Travle Arbejdere",
   ScienceFromIdleWorkers: "Videnskab Fra Ledige Arbejdere",
   SciencePerBusyWorker: "Per Travl Arbejder",
   SciencePerIdleWorker: "Per Ledig Arbejder",
   ScrollSensitivity: "Scroll Sensitivity",
   ScrollSensitivityDescHTML: "Adjust sensitivity when scrolling mousewheel. <b>Must be between 0.01 to 100. Default is 1</b>",
   ScrollWheelAdjustLevelTooltip: "Du kan bruge rullehjulet til at justere niveauet, når markøren er over dette",
   SeaTradeCost: "Søhandelsomkostninger",
   SeaTradeUpgrade: "Handel med spillere over havet. Tarif for hver havområde: %{tariff}",
   SelectCivilization: "Select Civilization",
   SelectedAll: "Vælg Alle",
   SelectedCount: "%{count} Valgt",
   Semiconductor: "Halvleder",
   SemiconductorFab: "Semiconductor Fab",
   SendExplorer: "Send Opdagelsesrejsende",
   SergeiKorolev: "Sergei Korolev",
   SetAsDefault: "Sæt Som Standard",
   SetAsDefaultBuilding: "Sæt Som Standard For Alle %{building}",
   Shamanism: "Shamanisme",
   Shelter: "Ly",
   Shortcut: "Genvej",
   ShortcutBuildingPageSellBuildingV2: "Nedriv Bygning",
   ShortcutBuildingPageToggleBuilding: "Skift Produktion",
   ShortcutBuildingPageToggleBuildingSetAllSimilar: "Skift Produktion Og Anvend På Alle",
   ShortcutBuildingPageUpgrade1: "Opgraderingsknap 1 (+1)",
   ShortcutBuildingPageUpgrade2: "Opgraderingsknap 2 (+5)",
   ShortcutBuildingPageUpgrade3: "Opgraderingsknap 3 (+10)",
   ShortcutBuildingPageUpgrade4: "Opgraderingsknap 4 (+15)",
   ShortcutBuildingPageUpgrade5: "Opgraderingsknap 5 (+20)",
   ShortcutClear: "Ryd",
   ShortcutConflict: "Din genvej er i konflikt med %{name}",
   ShortcutNone: "Ingen",
   ShortcutPressShortcut: "Tryk på Genvejsnøgle...",
   ShortcutSave: "Gem",
   ShortcutScopeBuildingPage: "Bygningsside",
   ShortcutScopeConstructionPage: "Konstruktions-/Opgraderingsside",
   ShortcutScopeEmptyTilePage: "Tom områdeside",
   ShortcutScopePlayerMapPage: "Handelskortside",
   ShortcutScopeTechPage: "Teknologi Side",
   ShortcutScopeUnexploredPage: "Uudforsket Side",
   ShortcutTechPageGoBackToCity: "Gå Tilbage Til By",
   ShortcutTechPageUnlockTech: "Lås Valgt Teknologi Op",
   ShortcutUpgradePageCancelAllUpgrades: "Cancel All Upgrades",
   ShortcutUpgradePageCancelUpgrade: "Annuller Opgradering",
   ShortcutUpgradePageDecreaseLevel: "Reducer Opgraderingsniveau",
   ShortcutUpgradePageEndConstruction: "Afslut Konstruktion",
   ShortcutUpgradePageIncreaseLevel: "Forøg Opgraderingsniveau",
   ShowTransportArrow: "Show Transport Arrow",
   ShowTransportArrowDescHTML: "Turning this off will hide transport arrows. It might <i>slightly</i> improve performance on low end devices. Performance improvement takes effect <b>after restarting your game</b>",
   ShowUnbuiltOnly: "Vis kun bygninger, der ikke er opført endnu",
   Shrine: "Alter",
   SidePanelWidth: "Sidepanel Bredde",
   SidePanelWidthDescHTML: "Ændre bredden af sidepanelet. <b>Kræver genstart af spillet for at træde i kraft</b>",
   SiegeRam: "Rambuk",
   SiegeWorkshop: "Belejring værksted",
   Silicon: "Silicon",
   SiliconSmelter: "Silicon Smelter",
   Skyscraper: "Skyscraper",
   Socialism: "Socialism",
   SocialismLevel4DescHTMLV2: "Generate one-time science equivalent to the cost of the cheapest <b>World Wars Age</b> technology",
   SocialismLevel5DescHTMLV2: "Generate one-time science equivalent to the cost of the cheapest <b>Cold War Age</b> technology",
   SocialismLevelX: "Socialism Level %{level}",
   SocialNetwork: "Socialt Netværk",
   Socrates: "Sokrates",
   SocratesDesc: "+%{value} Videnskab Fra Travle Arbejdere",
   Software: "Software",
   SoftwareCompany: "Software Company",
   Sound: "Lyd",
   SoundEffect: "Lyd Effekt",
   SourceGreatPerson: "Stor Person: %{person}",
   SourceGreatPersonPermanent: "Permanent Stor Person: %{person}",
   SourceIdeology: "Ideology: %{ideology}",
   SourceReligion: "Religion: %{religion}",
   SourceResearch: "Forskning: %{tech}",
   SourceTradition: "Tradition: %{tradition}",
   SpaceCenter: "Space Center",
   Spacecraft: "Spacecraft",
   SpacecraftFactory: "Spacecraft Factory",
   SpaceNeedle: "Space Needle",
   SpaceNeedleDesc: "+1 Happiness for each wonder constructed",
   SpaceProgram: "Rum program",
   Sports: "Sport",
   Stable: "Stald",
   Stadium: "Stadion",
   StartFestival: "Let the Festival Begin!",
   Stateship: "Statskab",
   StatisticsBuildings: "Bygninger",
   StatisticsBuildingsSearchText: "Type a building name to search",
   StatisticsEmpire: "Empire",
   StatisticsExploration: "Udforskning",
   StatisticsOffice: "Statistik Kontor",
   StatisticsOfficeDesc: "Giv statistikker for dit imperium. Generer opdagelsesrejsende til at udforske kortet",
   StatisticsResources: "Ressourcer",
   StatisticsResourcesDeficit: "Deficit",
   StatisticsResourcesDeficitDesc: "Produktion: %{output} - Forbrug: %{input}",
   StatisticsResourcesRunOut: "Løb Tør",
   StatisticsResourcesSearchText: "Type a resource name to search",
   StatisticsScience: "Videnskab",
   StatisticsScienceFromBuildings: "Videnskab Fra Bygninger",
   StatisticsScienceFromWorkers: "Videnskab Fra Arbejdere",
   StatisticsScienceProduction: "Videnskabsproduktion",
   StatisticsStalledTransportation: "Standset Transport",
   StatisticsTotalTransportation: "Total Transport",
   StatisticsTransportation: "Transport",
   StatisticsTransportationPercentage: "Procentdel af Transportarbejdere",
   StatueOfLiberty: "Frihedsgudinden",
   StatueOfLibertyDesc: "Alle tilstødende bygninger får +N Produktions-, Lager- og Arbejderkapacitetsmultiplikator. N = Antal af dens tilstødende bygninger af samme type",
   StatueOfZeus: "Zeusstatuen",
   StatueOfZeusDesc: "Generer tilfældige ressource områder, der er blevet afsløret på tilstødende tomme områder. Alle tilstødende Tier I bygninger får +5 Produktions- og Lagermultiplikator",
   SteamAchievement: "Steam Præstationer",
   SteamAchievementDetails: "Se Steam Præstationer",
   SteamEngine: "Dampmaskine",
   Steamworks: "Dampværk",
   Steel: "Stål",
   SteelMill: "Stålværk",
   StephenHawking: "Stephen Hawking",
   Stock: "Aktie",
   StockExchange: "Børs",
   StockMarket: "Aktiemarked",
   StockpileDesc: "Denne bygning vil transportere %{capacity}x inputressourcer per produktionscyklus, indtil maksimumet er nået",
   StockpileMax: "Maksimalt Lager",
   StockpileMaxDesc: "Denne bygning vil stoppe med at transportere en ressource, når der er nok til %{cycle} produktionscyklusser",
   StockpileMaxUnlimited: "Ubegrænset",
   StockpileMaxUnlimitedDesc: "Bygningen vil aldrig stoppe med at transportere ressourcer, indtil lagringen er fuld",
   StockpileSettings: "Lagring Input Kapacitet",
   Stone: "Sten",
   StoneAge: "Stenalder",
   Stonehenge: "Stonehenge",
   StonehengeDesc: "Alle bygninger, der forbruger eller producerer sten, får +1 Produktionsmultiplikator",
   StoneQuarry: "Stenbrud",
   StoneTool: "Sten Værktøj",
   StoneTools: "Sten Værktøjer",
   Storage: "Lager",
   StorageBaseCapacity: "Basis Kapacitet",
   StorageMultiplier: "Lagermultiplikator",
   StorageUsed: "Lager Brugt",
   StPetersBasilica: "St. Peters Basilika",
   StPetersBasilicaDescV2: "All churches get +5 Storage Multiplier. Generate science based on faith production of all churches",
   Submarine: "Submarine",
   SubmarineYard: "Submarine Yard",
   SuleimanI: "Suleiman I",
   SummerPalace: "Sommerpaladset",
   SummerPalaceDesc: "Alle tilstødende bygninger, der forbruger eller producerer krudt, er undtaget fra -1 Lykke. Alle bygninger, der forbruger eller producerer krudt, får +1 Produktions-, Lager- og Arbejderkapacitet",
   Supercomputer: "Supercomputer",
   SupercomputerLab: "Supercomputer Lab",
   SupporterPackRequired: "Supporter Pack Required",
   SupporterThankYou: "CivIdle is kept afloat thanks to the generousity of the following supporter pack owners",
   SwissBank: "Swiss Bank",
   SwissBankDescV2: "Convert a chosen resource from adjacent warehouses to Koti (10 million in Sanskrit) - a tradeable resource that is worth 10M value. Each level of Swiss Bank add 1 Koti conversion per cycle, which is affected by Production Multiplier. Swiss Bank can store unlimited amount of Koti",
   Sword: "Sværd",
   SwordForge: "Sværd Smedje",
   SydneyOperaHouse: "Sydney Opera Hus",
   SydneyOperaHouseDescV2: "Sydney Opera House",
   SyncToANewDevice: "Sync To A New Device",
   Synthetics: "Syntetik",
   TajMahal: "Taj Mahal",
   TajMahalDescV2: "En stor person fra den klassiske tidsalder og en stor person fra mellemalderen fødes. +5 Bygger Kapacitet Multiplikator, når bygninger opgraderes over Niveau 20",
   TangOfShang: "Shang-Dynastiets Tang",
   TangOfShangDesc: "+%{value} Videnskab Fra Ledige Arbejdere",
   Tank: "Tank",
   TankFactory: "Tank fabrik",
   TechAge: "Tidsalder",
   TechGlobalMultiplier: "Boost",
   TechHasBeenUnlocked: "%{tech} er blevet låst op",
   TechProductionPriority: "Lås op for bygningsprioritet - tillad indstilling af produktionsprioritet for hver bygning",
   TechResourceTransportPreference: "Lås op for bygnings transportpræference - tillad indstilling af, hvordan en bygning transporterer ressourcer, der er nødvendige for dens produktion",
   TechResourceTransportPreferenceAmount: "Mængde",
   TechResourceTransportPreferenceAmountTooltip: "Denne bygning vil foretrække at transportere ressourcer fra bygninger, der har større mængde i lagring",
   TechResourceTransportPreferenceDefault: "Standard",
   TechResourceTransportPreferenceDefaultTooltip: "Overskriv ikke transportpræferencen for denne ressource, vil bruge bygningens transportpræference i stedet",
   TechResourceTransportPreferenceDistance: "Afstand",
   TechResourceTransportPreferenceDistanceTooltip: "Denne bygning vil foretrække at transportere ressourcer fra bygninger, der er tættere på",
   TechResourceTransportPreferenceOverrideTooltip: "Denne ressource har transportpræference-overskrivning: %{mode}",
   TechResourceTransportPreferenceStorage: "Lagring",
   TechResourceTransportPreferenceStorageTooltip: "Denne bygning vil foretrække at transportere ressourcer fra bygninger, der har en højere procentdel af brugt lagring",
   TechStockpileMode: "Lås op for lagerstyring - tillad justering af lager for hver bygning",
   Teleport: "Teleport",
   TeleportDescHTML: "A teleport is generated <b>every %{time} seconds</b>. A teleport can be used to <b>move a building (wonders excluded)</b> once",
   Television: "Fjernsyn",
   TempleOfArtemis: "Artemistemplet",
   TempleOfArtemisDesc: "Alle Sværd Smedjer og Rustkamre får +5 Niveau, når de er færdige. Alle Sværd Smedjer og Rustkamre får +1 Produktionsmultiplikator, Arbejdskapacitetsmultiplikator og Lagringsmultiplikator",
   TempleOfHeaven: "Himmelens Tempel",
   TempleOfHeavenDesc: "Alle bygninger, der er niveau 10 eller derover, får +1 Arbejdskapacitetsmultiplikator",
   TempleOfPtah: "Ptahs Tempel",
   TerracottaArmy: "Terracotta Hær",
   TerracottaArmyDesc: "Alle Jern Miner får +1 Produktionsmultiplikator, Arbejdskapacitetsmultiplikator og Lagringsmultiplikator. Jern Smelteovne får +1 Produktionsmultiplikator for hver tilstødende Jern Mine",
   Thanksgiving: "Thanksgiving: Wall Street provides double the boost to buildings and applies to Mutual Fund, Hedge Fund and Bitcoin Miner. Research Funds get +5 Production Multiplier",
   Theater: "Teater",
   Theme: "Tema",
   ThemeColor: "Tema Farve",
   ThemeColorResearchBackground: "Forskningsbaggrund",
   ThemeColorReset: "Nulstil til Standard",
   ThemeColorResetBuildingColors: "Nulstil Bygningsfarver",
   ThemeColorResetResourceColors: "Nulstil Ressourcefarver",
   ThemeInactiveBuildingAlpha: "Inaktiv Bygning Alfa",
   ThemePremiumTile: "This tile is only available for Supporter Pack owners",
   ThemeResearchHighlightColor: "Fremhæv Farve til Forskning",
   ThemeResearchLockedColor: "Låst Farve til Forskning",
   ThemeResearchUnlockedColor: "Låst Farve til Forskning",
   ThemeTransportIndicatorAlpha: "Transportindikator Alfa",
   Theocracy: "Teokrati",
   TheoreticalData: "Theoretical Data",
   ThePentagon: "The Pentagon",
   ThePentagonDesc: "After constructed, generate teleports that can be used to move buildings. All buildings within 2 tile range get +1 Production, Worker Capacity and Storage Multiplier",
   TheWhiteHouse: "The White House",
   ThomasEdison: "Thomas Edison",
   ThomasGresham: "Thomas Gresham",
   Tile: "Område",
   TileBonusRefreshIn: "Tile bonus will refresh in <b>%{time}</b>",
   TimBernersLee: "Tim Berners-Lee",
   TimeWarp: "Tidskrumning",
   TimeWarpWarning: "Acceleration med en højere hastighed end din computer kan håndtere, kan resultere i datatab: BRUG PÅ EGET ANSVAR",
   ToggleWonderEffect: "Toggle Wonder Effect",
   Tool: "Værktøj",
   TopkapiPalace: "Topkapı Palace",
   TopkapiPalaceDesc: "All buildings within 2 tile range get +X Storage Multiplier. X = 50% of its Production Multiplier (excluding Dynamic)",
   TotalEmpireValue: "Totalt Imperium Værdi",
   TotalEmpireValuePerCycle: "Totalt Imperium Værdi Per Cyklus",
   TotalEmpireValuePerCyclePerGreatPeopleLevel: "Totalt Imperium Værdi Per Cyklus Per Stor Person Niveau",
   TotalEmpireValuePerWallSecond: "Total Empire Value Wall Second",
   TotalEmpireValuePerWallSecondPerGreatPeopleLevel: "Total Empire Value Per Wall Second Per Great People Level",
   TotalGameTimeThisRun: "Total Game Time This Run",
   TotalScienceRequired: "Total Science Required",
   TotalStorage: "Total Lagring",
   TotalWallTimeThisRun: "Total Wall Time This Run",
   TotalWallTimeThisRunTooltip: "Wall time (aka. elapsed real time) measures the actual time taken for this run. The differs from the game time in that Time Warp in Petra and Offline Production does not affect wall time but it does affect game time",
   TotalWorkers: "Samlet Arbejdere",
   TowerBridge: "Tower Bridge",
   TowerBridgeDesc: "After constructed, a great person from unlocked ages is born every 3600 cycles (1h game time)",
   TowerOfBabel: "Tower of Babel",
   TowerOfBabelDesc: "Provides +2 Production Multiplier to all buildings that has at least one working building located adjacent to the wonder",
   TradeFillSound: "'Trade Filled' Sound",
   TradeValue: "Trade Value",
   TraditionCommerce: "Commerce",
   TraditionCultivation: "Cultivation",
   TraditionDescHTML: "Choose from <b>Cultivation, Commerce, Expansion and Honor</b> as your empire tradition. You <b>cannot switch tradition</b> after it is chosen. You can unlock more boost within each tradition",
   TraditionExpansion: "Expansion",
   TraditionHonor: "Honor",
   Train: "Tog",
   TranslationPercentage: "%{language} er %{percentage} oversat. Hjælp med at forbedre denne oversættelse på GitHub",
   TranslatorCredit: "UnpredictedUwU",
   Translators: "Oversættere",
   TransportAllocatedCapacityTooltip: "Byggerkapacitet allokeret til at transportere denne ressource",
   TransportationWorkers: "Transportarbejdere",
   TransportCapacity: "Transportkapacitet",
   TransportCapacityMultiplier: "Transportkapacitetsmultiplikator",
   TransportManualControlTooltip: "Transporter denne ressource til konstruktion/opgradering",
   TransportPlanCache: "Transport Plan Cache",
   TransportPlanCacheDescHTML:
      "Every cycle, each building calculates the best transport plan based on its settings - this process requires high CPU power. Enabling this will attempt to cache the result of the transport plan if it is still valid and therefore reduce CPU usage and frame rate drop. <b>Experimental Feature</b>",
   TribuneUpgradeDescGreatPeopleWarning: "Dit nuværende spil har store personer. Du bør <b>genfødes først</b>. Opgradering til Quæstor-rang vil nulstille dit nuværende spil",
   TribuneUpgradeDescGreatPeopleWarningTitle: "Venligst Genfødes Først",
   TribuneUpgradeDescV4:
      "You can play the full game as Tribune if you do not plan to participate in the <b>optional</b> online features. To acquire unrestricted access to the online features, you will need to upgrade to Quaestor. <b>This is an anti-bot measure to keep the game free for everyone.</b> However, <b>when upgrading to Quaestor</b> you can carry over great people: <ul><li>Up to Level <b>3</b> for Bronze, Iron and Classical Age</li><li>Up to Level <b>2</b> for Middle Age, Renaissance and Industrial Age</li><li>Up to Level <b>1</b> for World Wars, Cold War and Information Age</li></ul>Great People Shards above the level and <b>Age Wisdom</b> levels <b>cannot</b> be carried over",
   TurnOffFullBuildings: "Turn Off All %{building} With Full Storage",
   TurnOnTimeWarpDesc: "Koster %{speed} krøller for hver sekund og fremskynder dit imperium for at køre med %{speed}x hastighed.",
   Tutorial: "Tutorial",
   TutorialPlayerFlag: "Vælg dit flag",
   TutorialPlayerHandle: "Vælg dit spiller navn",
   TV: "TV",
   TVStation: "TV Station",
   UnclaimedGreatPersonPermanent: "Du har uafhentet <b>Permanente Store Personer</b>, klik her for at hente",
   UnclaimedGreatPersonThisRun: "Du har uafhentet <b>Store Personer dette spil</b>, klik her for at hente",
   UnexploredTile: "Uudforsket område",
   UNGeneralAssemblyCurrent: "Aktuel FN Generalforsamling #%{id}",
   UNGeneralAssemblyMultipliers: "<b>+%{count}</b> Produktion, Arbejdskapacitet og Lagrings Multiplikatorer for <b>%{buildings}</b>",
   UNGeneralAssemblyNext: "Kommende FN Generalforsamling #%{id}",
   UNGeneralAssemblyVoteEndIn: "Du kan ændre din stemme når som helst, før afstemningen slutter om <b>%{time}</b>",
   UniqueBuildings: "Unikke Bygninger",
   UniqueTechMultipliers: "Unique Tech Multipliers",
   UnitedNations: "De Forenede Nationer",
   UnitedNationsDesc: "Alle Tier IV og V og VI bygninger får +1 Produktion, Arbejdskapacitet og Lagrings Multiplikator. Deltag i FN's Generalforsamling og stem for en yderligere stigning hver uge",
   University: "Universitet",
   UnlockableResearch: "Tilgængelig Forskning",
   UnlockBuilding: "Lås Op",
   UnlockTechProgress: "Fremgang",
   UnlockXHTML: "Unlock <b>%{name}</b>",
   Upgrade: "Opgrader",
   UpgradeBuilding: "Opgrader",
   UpgradeBuildingNotProducingDescV2: "Denne bygning bliver opgraderet - <b>produktionen vil stoppe, indtil opgraderingen er færdig</b>",
   UpgradeTo: "Opgrader Til Niveau %{level}",
   Uranium: "Uran",
   UraniumEnrichmentPlant: "Uranberigelsesanlæg",
   UraniumMine: "Uran mine",
   Urbanization: "Urbanisering",
   UserAgent: "Brugeragent: %{driver}",
   View: "Se",
   ViewMenu: "Se",
   ViewTechnology: "Se",
   Vineyard: "Vingård",
   VirtualReality: "Virtuel Virkelighed",
   Voltaire: "Voltaire",
   WallOfBabylon: "Wall of Babylon",
   WallOfBabylonDesc: "All buildings get +N Storage Multiplier. N = number of unlocked ages / 2",
   WallStreet: "Wall Street",
   WallStreetDesc: "All buildings that produce coin, banknote, bond, stock and forex within 2 tile range get +N production multiplier. N = Random value between 1 to 5 which is different per building and changes with every market refresh. Double the effect of John D. Rockefeller",
   WaltDisney: "Walt Disney",
   Warehouse: "Lager",
   WarehouseAutopilotSettings: "Autopilot Indstillinger",
   WarehouseAutopilotSettingsEnable: "Aktivér Autopilot",
   WarehouseAutopilotSettingsRespectCapSetting: "Kræv Lager < Kapacitet",
   WarehouseAutopilotSettingsRespectCapSettingTooltip: "Autopilot vil kun transportere ressourcer, hvis mængden i lageret er under kapaciteten",
   WarehouseDesc: "Transport af specifikke ressourcer og giver ekstra opbevaring",
   WarehouseExtension: "Lås op for lagerkaravaneudvidelses tilstand. Tillad lagre ved siden af karavaner at blive inkluderet i spillerhandel",
   WarehouseSettingsAutopilotDesc: "Dette lager vil bruge sin ledige kapacitet til at transportere ressourcer fra bygninger, der har fuldt lager. Nuværende ledig kapacitet: %{capacity}",
   WarehouseUpgrade: "Lås op for lagerautopilottilstand. Gratis transport mellem et lager og dets tilstødende bygninger",
   WarehouseUpgradeDesc: "Gratis transport mellem dette lager og dets tilstødende områder",
   Warp: "Tidskrumning",
   WarpSpeed: "Warp Speed",
   Water: "Vand",
   WellStockedTooltip: "Godt forsynede bygninger er bygninger, der har nok ressourcer til deres produktion, herunder bygninger, der producerer, der har fuldt lager eller ikke producerer på grund af mangel på arbejdere",
   WernherVonBraun: "Wernher von Braun",
   Westminster: "Westminster",
   Wheat: "Hvede",
   WheatFarm: "Hvedefarm",
   WildCardGreatPersonDescV2: "When consumed, become any great person of the same age",
   WilliamShakespeare: "William Shakespeare",
   Wine: "Vin",
   Winery: "Vingård",
   WishlistSpaceshipIdle: "Wishlist Spaceship Idle",
   Wonder: "Vidunderværk",
   WonderBuilderCapacityDescHTML: "<b>Byggerkapacitet</b> ved konstruktion af vidunderværker påvirkes af <b>alderen</b> og <b>teknologien</b>, der låser vidunderværket op",
   WondersBuilt: "Byggede Verdensvidunderværker",
   WondersUnlocked: "Låste Verdensvidunderværker Op",
   WonderUpgradeLevel: "Wonder Level",
   Wood: "Træ",
   Worker: "Arbejder",
   WorkerCapacityMultiplier: "Arbejdskapacitetsmultiplikator",
   WorkerHappinessPercentage: "Lykkemultiplikator",
   WorkerMultiplier: "Arbejdskapacitet",
   WorkerPercentagePerHappiness: "%{value}% Multiplikator for Hver Lykke",
   Workers: "Arbejdere",
   WorkersAvailableAfterHappinessMultiplier: "Arbejdere Efter Lykkemultiplikator",
   WorkersAvailableBeforeHappinessMultiplier: "Arbejdere Før Lykkemultiplikator",
   WorkersBusy: "Beskæftigede Arbejdere",
   WorkerScienceProduction: "Arbejder Videnskabsproduktion",
   WorkersRequiredAfterMultiplier: "Krævede Arbejdere",
   WorkersRequiredBeforeMultiplier: "Krævet Arbejdskapacitet",
   WorkersRequiredForProductionMultiplier: "Produktionskapacitet Per Arbejder",
   WorkersRequiredForTransportationMultiplier: "Transportkapacitet Per Arbejder",
   WorkersRequiredInput: "Transport",
   WorkersRequiredOutput: "Produktion",
   WorldWarAge: "Verdenskrige",
   WorldWideWeb: "World Wide Web",
   WritersGuild: "Forfatter forening",
   Writing: "Skrivning",
   WuZetian: "Kejserinde Wu Zetian",
   WuZetianDesc: "+%{value} Transportkapacitetsmultiplikator",
   Xuanzang: "Xuanzang",
   YangtzeRiver: "Yangtze-floden",
   YangtzeRiverDesc: "Alle bygninger, der forbruger vand, får +1 Produktion, Arbejdskapacitet og Lager Multiplikator. Fordoble effekten af Zheng He (Stor Person). Hver niveau af Permanent Kejserinde Wu Zetian (Stor Person) giver +1 Lager Multiplikator til alle bygninger",
   YearOfTheSnake: "Year of the Snake",
   YearOfTheSnakeDesc:
      "After completed, when entering a new age, instead of getting one great person of each unlocked age, get the same amount of great people in the current age. All buildings within 2-tile range get +1 Production Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Production Multiplier to buildings within 2-tile range. This wonder can only be constructed during the lunar new year period (1.20 ~ 2.10)",
   YellowCraneTower: "Gul Kran-Tårn",
   YellowCraneTowerDesc: "+1 valg, når du vælger store personer. Alle bygninger inden for 1 områderækkevidde får +1 Produktion, Arbejdskapacitet og Lager Multiplikator. Når det er konstrueret ved siden af Yangtze-floden, øges rækkevidden til 2 områder",
   YuriGagarin: "Yuri Gagarin",
   ZagrosMountains: "Zagros Mountains",
   ZagrosMountainsDesc: "All adjacent buildings that have less than 5 Production Multiplier get +2 Production Multiplier. Double the effect of Nebuchadnezzar II (Great Person)",
   ZahaHadid: "Zaha Hadid",
   ZahaHadidDesc: "+%{value} Builder Capacity Multiplier",
   Zenobia: "Zenobia",
   ZenobiaDesc: "+%{value}t Petra Tidskrumning Lager",
   ZhengHe: "Zheng He",
   ZigguratOfUr: "Ziggurat of Ur",
   ZigguratOfUrDescV2: "Every 10 happiness (capped) provides +1 Production Multiplier to all buildings that do not produce workers and are unlocked in previous ages (max = number of unlocked ages / 2). Wonders (incl. Natural) no longer provide +1 Happiness. The effect can be turned off",
   Zoroaster: "Zoroaster",
   Zugspitze: "Zugspitze",
   ZugspitzeDesc: "For each unlocked age, get one point that can be used to provide one extra level to any Great Person that is born from this run",
};
