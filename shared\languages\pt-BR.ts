export const PT_BR = {
   About: "Sobre CivIdle",
   AbuSimbel: "Abu Simbel",
   AbuSimbelDesc: "Dobra o efeito de Ramsés II. Todas as maravilhas adjacentes recebem +1 Felicidade",
   AccountActiveTrade: "Troca Ativa",
   AccountChatBadge: "Insígnia de Chat",
   AccountCustomColor: "Cor Personalizada",
   AccountCustomColorDefault: "Padrão",
   AccountGreatPeopleLevelRequirement: "Nível de Figura Histórica Necessário",
   AccountLevel: "Ranque da Conta",
   AccountLevelAedile: "Edil",
   AccountLevelConsul: "Cônsul",
   AccountLevelMod: "Moderador",
   AccountLevelPlayTime: "Tempo de Jogo Ativo Online > %{requiredTime} (Seu tempo de jogo é %{actualTime})",
   AccountLevelPraetor: "Pretor",
   AccountLevelQuaestor: "<PERSON>or",
   AccountLevelSupporterPack: "Possui Pacote de Apoio",
   AccountLevelTribune: "Tribuno",
   AccountLevelUpgradeConditionAnyHTML: "Para melhorar sua conta, você só precisa satisfazer <b>um dos seguintes</b> critérios:",
   AccountPlayTimeRequirement: "Tempo de Jogo Necessário",
   AccountRankUp: "Atualizar Classificação da Conta",
   AccountRankUpDesc: "Todo o seu progresso será transferido para o seu novo ranque.",
   AccountRankUpTip: "Parabéns, sua conta é elegível para um ranque mais alto – clique aqui para atualizar!",
   AccountSupporter: "Dono do Pacote de Apoio",
   AccountTradePriceRange: "Faixa de Preço de Troca",
   AccountTradeTileReservationTime: "Reserva de Espaço de Troca",
   AccountTradeTileReservationTimeDesc: "Esse é o tempo que seu espaço de troca de jogador será reservado para você desde a última vez que você esteve online. Após o período de reserva terminar, seu espaço se tornará disponível para outros jogadores",
   AccountTradeValuePerMinute: "Valor de Troca Por Minuto",
   AccountTypeShowDetails: "Mostrar Detalhes da Conta",
   AccountUpgradeButton: "Melhorar para o Ranque Questor",
   AccountUpgradeConfirm: "Melhoria de Conta",
   AccountUpgradeConfirmDescV2: "Melhorar sua conta <b>reiniciará sua execução atual</b> e carregará pessoas notáveis permanentes dentro dos níveis permitidos. Isso <b>não</b> pode ser desfeito, você tem certeza de que deseja continuar?",
   Acknowledge: "Acknowledge",
   Acropolis: "Acrópole",
   ActorsGuild: "Associação de Atores",
   AdaLovelace: "Ada Lovelace",
   AdamSmith: "Adam Smith",
   AdjustBuildingCapacity: "Capacidade de Produção",
   AdvisorElectricityContent:
      "As usinas de energia fornecem dois novos sistemas para você. O primeiro, 'Energia', é indicado pelos blocos de relâmpago adjacentes à usina de energia. Alguns edifícios (começando com Rádio em Guerras Mundiais) têm um indicador 'requer energia' em sua lista de entradas. <b>Isso significa que eles devem ser construídos em um bloco de relâmpago para funcionar</b>. Edifícios que requerem energia e a têm, também transmitirão energia para os blocos adjacentes a esse edifício, então você pode alimentá-los um do outro, desde que pelo menos um esteja tocando uma usina de energia.<br><br>O outro sistema 'eletrificação' pode ser aplicado a <b>qualquer edifício em qualquer lugar</b> no mapa, desde que não produza ciência ou trabalhadores. Isso usa a energia gerada pela usina de energia para aumentar o consumo e a produção do edifício. Mais níveis de eletrificação exigem quantidades cada vez maiores de energia. Eletrificar edifícios que também têm 'requer energia' é mais eficiente do que eletrificar os que não têm.",
   AdvisorElectricityTitle: "Energia e Eletrificação",
   AdvisorGreatPeopleContent:
      "Cada vez que você entra em uma nova era de tecnologia, você poderá selecionar uma Figura Histórica daquela era e de cada era anterior. Essas Figuras Históricas dão bônus globais que podem aumentar a produção, ciência, felicidade e muitas outras coisas.<br><br>Esses bônus são permanentes pelo resto do renascimento. Quando você renasce, todas as suas Figuras Históricas se tornam permanentes e seus bônus duram para sempre.<br><br>Escolher a mesma em uma corrida posterior acumulará seus bônus permanentes e de corrida e, quando você renasce com duplicatas, os extras são armazenados e podem ser usados ​​para atualizar o bônus permanente. Isso é acessado no menu <b>Gerenciar Figuras Históricas Permanentes</b> em seu Edifício Residencial.",
   AdvisorGreatPeopleTitle: "Figuras Históricas",
   AdvisorHappinessContent:
      "Felicidade é a mecânica central em CivIdle que limita a expansão. Você ganha felicidade desbloqueando novas tecnologias, avançando para novas eras, construindo maravilhas, de Grandes Pessoas que as fornecem, e algumas outras maneiras que você pode descobrir conforme aprende. <b>Cada novo edifício custa 1 felicidade</b>. Para cada ponto acima/abaixo de 0 felicidade, você ganha um bônus ou penalidade de 2% para seus trabalhadores totais (com limite de -50 e +50 Felicidade). Você pode ver uma análise detalhada da sua felicidade na <b>seção Felicidade do seu Edifício Residencial</b>.",
   AdvisorHappinessTitle: "Mantenha seus Trabalhadores Felizes",
   AdvisorOkay: "Entendi, obrigado!",
   AdvisorScienceContent:
      "Seus trabalhadores ocupados geram ciência, o que permite que você desbloqueie novas tecnologias e avance sua civilização. Você pode acessar o menu de pesquisa de várias maneiras. Clicando no medidor de ciência, acessando suas tecnologias desbloqueáveis ​​em seu Edifício Residencial ou usando o menu 'Exibir'. Tudo isso o levará à árvore tecnológica, mostrando todas as tecnologias, bem como quanta ciência é necessária para cada uma. Se você tiver ciência suficiente para aprender uma nova tecnologia, basta clicar nela e pressionar 'desbloquear' no menu da barra lateral. <b>Cada novo nível e era da tecnologia requer mais e mais ciência, mas você desbloqueará novas e melhores maneiras de obter ciência também.</b>",
   AdvisorScienceTitle: "Descoberta Científica!",
   AdvisorSkipAllTutorials: "Pular todos os tutoriais",
   AdvisorStorageContent:
      "Embora os edifícios tenham uma quantidade razoável de armazenamento, eles podem ficar cheios, especialmente se ficarem ociosos por muito tempo. <b>Quando os edifícios estão cheios, eles não podem mais produzir</b>. Isso nem sempre é um problema, já que você claramente tem um grande estoque, já que o edifício está cheio. Mas manter as coisas produzindo geralmente é melhor.<br><br>Uma maneira de lidar com o armazenamento cheio é por meio de um armazém. Quando você constrói um armazém, obtém um menu de cada produto que descobriu e pode configurar o armazém para puxar quaisquer produtos em qualquer quantidade, desde que o total de todos os produtos esteja dentro do que o armazém pode puxar com base em seu nível e multiplicador de armazenamento.<br><br>Uma maneira fácil de configurar um armazém é marcar cada produto que deseja importar para o armazém e usar os botões 'redistribuir entre os selecionados' para dividir sua taxa de importação e armazenamento igualmente. Se você quiser que os edifícios também possam retirar do armazém, certifique-se de ativar a opção 'exportar abaixo do valor máximo' também.",
   AdvisorStorageTitle: "Armazenamento e Armazéns",
   AdvisorTraditionContent:
      "Algumas maravilhas (Chogha Zanbil, Templo de Luxor, Big Ben) fornecem acesso a um novo conjunto de opções, permitindo que você personalize o caminho da sua corrida. Cada uma permite que você escolha entre 1 de 4 opções para a tradição, religião e ideologia da sua civilização, respectivamente.<br><br>Depois de escolher uma, essa escolha é bloqueada para essa corrida, embora você possa escolher outras em corridas futuras. Uma vez escolhida, cada uma também pode ser atualizada várias vezes, fornecendo os recursos necessários. As recompensas em cada nível são cumulativas, então o Nível 1 dando +1 produção para X e o Nível 2 dando +1 produção para X significa que no Nível 2 você terá +2 produção para X no total.",
   AdvisorTraditionTitle: "Escolhendo Caminhos e Maravilhas Atualizáveis",
   AdvisorWonderContent:
      "Maravilhas são construções especiais que fornecem efeitos globais que podem ter um impacto significativo na sua jogabilidade. Além das funções listadas, todas as Maravilhas também dão +1 de Felicidade. Você precisa ter cuidado, pois <b>Maravilhas exigem MUITOS materiais e também têm uma Capacidade de Construtor maior do que o normal</b>. Isso significa que elas podem facilmente esvaziar seus estoques de insumos necessários, deixando seus outros edifícios morrendo de fome. <b>Você pode ligar e desligar cada entrada livremente</b>, permitindo que você a construa em estágios enquanto armazena materiais suficientes para manter tudo funcionando.",
   AdvisorWonderTitle: "Maravilhas do Mundo",
   AdvisorWorkerContent:
      "Toda vez que um edifício produz ou transporta bens, isso requer trabalhadores. Se você não tiver trabalhadores suficientes disponíveis, alguns edifícios não conseguirão executar esse ciclo. A solução óbvia para isso é aumentar seus trabalhadores totais disponíveis construindo ou atualizando estruturas que produzem trabalhadores (Cabana/Casa/Apartamento/Condomínio).<br><br><b>Esteja ciente, porém, de que os edifícios desligam durante a atualização e não podem fornecer nenhum de seus recursos, o que inclui trabalhadores, então você pode querer atualizar apenas um edifício residencial por vez.</b> Uma boa meta para os estágios iniciais do jogo é manter cerca de 70% dos seus trabalhadores ocupados. Se mais de 70% estiverem ocupados, atualize/construa habitações. Se menos de 70% estiverem ocupados, expanda a produção.",
   AdvisorWorkerTitle: "Gestão de Trabalhadores",
   Aeschylus: "Ésquilo",
   Agamemnon: "Agamêmnon",
   AgeWisdom: "Sabedoria da Era",
   AgeWisdomDescHTML: "Cada nível de Sabedoria da Era fornece <b>um nível equivalente</b> de Figuras Históricas Permanentes elegíveis daquela era - ele pode ser aprimorado com fragmentos de Figuras Históricas Permanentes elegíveis.",
   AgeWisdomGreatPeopleShardsNeeded: "Você precisa de %{amount} mais fragmentos de figuras históricas para a próxima atualização da Sabedoria da Era.",
   AgeWisdomGreatPeopleShardsSatisfied: "Você tem fragmentos de figuras históricas suficiente para a próxima atualização da Sabedoria da Era.",
   AgeWisdomNeedMoreGreatPeopleShards: "Precisa de mais fragmentos de figuras históricas.",
   AgeWisdomNotEligible: "Esta figura histórica não é elegível para a Sabedoria da Era.",
   AgeWisdomSource: "%{age} Sabedoria: %{person}",
   AgeWisdomUpgradeWarningHTMLV3: "Age Wisdom <b>does not carry over</b> when upgrading from Tribune to Quaestor",
   AGreatPersonIsBorn: "Uma Figura Histórica Nasce",
   AircraftCarrier: "Porta-Aviões",
   AircraftCarrierYard: "Estaleiro de Porta-Aviões",
   Airplane: "Avião",
   AirplaneFactory: "Fábrica de Aviões",
   Akitu: "Akitu: Zigurate de Ur e Rio Eufrates se aplicam a edifícios desbloqueados na era atual.",
   AlanTuring: "Alan Turing",
   AlanTuringDesc: "+%{value} Ciência de Trabalhadores Inativos",
   AlbertEinstein: "Albert Einstein",
   Alcohol: "Álcool",
   AldersonDisk: "Disco de Alderson",
   AldersonDiskDesc: "+25 Felicidade. Esta maravilha pode ser melhorada e cada melhoria adicional fornece +5 Felicidade.",
   Alloy: "Liga Metálica",
   Alps: "Alpes",
   AlpsDesc: "Cada 10º nível de uma construção garante +1 de Capacidade de Produção (+1 de Multiplicador de Consumo, +1 de Multiplicador de Produção)",
   Aluminum: "Alumínio",
   AluminumSmelter: "Fundição de Alumínio",
   AmeliaEarhart: "Amelia Earhart",
   American: "Americanos",
   AndrewCarnegie: "Andrew Carnegie",
   AngkorWat: "Angkor Wat",
   AngkorWatDesc: "Todas as construções adjacentes ganham +1 de Multiplicador de Capacidade de Trabalhadores. Proporciona 1000 Trabalhadores",
   AntiCheatFailure: "O ranque da sua conta foi restrito devido a <b>falha na verificação anti-trapaça</b>. Entre em contato com o desenvolvedor se quiser apelar",
   AoiMatsuri: "Aoi Matsuri: O Monte Fuji gera o dobro da distorção",
   Apartment: "Apartamento",
   Aphrodite: "Afrodite",
   AphroditeDescV2: "+1 Multiplicador de Capacidade do Construtor para cada nível ao melhorar edifícios acima do Nível 20. Todas as figuras históricas permanentes da Era Clássica desbloqueadas ganham +1 nível nesta corrida.",
   ApolloProgram: "Programa Apollo",
   ApolloProgramDesc: "Todas as fábricas de foguetes recebem +2 de Produção, Capacidade de Trabalhadores e Multiplicador de Armazenamento. Fábricas de satélites, fábricas de naves espaciais e silos de mísseis nucleares recebem +1 de Multiplicador de Produção para cada fábrica de foguetes adjacente.",
   ApplyToAll: "Aplicar a Todos",
   ApplyToAllBuilding: "Aplicar a Todos(as) %{building}",
   ApplyToBuildingInTile: "Aplicar a Todos(as) %{building} Dentro de %{tile} blocos",
   ApplyToBuildingsToastHTML: "Aplicado com sucesso a <b>%{count} (%{building})</b>",
   Aqueduct: "Aqueduto",
   ArcDeTriomphe: "Arco do Triunfo",
   ArcDeTriompheDescV2: "Every 1 happiness (capped) provides +1 builder capacity to all buildings",
   Archimedes: "Arquimedes",
   Architecture: "Arquitetura",
   Aristophanes: "Aristófanes",
   AristophanesDesc: "+%{value} Felicidade",
   Aristotle: "Aristóteles",
   Arithmetic: "Aritmética",
   Armor: "Armadura",
   Armory: "Arsenal",
   ArtificialIntelligence: "Inteligência Artificial",
   Artillery: "Artilharia",
   ArtilleryFactory: "Fábrica de Artilharia",
   AshokaTheGreat: "Ashoka, o Grande",
   Ashurbanipal: "Assurbanípal",
   Assembly: "Montagem",
   Astronomy: "Astronomia",
   AtomicBomb: "Bomba Atômica",
   AtomicFacility: "Instalação Atômica",
   AtomicTheory: "Teoria Atômica",
   Atomium: "Átomo",
   AtomiumDescV2: "Todos os edifícios que produzem ciência dentro do alcance de 2 blocos recebem +5 de Multiplicador de Produção. Gera ciência que é igual à produção de ciência dentro do alcance de 2 blocos. Quando concluído, gera ciência única equivalente ao custo da tecnologia desbloqueada mais cara.",
   Autocracy: "Autocracia",
   Aviation: "Aviação",
   Babylonian: "Babilônicos",
   BackToCity: "Voltar para a Cidade",
   BackupRecovery: "Recuperação de Backup",
   Bakery: "Padaria",
   Ballistics: "Balística",
   Bank: "Banco",
   Banking: "Bancário",
   BankingAdditionalUpgrade: "Todas as construções que são de nível 10 ou superior ganham +1 de Multiplicador de Armazenamento",
   Banknote: "Cédula",
   BaseCapacity: "Capacidade Base",
   BaseConsumption: "Consumo Base",
   BaseMultiplier: "Multiplicador Base",
   BaseProduction: "Produção Base",
   BastilleDay: "Dia da Bastilha: Dobra o efeito do Centro Pompidou e do Arco do Triunfo. Dobra a geração de Cultura do Monte Saint-Michel",
   BatchModeTooltip: "%{count} construções estão atualmente selecionadas. A melhoria será aplicada a todas as construções selecionadas",
   BatchSelectAllSameType: "Todos do Mesmo Tipo",
   BatchSelectAnyType1Tile: "Qualquer tipo em 1 Espaço",
   BatchSelectAnyType2Tile: "Qualquer Tipo em 2 Espaços",
   BatchSelectAnyType3Tile: "Qualquer Tipo em 3 Espaços",
   BatchSelectSameType1Tile: "Mesmo Tipo em 1 Espaço",
   BatchSelectSameType2Tile: "Mesmo Tipo em 2 Espaços",
   BatchSelectSameType3Tile: "Mesmo Tipo em 3 Espaços",
   BatchSelectSameTypeSameLevel: "Mesmo Tipo, Mesmo Nível",
   BatchSelectThisBuilding: "Esta Construção",
   BatchStateSelectActive: "Active",
   BatchStateSelectAll: "All",
   BatchStateSelectTurnedFullStorage: "Full Storage",
   BatchStateSelectTurnedOff: "Turned Off",
   BatchUpgrade: "Melhoria em Grupo",
   Battleship: "Encouraçado",
   BattleshipBuilder: "Construtor de Encouraçado",
   BigBen: "Big Ben",
   BigBenDesc: "+2 Ciência de Trabalhadores Ocupados. Escolha uma ideologia de império, desbloqueie mais impulso com cada escolha.",
   Biplane: "Biplano",
   BiplaneFactory: "Fábrica de Biplanos",
   Bitcoin: "Bitcoin",
   BitcoinMiner: "Minerador de Bitcoins",
   BlackForest: "Floresta Negra",
   BlackForestDesc: "Ao ser descoberta, revela todas as células de madeira no mapa. Gera madeira em células adjacentes. Todos os edifícios que consomem Madeira ou Lenha recebem +5 no Multiplicador de Produção.",
   Blacksmith: "Ferraria",
   Blockchain: "Blockchain",
   BlueMosque: "Blue Mosque",
   BlueMosqueDesc: "All wonders provide +1 Production, Worker Capacity and Storage Multiplier to adjacent buildings. When constructed next to Hagia Sophia, provide extra +1 Production, Worker Capacity and Storage Multiplier",
   BobHope: "Bob Hope",
   BobHopeDesc: "+%{value} Felicidade",
   Bond: "Título",
   BondMarket: "Mercado de Títulos",
   Book: "Livro",
   BoostCyclesLeft: "Ciclos de Impulso Restantes",
   BoostDescription: "+%{value} %{multipliers} para %{buildings}",
   Borobudur: "Borobudur",
   BorobudurDesc: "Borobudur",
   BranCastle: "Castelo de Bran",
   BranCastleDesc: "Castelo de Bran",
   BrandenburgGate: "Portão de Brandenburgo",
   BrandenburgGateDesc: "Todas as Minas de Carvão e Poços de Petróleo ganham +1 de Multiplicador de Produção, Armazenamento e Capacidade de Trabalhadores. Refinarias de Petróleo ganham +1 de Multiplicador de Produção, Armazenamento e Capacidade de Trabalhadores para cada depósito de petróleo adjacente",
   Bread: "Pão",
   Brewery: "Cervejaria",
   Brick: "Tijolo",
   Brickworks: "Fábrica de Tijolos",
   BritishMuseum: "Museu Britânico",
   BritishMuseumChooseWonder: "Escolha uma Maravilha",
   BritishMuseumDesc: "Após ser construído, pode se transformar em uma maravilha única de outra civilização",
   BritishMuseumTransform: "Transformar",
   Broadway: "Broadway",
   BroadwayCurrentlySelected: "Currently selected",
   BroadwayDesc: "Uma figura histórica da era atual e uma figura histórica da era anterior nascem. Selecione uma figura histórica e dobre o seu efeito",
   BronzeAge: "Idade do Bronze",
   BronzeTech: "Bronze",
   BuddhismLevelX: "Budismo %{level}",
   Build: "Construir",
   BuilderCapacity: "Capacidade de Construtores",
   BuildingColor: "Cor da Construção",
   BuildingColorMatchBuilding: "Copiar Cor de uma Construção",
   BuildingColorMatchBuildingTooltip: "Define a cor dos recursos para a mesma da construção que produz esse recurso. Se múltiplas construções produzem esse recurso, uma aleatória será selecionada",
   BuildingDefaults: "Padrões de Construção",
   BuildingDefaultsCount: "%{count} as propriedades são substituídas no padrão de construção",
   BuildingDefaultsRemove: "Limpar todas as substituições de propriedade",
   BuildingEmpireValue: "Valor de Construção do Império/ Valor de Recursos do Império",
   BuildingMultipliers: "Impulso",
   BuildingName: "Nome",
   BuildingNoMultiplier: "%{building} <b>não é afetado</b> por nenhum multiplicador (produção, capacidade de trabalhadores, armazenamento, etc.)",
   BuildingSearchText: "Digite o nome de uma construção ou recurso para pesquisar",
   BuildingTier: "Tier",
   Cable: "Cabo",
   CableFactory: "Fábrica de Cabos",
   Calendar: "Calendário",
   CambridgeUniversity: "Universidade de Cambridge",
   CambridgeUniversityDesc: "+1 nível de Sabedoria da Era para o Renascimento e eras posteriores",
   CambridgeUniversitySource: "Universidade de Cambridge (%{age})",
   Cancel: "Cancelar",
   CancelAllUpgradeDesc: "Cancel all %{building} upgrades",
   CancelUpgrade: "Cancelar Melhoria",
   CancelUpgradeDesc: "Todos os recursos que já foram transportados vão permanecer no armazenamento",
   Cannon: "Canhão",
   CannonWorkshop: "Oficina de Canhões",
   CannotEarnPermanentGreatPeopleDesc: "Por essa ser uma tentativa teste, figuras históricas permanentes não podem ser obtidas",
   Capitalism: "Capitalismo",
   Cappadocia: "Cappadocia",
   CappadociaDesc: "All buildings within 3 tile range get +1 Production, Worker Capacity and Storage Multiplier for every level above Level 30",
   Car: "Carro",
   Caravansary: "Caravana",
   CaravansaryDesc: "Permite trocar recursos com outros jogadores e garante armazenamento extra",
   Caravel: "Caravela",
   CaravelBuilder: "Construtor de Caravelas",
   CarFactory: "Fábrica de Carros",
   CarlFriedrichGauss: "Carl Friedrich Gauss",
   CarlFriedrichGaussDesc: "+%{idle} Ciência de Trabalhadores Inativos. +%{busy} Ciência de Trabalhadores Ocupados",
   CarlSagan: "Carl Sagan",
   Census: "Censo",
   CentrePompidou: "Centro Pompidou",
   CentrePompidouDesc:
      "Após ser construído, todos os edifícios recebem +1 Produção e +2 Multiplicador de Armazenamento. A maravilha persistirá se a corrida atual alcançar a Idade da Informação e a próxima corrida for de uma civilização diferente. A maravilha recebe +1 nível a cada renascimento para cada corrida que alcançar a Idade da Informação com uma civilização única. Cada nível fornece +1 Produção e +2 Multiplicador de Armazenamento. O valor desta maravilha é excluído do valor total do império e o Museu Britânico não pode ser transformado nesta maravilha",
   CentrePompidouWarningHTML: "Centre Pompidou will disappear if you rebirth as <b>%{civ}</b>",
   CerneAbbasGiant: "Cerne Abbas Giant",
   CerneAbbasGiantDesc: "A great person of the current age is born when a wonder is constructed",
   ChangePlayerHandle: "Alterar",
   ChangePlayerHandleCancel: "Cancelar",
   ChangePlayerHandledDesc: "Escolha um nome de jogador único de 5 ~ 16 caracteres. Seu nome de jogador deve conter apenas letras e números",
   Chariot: "Carroça",
   ChariotWorkshop: "Oficina de Carroças",
   Charlemagne: "Carlos Magno",
   CharlesDarwin: "Charles Darwin",
   CharlesDarwinDesc: "+%{value} Ciência de Trabalhadores Ocupados",
   CharlesMartinHall: "Charles Martin Hall",
   CharlesParsons: "Charles Parsons",
   CharlieChaplin: "Charlie Chaplin",
   CharlieChaplinDesc: "+%{value} Felicidade",
   Chat: "Chat",
   ChatChannel: "Canal do Chat",
   ChatChannelLanguage: "Linguagem",
   ChatHideLatestMessage: "Esconder Conteúdo da Última Mensagem",
   ChatNoMessage: "Sem Mensagens no Chat",
   ChatReconnect: "Desconectado(a), reconectando...",
   ChatSend: "Enviar",
   CheckInAndExit: "Check In And Exit",
   CheckInCloudSave: "Check In Save",
   CheckOutCloudSave: "Check Out Save",
   Cheese: "Queijo",
   CheeseMaker: "Fábrica de Queijos",
   Chemistry: "Química",
   ChesterWNimitz: "Chester W. Nimitz",
   ChichenItza: "Chichén-Itzá",
   ChichenItzaDesc: "Todas as construções adjacentes ganham +1 de Multiplicador de Produção, Armazenamento e Capacidade de Trabalhadores",
   Chinese: "Chineses",
   ChoghaZanbil: "Tchogha Zanbil",
   ChoghaZanbilDescV2: "Escolha uma tradição do império, desbloqueie mais impulsos com cada escolha",
   ChooseGreatPersonChoicesLeft: "Você tem %{count} escolhas restantes",
   ChristianityLevelX: "Cristianismo %{level}",
   Church: "Igreja",
   CircusMaximus: "Circo Máximo",
   CircusMaximusDescV2: "+5 Felicidade. Todas as Associações de Músicos, Associações de Escritores e Associações de Pintores ganham +1 de Multiplicador de Produção e Armazenamento",
   CityState: "Cidade-Estado",
   CityViewMap: "Cidade",
   CivGPT: "CivGPT",
   CivIdle: "CivIdle",
   CivIdleInfo: "Orgulhosamente apresentado por Fish Pond Studio",
   Civilization: "Civilization",
   CivilService: "Serviço Civil",
   CivOasis: "CivOasis",
   CivTok: "CivTok",
   ClaimedGreatPeople: "Figuras Históricas Reivindicadas",
   ClaimedGreatPeopleTooltip: "Você tem %{total} figuras históricas no renascimento, %{claimed} delas já foram reivindicadas",
   ClassicalAge: "Período Clássico",
   ClearAfterUpdate: "Redefinir Trocas Após Atualização de Mercado",
   ClearSelected: "Redefinir Selecionados",
   ClearSelection: "Desselecionar",
   ClearTransportPlanCache: "Limpar cache do plano de transporte",
   Cleopatra: "Cleopatra",
   CloneFactory: "Fábrica de Clones",
   CloneFactoryDesc: "Clonar quaisquer recursos",
   CloneFactoryInputDescHTML: "A Fábrica de Clones só pode clonar <b>%{res}</b> transportado diretamente de <b>%{buildings}</b>",
   CloneLab: "Laboratório de Clones",
   CloneLabDesc: "Converta quaisquer recursos em Ciência",
   CloneLabScienceMultiplierHTML: "Multiplicadores de produção que <b>se aplicam apenas a edifícios de produção científica</b> (por exemplo, multiplicadores de produção do Atomium) <b>não se aplicam</b> ao Clone Lab",
   Cloth: "Tecido",
   CloudComputing: "Computação em Nuvem",
   CloudSaveRefresh: "Refresh",
   CloudSaveReturnToGame: "Retornar ao Jogo",
   CNTower: "Torre CN",
   CNTowerDesc:
      "Todos os estúdios de cinema, estações de rádio e estações de TV estão isentos de -1 de Felicidade. Todas as construções desbloqueadas nas Guerras Mundiais e na Guerra Fria ganham +N de Produção, Capacidade de Trabalhadores e Multiplicador de Armazenamento. N = Diferença entre a era e o tier da construção",
   Coal: "Carvão",
   CoalMine: "Mina de Carvão",
   CoalPowerPlant: "Usina de Carvão",
   Coin: "Moeda",
   CoinMint: "Casa da Moeda",
   ColdWarAge: "Guerra Fria",
   CologneCathedral: "Cologne Cathedral",
   CologneCathedralDesc:
      "When constructed, generate one-time science equivalent to the cost of the most expensive technology in the current age. All buildings that produce science (excluding Clone Lab) get +1 Production Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Production Multiplier to all buildings that produce science (excluding Clone Lab)",
   Colonialism: "Colonialismo",
   Colosseum: "Coliseu",
   ColosseumDescV2: "Oficinas de Carruagens são isentas de -1 felicidade. Consome 10 carruagens e produz 10 felicidade. Cada idade desbloqueada dá 2 felicidades extras.",
   ColossusOfRhodes: "Colosso de Rodes",
   ColossusOfRhodesDesc: "Todas as construções adjacentes que não produzem trabalhadores ganham +1 de Felicidade",
   Combustion: "Combustão",
   Commerce4UpgradeHTMLV2: "Quando desbloqueado, todos os <b>bancos adjacentes</b> recebem atualização gratuita para o <b>nível 30</b>",
   CommerceLevelX: "Comércio %{level}",
   Communism: "Communismo",
   CommunismLevel4DescHTML: "Nascem uma figura histórica da <b>Era Industrial</b> e uma figura histórica da <b>Era das Guerras Mundiais</b>.",
   CommunismLevel5DescHTML: "Uma figura histórica da <b>Era da Guerra Fria</b> nasce. Ao entrar em uma nova era, ganhe <b>2 figuras históricas adicionais</b> daquela era.",
   CommunismLevelX: "Nível Communismo %{level}",
   Computer: "Computador",
   ComputerFactory: "Fábrica de Computadores",
   ComputerLab: "Laboratório de Informática",
   Concrete: "Concreto",
   ConcretePlant: "Fábrica de Cimento",
   Condo: "Condomínio",
   ConfirmDestroyResourceContent: "Você está prestes a destruir %{amount} %{resource}. Isso não pode ser desfeito",
   ConfirmNo: "Não",
   ConfirmYes: "Sim",
   Confucius: "Confúcio",
   ConfuciusDescV2: "+%{value} Ciência de Todos os Trabalhadores se mais de 50% dos trabalhadores estiverem ocupados e menos de 50% dos trabalhadores ocupados trabalharem em transporte",
   ConnectToADevice: "Connect To A Device",
   Conservatism: "Conservadorismo",
   ConservatismLevelX: "Nível Conservadorismo %{level}",
   Constitution: "Constituição",
   Construction: "Construção",
   ConstructionBuilderBaseCapacity: "Capacidade Base",
   ConstructionBuilderCapacity: "Capacidade de Construtores",
   ConstructionBuilderMultiplier: "Multiplicador de Capacidade",
   ConstructionBuilderMultiplierFull: "Multiplicador de Capacidade de Construtores",
   ConstructionCost: "Custo de Construção: %{cost}",
   ConstructionDelivered: "Entregue",
   ConstructionPriority: "Prioridade de Construção",
   ConstructionProgress: "Progresso",
   ConstructionResource: "Recurso",
   Consume: "Consumo",
   ConsumeResource: "Consumo: %{resource}",
   ConsumptionMultiplier: "Multiplicador de Consumo",
   ContentInDevelopment: "Conteúdo em Desenvolvimento",
   ContentInDevelopmentDesc: "O conteúdo deste jogo ainda está em desenvolvimento e estará disponível em uma futura atualização, fique ligado!",
   Copper: "Cobre",
   CopperMiningCamp: "Mina de Cobre",
   CosimoDeMedici: "Cosimo de' Medici",
   Cotton: "Algodão",
   CottonMill: "Tear de algodão",
   CottonPlantation: "Plantação de Algodão",
   Counting: "Contagem",
   Courthouse: "Tribunal",
   CristoRedentor: "Cristo Redentor",
   CristoRedentorDesc: "Todos os edifícios dentro do alcance de 2 blocos estão isentos de - 1 felicidade.",
   CrossPlatformAccount: "Conta entre Plataformas",
   CrossPlatformConnect: "Conectar",
   CrossPlatformSave: "Salvar Entre Plataformas",
   CrossPlatformSaveLastCheckIn: "Última Verificação",
   CrossPlatformSaveStatus: "Status Atual",
   CrossPlatformSaveStatusCheckedIn: "Verificado",
   CrossPlatformSaveStatusCheckedOut: "Desconectado em %{platform}",
   CrossPlatformSaveStatusCheckedOutTooltip: "Seu salvamento entre plataformas foi desconectado em outra plataforma. Você precisa reconectar nela antes de poder desconectar nesta plataforma.",
   Cultivation4UpgradeHTML: "Uma figura histórica da <b>Idade do Renascimento</b> nasce",
   CultivationLevelX: "Cultivo %{level}",
   Culture: "Cultura",
   Culus: "Cülus: Double the effect of Cappadocia. Mount Ararat's effect becomes based on square root of Effective Great People Level, instead of cubic root",
   CurrentLanguage: "Português",
   CurrentPlatform: "Plataforma Atual",
   CursorBigOldFashioned: "3D (Grande)",
   CursorOldFashioned: "3D",
   CursorStyle: "Estilo do Cursor",
   CursorStyleDescHTML: "Mude o estilo do cursor. <b>Requer reiniciar o jogo para ter efeito</b>",
   CursorSystem: "Sistema",
   Cycle: "Ciclo",
   CyrusII: "Ciro II",
   DairyFarm: "Fazenda de Laticínios",
   DefaultBuildingLevel: "Nível de Construção Padrão",
   DefaultConstructionPriority: "Prioridade de Construção Padrão",
   DefaultProductionPriority: "Prioridade de Produção Padrão",
   DefaultStockpileMax: "Estoque Máximo Padrão",
   DefaultStockpileSettings: "Capacidade de Entrada de Estoque Padrão",
   DeficitResources: "Recursos em Déficit",
   Democracy: "Democracia",
   DemolishAllBuilding: "Demolir Todos os %{building} Dentro de %{tile} Espaço",
   DemolishAllBuildingConfirmContent: "Você tem certeza de que deseja demolir %{count} %{name}?",
   DemolishAllBuildingConfirmTitle: "Demolir %{count} Edifício(s)?",
   DemolishBuilding: "Demolir Construção",
   DennisRitchie: "Dennis Ritchie",
   Deposit: "Depósito",
   DepositTileCountDesc: "%{count} espaço(s) de %{deposit} podem ser encontrados em %{city}",
   Dido: "Dido",
   Diplomacy: "Diplomacia",
   DistanceInfinity: "Ilimitado",
   DistanceInTiles: "Distância (Em Espaços)",
   DolmabahcePalace: "Dolmabahçe Palace",
   Drilling: "Perfuração",
   DukeOfZhou: "Duque de Zhou",
   DuneOfPilat: "Duna de Pilat",
   DuneOfPilatDesc: "Em cada idade, dobre a sabedoria da era anterior",
   DynamicMultiplierTooltip: "Este multiplicador é dinâmico: não afetará os trabalhadores e o armazenamento.",
   Dynamite: "Dinamite",
   DynamiteWorkshop: "Oficina de Dinamites",
   DysonSphere: "Dyson Sphere",
   DysonSphereDesc: "Todos os edifícios recebem +5 de Multiplicador de Produção. Esta maravilha pode ser melhorada e cada melhoria adicional fornece +1 de Multiplicador de Produção para todos os edifícios.",
   EasterBunny: "Easter Bunny",
   EasterBunnyDesc: "Once constructed, 10% of the extra Great People at Rebirth from this run will carry forward to the next run and are born after building the Easter Bunny in the new run. This wonder can only be constructed during April",
   EastIndiaCompany: "East India Company",
   EastIndiaCompanyDescV2:
      "This wonder accumulates the total value of your completed player trade transactions. For every 2,000 trade value, all buildings adjacent to caravansaries get a +0.5 Production Multiplier for 1 cycle. This wonder can be upgraded and each upgrade provides an additional +0.5 Production Multiplier. A trade transaction counts when you either fulfill another player's trade request or when your own trade request is fulfilled. Multiple boosts stack by extending the duration",
   Education: "Educação",
   EffectiveGreatPeopleLevel: "Effective Great People Level",
   EffectiveGreatPeopleLevelDesc: "Effective great people level is the sum of all permanent great people level and age wisdom level. It measures the effect boost provided by great people and age wisdom",
   Egyptian: "Egípcios",
   EiffelTower: "Torre Eiffel",
   EiffelTowerDesc: "Todas as siderúrgicas adjacentes ganham +N Multiplicador de Produção, Armazenamento e Trabalhadores. N = Número de siderúrgicas adjacentes",
   Elbphilharmonie: "Elbphilharmonie",
   ElbphilharmonieDesc: "All buildings within 3 tile range get +1 Production Multiplier for each adjacent working building that has different tier",
   Electricity: "Eletricidade",
   Electrification: "Eletrificação",
   ElectrificationPowerRequired: "Energia Elétrica Necessária",
   ElectrificationStatusActive: "Ativa",
   ElectrificationStatusDesc: "Tanto construções que requerem energia quanto construções que não requerem energia podem ser eletrificadas. No entanto, construções que requerem energia fornecem maior eficiência de eletrificação",
   ElectrificationStatusNoPowerV2: "Sem Energia Suficiente",
   ElectrificationStatusNotActive: "Inativa",
   ElectrificationStatusV2: "Status de Eletrificação",
   ElectrificationUpgrade: "Desbloqueia eletrificação. Permite que construções consumam energia para impulsionar sua produção",
   Electrolysis: "Eletrólise",
   ElvisPresley: "Elvis Presley",
   ElyseePalace: "Palácio do Eliseu",
   EmailDeveloper: "E-mail do Desenvolvedor",
   Embassy: "Embaixada",
   EmperorWuOfHan: "Imperador Wu de Han",
   EmpireValue: "Valor do Império",
   EmpireValueByHour: "Valor do Império por Hora",
   EmpireValueFromBuilding: "Valor do Império proveniente das Construções",
   EmpireValueFromBuildingsStat: "Das Construções",
   EmpireValueFromResources: "Dos Recursos",
   EmpireValueFromResourcesStat: "Dos Recursos",
   EmpireValueIncrease: "Valor do Império Aumentado",
   EmptyTilePageBuildLastBuilding: "Construir Última Construção",
   EndConstruction: "Parar Construção",
   EndConstructionDescHTML: "Quando você para a construção, todos os recursos que já foram usados <b>não serão devolvidos</b>",
   Engine: "Motor",
   Engineering: "Engenharia",
   English: "Ingleses",
   Enlightenment: "Iluminismo",
   Enrichment: "Enriquecimento",
   EnricoFermi: "Enrico Fermi",
   EstimatedTimeLeft: "Tempo Restante Estimado",
   EuphratesRiver: "Rio Eufrates",
   EuphratesRiverDesc:
      "Cada 10% de trabalhadores ocupados que estão em produção (não transportando) fornece +1 Multiplicador de Produção para todos os edifícios que não produzem trabalhadores (máx. = número de eras desbloqueadas / 2). Quando o Hanging Garden é construído próximo a ele, o Hanging Garden recebe +1 efeito para cada era após o Hanging Garden ser desbloqueado. Quando descoberto, gera água em todos os blocos adjacentes que não têm depósitos.",
   ExpansionLevelX: "Expansão %{level}",
   Exploration: "Exploração",
   Explorer: "Explorador",
   ExplorerRangeUpgradeDesc: "Aumentar o alcance do explorador para %{range}",
   ExploreThisTile: "Enviar um Explorador",
   ExploreThisTileHTML: "Um explorador irá explorar <b>este espaço e seus espaços adjacentes</b>. Exploradores são gerados em %{name}. Você tem %{count} exploradores restantes",
   ExtraGreatPeople: "%{count} Figuras Históricas Extras",
   ExtraGreatPeopleAtReborn: "Figuras Históricas Extras ao Renascer",
   ExtraTileInfoType: "Informação Extra do Espaço",
   ExtraTileInfoTypeDesc: "Escolha quais informações são exibidas abaixo de cada espaço",
   ExtraTileInfoTypeEmpireValue: "Valor do Império",
   ExtraTileInfoTypeNone: "Nenhum",
   ExtraTileInfoTypeStoragePercentage: "Porcentagem de Armazenamento",
   Faith: "Fé",
   Farming: "Cultivo",
   FavoriteBuildingAdd: "Adicionar aos Favoritos",
   FavoriteBuildingEmptyToast: "Você não tem nenhuma construção favorita",
   FavoriteBuildingRemove: "Remover dos Favoritos",
   FeatureRequireQuaestorOrAbove: "Este recurso requer classificação de Questor ou superior",
   Festival: "Festival",
   FestivalCycle: "Ciclo do Festival",
   FestivalTechTooltipV2: "Positive Happiness (max. 50) is converted into festival points. For every %{point} festival points, your empire enters a festival cycle, granting a significant map-specific boost. The festival on this map is %{desc}",
   FestivalTechV2: "Unlock festival - positive Happiness (max. 50) is converted into festival points. For every %{point} festival points, your empire enters a festival cycle, granting a significant map-specific boost",
   Feudalism: "Feudalismo",
   Fibonacci: "Fibonacci",
   FibonacciDescV2: "+%{idle} Ciência de Trabalhadores Inativos. +%{busy} Ciência de Trabalhadores Ocupados. O custo de melhoria permanente de Fibonacci segue a sequência de Fibonacci",
   FighterJet: "Caça (aeronave)",
   FighterJetPlant: "Fabrica de Caças",
   FilterByAge: "Filtrar por Era",
   FinancialArbitrage: "Financial Arbitrage",
   FinancialLeverage: "Alavancagem Financeira",
   Fire: "Fogo",
   Firearm: "Arma de Fogo",
   FirstTimeGuideNext: "Próximo",
   FirstTimeTutorialWelcome: "Bem Vindo ao CivIdle!",
   FirstTimeTutorialWelcome1HTML:
      "Bem-vindo ao CivIdle. Neste jogo, você comandará seu próprio império: <b>gerenciará produções, desbloqueará tecnologias, negociará recursos com outros jogadores, obterá figuras históricas e construirá maravilhas do mundo</b>.<br><br>Arraste o mouse para se movimentar. Use a roda de rolagem para aumentar ou diminuir o zoom. Clique em um bloco vazio para construir novos edifícios, clique em um edifício para inspecioná-lo.<br><br>Certos edifícios, como Pedreira e Madereira, precisam ser construídos em cima do bloco de recursos. Recomendo colocar uma Cabana, que fornece trabalhadores, ao lado da névoa - o edifício levará algum tempo para ser construído. Após a conclusão, ele revelará a névoa próxima.",
   FirstTimeTutorialWelcome2HTML:
      "Edifícios podem ser melhorados - custa recursos e leva tempo. Quando um edifício está sendo melhorado, <b>ele não produzirá mais</b>. Isso inclui edifícios que fornecem trabalhadores, <b>então nunca melhore todos os seus edifícios ao mesmo tempo!</b><br><br>À medida que seu império cresce, você obterá mais ciência e desbloqueará novas tecnologias. Eu lhe contarei mais sobre isso quando chegarmos lá, mas você pode ir em Exibir -> Pesquisar para dar uma olhada rápida<br><br>",
   FirstTimeTutorialWelcome3HTML: "Agora que você sabe todos os fundamentos do jogo, pode começar a construir seu império. Mas antes de eu deixar você ir, você deve <b>escolher um nome de jogador</b> e dizer oi no chat do jogo. Temos uma comunidade incrivelmente útil: se você se perder, não tenha medo de perguntar!",
   Fish: "Peixe",
   FishPond: "Viveiro de Peixes",
   FlorenceNightingale: "Florence Nightingale",
   FlorenceNightingaleDesc: "+%{value} Felicidade",
   Flour: "Farinha",
   FlourMill: "Moinho de Farinha",
   FontSizeScale: "Escala de Tamanho da Fonte",
   FontSizeScaleDescHTML: "Mude a escala do tamanho da fonte da interface do jogo. <b>Configurar a escala maior que 1x pode quebrar alguns layouts da interface</b>",
   ForbiddenCity: "Cidade Proibida",
   ForbiddenCityDesc: "Todas as Fábricas de Papel, Associações de Escritores e Gráficas ganham +1 de Multiplicador de Produção, Multiplicador de Capacidade de Trabalhadores e Multiplicador de Armazenamento",
   Forex: "Forex",
   ForexMarket: "Mercado Forex",
   FrankLloydWright: "Frank Lloyd Wright",
   FrankLloydWrightDesc: "+%{value} Multiplicador de Capacidade de Construtores",
   FrankWhittle: "Frank Whittle",
   FreeThisWeek: "Grátis Esta Semana",
   FreeThisWeekDescHTMLV2: "<b>Every week</b>, one of the premium civilizations is free to play. This week's free civilization is <b>%{city}</b>",
   French: "Franceses",
   Frigate: "Fragata",
   FrigateBuilder: "Construtor de Fragatas",
   Furniture: "Móvel",
   FurnitureWorkshop: "Oficina de Móveis",
   Future: "Futuro",
   GabrielGarciaMarquez: "Gabriel García Márquez",
   GabrielGarciaMarquezDesc: "+%{value} Felicidade",
   GalileoGalilei: "Galileu Galilei",
   GalileoGalileiDesc: "+%{value} Ciência de Trabalhadores Inativos",
   Galleon: "Galeão",
   GalleonBuilder: "Construtor de Galeão",
   Gameplay: "Jogabilidade",
   Garment: "Vestuário",
   GarmentWorkshop: "Costureiro",
   GasPipeline: "Gasoduto",
   GasPowerPlant: "Usina de Energia a Gás",
   GatlingGun: "Metralhadora",
   GatlingGunFactory: "Fábrica de Metralhadoras",
   Genetics: "Genética",
   Geography: "Geografia",
   GeorgeCMarshall: "George C. Marshall",
   GeorgeWashington: "George Washington",
   GeorgiusAgricola: "Georgius Agricola",
   German: "Germânicos",
   Glass: "Vidro",
   Glassworks: "Vidraria",
   GlobalBuildingDefault: "Padrão de Construção Global",
   Globalization: "Globalização",
   GoBack: "Voltar",
   Gold: "Ouro",
   GoldenGateBridge: "Ponte Golden Gate",
   GoldenGateBridgeDesc: "Todas as plataformas de energia recebem +1 multiplicador de produção. Fornece energia para todos os blocos dentro do alcance de 2 blocos.",
   GoldenPavilion: "Golden Pavilion",
   GoldenPavilionDesc: "Todos os edifícios dentro do alcance de 3 blocos ganham +1 Multiplicador de Produção para cada edifício adjacente que produz qualquer um dos seus recursos consumidos (excluindo Laboratório de Clones, Fábrica de Clones e o edifício não pode ser desligado).",
   GoldMiningCamp: "Mina de Ouro",
   GordonMoore: "Gordon Moore",
   GrandBazaar: "Grande Bazar",
   GrandBazaarDesc: "Controle todos os mercados em um só lugar! Todas as construções adjacentes ganham +5 Multiplicador de Armazenamento",
   GrandBazaarFilters: "Filtros",
   GrandBazaarFilterWarningHTML: "Você deve selecionar um filtro antes que quaisquer negociações de mercado sejam exibidas",
   GrandBazaarFilterYouGet: "Você Recebe",
   GrandBazaarFilterYouPay: "Você Paga",
   GrandBazaarSeach: "Search",
   GrandBazaarSearchGet: "Get",
   GrandBazaarSearchPay: "Pay",
   GrandBazaarTabActive: "Ativo",
   GrandBazaarTabTrades: "Negociações",
   GrandCanyon: "Grand Canyon",
   GrandCanyonDesc: "Buildings unlocked in the current age get +2 Production Multiplier. Double the effect of J.P. Morgan",
   GraphicsDriver: "Driver Gráfico: %{driver}",
   GreatDagonPagoda: "Pagode Shwedagon",
   GreatDagonPagodaDescV2: "Todos os pagodas estão isentos de -1 felicidade. Gere ciência com base na produção de fé de todos os pagodas.",
   GreatMosqueOfSamarra: "Grande Mesquita de Samarra",
   GreatMosqueOfSamarraDescV2: "+1 alcance de visão das construções. Revela 5 espaços de depósitos não explorados aleatórios e constrói um edifício de extração de recursos nível 10 em cada um",
   GreatPeople: "Figuras Históricas",
   GreatPeopleEffect: "Efeito",
   GreatPeopleFilter: "Digite o nome ou a era para filtrar figuras históricas",
   GreatPeopleName: "Nome",
   GreatPeoplePermanentColumn: "Permanente",
   GreatPeoplePermanentShort: "Permanente",
   GreatPeoplePickPerRoll: "Figuras Históricas Selecionadas Por Vez",
   GreatPeopleThisRun: "Figuras Históricas desta Tentativa",
   GreatPeopleThisRunColumn: "Esta Tentativa",
   GreatPeopleThisRunShort: "Esta Tentativa",
   GreatPersonLevelRequired: "Nível de Figura Histórica Permanente Necessário",
   GreatPersonLevelRequiredDescV2: "%{city} civilization requires %{required} permanent great people levels. You currently have %{current}",
   GreatPersonPromotionPromote: "Promover",
   GreatPersonThisRunEffectiveLevel: "Você atualmente tem %{count} %{person} desta tentativa. Uma %{person} adicional terá 1/%{effect} do efeito",
   GreatPersonWildCardBirth: "Nascimento",
   GreatSphinx: "Grande Esfinge",
   GreatSphinxDesc: "Todas as construções de Tier II ou acima dentro de 2 espaços ganham +N Consumo, Multiplicador de Produção. N = Número de suas construções adjacentes do mesmo tipo",
   GreatWall: "Grande Muralha",
   GreatWallDesc:
      "Todas as construções dentro de 1 espaço ganham +N Produção, Capacidade de Trabalhadores e Multiplicador de Armazenamento. N = número das diferentes eras entre a era atual e a era em que a construção foi desbloqueada pela primeira vez. Quando construída ao lado da Cidade Proibida, o alcance aumenta para 2 espaços",
   GreedyTransport: "Construção/Atualização Transporte Ganancioso",
   GreedyTransportDescHTML: "Isso fará com que os edifícios continuem transportando recursos mesmo que tenham recursos suficientes para a atualização atual, o que pode tornar a atualização de vários níveis <b>mais rápida</b>, mas acabará transportando <b>mais recursos do que o necessário</b>",
   Greek: "Gregos",
   GrottaAzzurra: "Gruta Azul",
   GrottaAzzurraDescV2: "Quando descoberta, todos os seus edifícios de Tier I recebem +5 de Nível e +1 de Produção, Capacidade de Trabalhadores e Multiplicador de Armazenamento.",
   Gunpowder: "Pólvora",
   GunpowderMill: "Usina de Pólvora",
   GuyFawkesNightV2: "Noite de Guy Fawkes: A Companhia das Índias Orientais fornece o dobro do Multiplicador de Produção para edifícios adjacentes a caravançarás. A Tower Bridge gera grandes pessoas 20% mais rápido",
   HagiaSophia: "Santa Sofia",
   HagiaSophiaDescV2: "5 Felicidade. Edifícios com 0% de Capacidade de Produção estão isentos de -1 de felicidade. Durante o bootstrap do jogo, forneça felicidade extra para evitar a interrupção da produção.",
   HallOfFame: "Hall da Fama",
   HallOfSupremeHarmony: "Salão da Suprema Harmonia",
   Hammurabi: "Hamurabi",
   HangingGarden: "Jardins Suspensos",
   HangingGardenDesc: "+1 de Multiplicador de Capacidade de Construtores. Aquedutos adjacentes ganham +1 de Multiplicador de Capacidade de Produção, Armazenamento e Trabalhadores",
   Happiness: "Felicidade",
   HappinessFromBuilding: "De Construções (exceto Maravilhas)",
   HappinessFromBuildingTypes: "De Tipos de Construção Devidamente Abastecidas",
   HappinessFromHighestTierBuilding: "Da Construção de Maior Tier em Funcionamento",
   HappinessFromUnlockedAge: "Por Era Desbloqueada",
   HappinessFromUnlockedTech: "Por Tecnologia Desbloqueada",
   HappinessFromWonders: "De Maravilhas (incluindo Naturais)",
   HappinessUncapped: "Felicidade (Sem Limite)",
   HarryMarkowitz: "Harry Markowitz",
   HarunAlRashid: "Harun al-Rashid",
   Hatshepsut: "Hatshepsut",
   HatshepsutTemple: "Templo Mortuário de Hatshepsut",
   HatshepsutTempleDesc: "Revela todos os depósitos de água no mapa quando construído. Campos de Trigo ganham +1 de Multiplicador de Produção para cada depósito de água adjacente",
   Headquarter: "Sede",
   HedgeFund: "Fundo Hedge",
   HelpMenu: "Ajuda",
   HenryFord: "Henry Ford",
   Herding: "Pastoreio",
   Herodotus: "Heródoto",
   HighlightBuilding: "Highlight %{building}",
   HimejiCastle: "Castelo Himeji",
   HimejiCastleDesc: "Todos os Construtores de Caravelas, Construtores de Galeões e Construtores de Fragata ganham +1 de Multiplicador de Produção, Multiplicador de Capacidade de Trabalhadores e Multiplicador de Armazenamento",
   Hollywood: "Hollywood",
   HollywoodDesc: "+5 Felicidade. +1 Felicidade para cada construção bem abastecida que consuma ou produza cultura dentro de um alcance de 2 espaços",
   HolyEmpire: "Sacro Império",
   Homer: "Homero",
   Honor4UpgradeHTML: "Dobre o efeito de <b>Zheng He</b> (Figura Histórica)",
   HonorLevelX: "Honra %{level}",
   Horse: "Cavalo",
   HorsebackRiding: "Cavalgar",
   House: "Casa",
   Housing: "Habitação",
   Hut: "Cabana",
   HydroDam: "Hidroelétrica",
   Hydroelectricity: "Hidroeletricidade",
   HymanGRickover: "Hyman G. Rickover",
   IdeologyDescHTML: "Escolha entre <b>Liberalismo, Conservadorismo, Socialismo ou Comunismo</b> como sua ideologia de império. Você <b>não pode trocar de ideologia</b> depois que ela for escolhida. Você pode desbloquear mais impulso dentro de cada ideologia.",
   IMPei: "I. M. Pei",
   IMPeiDesc: "+%{value} Multiplicador de Capacidade de Construtores",
   Imperialism: "Imperialismo",
   ImperialPalace: "Imperial Palace",
   IndustrialAge: "Era Industrial",
   InformationAge: "Era da Informação",
   InputResourceForCloning: "Entrada de Recurso para clonagem",
   InternationalSpaceStation: "Estação Espacial Internacional",
   InternationalSpaceStationDesc: "Todos os edifícios recebem +5 Multiplicador de Armazenamento. Esta maravilha pode ser atualizada e cada atualização adicional fornece +1 Multiplicador de Armazenamento para todos os edifícios.",
   Internet: "Internet",
   InternetServiceProvider: "Provedor de Serviços de Internet",
   InverseSelection: "Inverso",
   Iron: "Ferro",
   IronAge: "Idade do Ferro",
   Ironclad: "Couraçado",
   IroncladBuilder: "Construtor de Couraçados",
   IronForge: "Forja de Ferramentas",
   IronMiningCamp: "Mina de Ferro",
   IronTech: "Ferro",
   IsaacNewton: "Isaac Newton",
   IsaacNewtonDescV2: "+%{value} Ciência de Todos os Trabalhadores se mais de 50% dos trabalhadores estiverem ocupados e menos de 50% dos trabalhadores ocupados trabalharem em transporte",
   IsambardKingdomBrunel: "Isambard Kingdom Brunel",
   IsidoreOfMiletus: "Isidoro de Mileto",
   IsidoreOfMiletusDesc: "+%{value} Multiplicador de Capacidade de Construtores",
   Islam5UpgradeHTML: "Quando desbloqueado, gera ciência única equivalente ao custo da tecnologia <b>Industrial</b> mais cara.",
   IslamLevelX: "Islamismo %{level}",
   ItsukushimaShrine: "Santuário de Itsukushima",
   ItsukushimaShrineDescV2: "Quando todas as tecnologias dentro de uma era são desbloqueadas, gere ciência única equivalente ao custo da tecnologia mais barata na próxima era.",
   JamesWatson: "James Watson",
   JamesWatsonDesc: "+%{value} Ciência de Trabalhadores Ocupados",
   JamesWatt: "James Watt",
   Japanese: "Japoneses",
   JetPropulsion: "Propulsão a Jato",
   JohannesGutenberg: "Johannes Gutenberg",
   JohannesKepler: "Johannes Kepler",
   JohnCarmack: "John Carmack",
   JohnDRockefeller: "John D. Rockefeller",
   JohnMcCarthy: "John McCarthy",
   JohnVonNeumann: "John von Neumann",
   JohnVonNeumannDesc: "+%{value} Ciência de Trabalhadores Ocupados",
   JoinDiscord: "Entre no Discord",
   JosephPulitzer: "Joseph Pulitzer",
   Journalism: "Jornalismo",
   JPMorgan: "J.P. Morgan",
   JRobertOppenheimer: "J. Robert Oppenheimer",
   JuliusCaesar: "Júlio César",
   Justinian: "Justiniano",
   Kanagawa: "Kanagawa",
   KanagawaDesc: "Todas as figuras históricas da era atual ganham um nível adicional para esta corrida (excluindo Zenobia).",
   KarlMarx: "Karl Marx",
   Knight: "Cavaleiro",
   KnightCamp: "Acampamento de Cavaleiros",
   Koti: "Koti",
   KotiInStorage: "Koti In Storage",
   KotiProduction: "Koti Production",
   LandTrade: "Comércio entre Jogadores",
   Language: "Idioma",
   Lapland: "Lapônia",
   LaplandDesc: "Ao ser descoberta, revela todo o mapa. Todos os edifícios em um raio de 2 espaços recebem +5 de Multiplicador de Produção. Esta maravilha natural só pode ser descoberta em dezembro.",
   LargeHadronCollider: "Grande Colisor de Hádrons",
   LargeHadronColliderDescV2: "Todas as figuras históricas da Era da Informação ganham +2 nível para esta corrida. Esta maravilha pode ser atualizada e cada atualização adicional fornece +1 nível para todas as figuras históricas da Era da Informação para esta corrida.",
   Law: "Lei",
   Lens: "Lente",
   LensWorkshop: "Oficina de Lentes",
   LeonardoDaVinci: "Leonardo da Vinci",
   Level: "Nível",
   LevelX: "Nível %{level}",
   Liberalism: "Liberalismo",
   LiberalismLevel3DescHTML: "Transporte gratuito <b>de</b> e <b>para</b> armazéns",
   LiberalismLevel5DescHTML: "<b>Duplique</b> o efeito de eletrificação",
   LiberalismLevelX: "Nível de Liberalismo %{level}",
   Library: "Biblioteca",
   LighthouseOfAlexandria: "Farol de Alexandria",
   LighthouseOfAlexandriaDesc: "Todas as construções adjacentes ganham +5 de Multiplicador de Armazenamento",
   LinusPauling: "Linus Pauling",
   LinusPaulingDesc: "+%{value} Ciência de Trabalhadores Ociosos",
   Literature: "Literatura",
   LiveData: "Valores em Tempo Real",
   LocomotiveFactory: "Fábrica de Locomotivas",
   Logging: "Madeireira",
   LoggingCamp: "Madeireira",
   LouisSullivan: "Louis Sullivan",
   LouisSullivanDesc: "+%{value} Multiplicador de Capacidade do Construtor",
   Louvre: "Louvre",
   LouvreDesc: "Para cada 10 Figuras Históricas Extras no Renascimento, uma figura histórica de todas as idades desbloqueadas nasce",
   Lumber: "Lenha",
   LumberMill: "Serraria",
   LunarNewYear: "Ano Novo Lunar: Great Wall fornece o dobro de boost para edifícios. Porcelain Tower fornece +1 nível para todas as figuras históricas desta corrida.",
   LuxorTemple: "Templo de Luxor",
   LuxorTempleDescV2: "+1 Ciência de Trabalhadores Ocupados. Escolha uma religião do império, desbloqueie mais impulso com cada escolha.",
   Machinery: "Maquinaria",
   Magazine: "Revista",
   MagazinePublisher: "Editora de Revistas",
   Maglev: "Maglev",
   MaglevFactory: "Fábrica de Maglev",
   MahatmaGandhi: "Mahatma Gandhi",
   ManageAgeWisdom: "Gerenciar a Sabedoria da Era",
   ManagedImport: "Importação Gerenciada",
   ManagedImportDescV2: "Este edifício importará automaticamente recursos produzidos dentro do alcance de %{range} blocos. Os transportes de recursos para este edifício não podem ser alterados manualmente. A distância máxima de transporte será ignorada.",
   ManageGreatPeople: "Gerenciar Figuras Históricas",
   ManagePermanentGreatPeople: "Gerenciar Figuras Históricas Permanentes",
   ManageSave: "Gerenciar Salvamento",
   ManageWonders: "Gerenciar Maravilhas",
   Manhattan: "Manhattan",
   ManhattanProject: "Projeto Manhattan",
   ManhattanProjectDesc: "Todas as minas de urânio recebem +2 multiplicadores de produção, capacidade de trabalho e armazenamento. Usinas de enriquecimento de urânio e instalações atômicas recebem +1 multiplicador de produção para cada mina de urânio adjacente que é construída em cima de um depósito de urânio.",
   Marble: "Mármore",
   Marbleworks: "Marmoraria",
   MarcoPolo: "Marco Polo",
   MarieCurie: "Marie Curie",
   MarinaBaySands: "Marina Bay Sands",
   MarinaBaySandsDesc: "Todos os edifícios recebem +5 Multiplicador de Capacidade de Trabalho. Esta maravilha pode ser atualizada e cada atualização adicional fornece +1 Multiplicador de Capacidade de Trabalho para todos os edifícios.",
   Market: "Mercado",
   MarketDesc: "Troque um recurso por outro, recursos disponíveis atualizam a cada hora.",
   MarketRefreshMessage: "As negociações em %{count} mercados foram atualizadas.",
   MarketSell: "Vender",
   MarketSettings: "Configurações de Mercado",
   MarketValueDesc: "%{value} comparado ao preço médio",
   MarketYouGet: "Você Recebe",
   MarketYouPay: "Você Paga",
   MartinLuther: "Martin Luther",
   MaryamMirzakhani: "Maryam Mirzakhani",
   MaryamMirzakhaniDesc: "+%{value} Ciência de trabalhadores Ociosos",
   Masonry: "Alvenaria",
   MatrioshkaBrain: "Matrioshka Brain",
   MatrioshkaBrainDescV2:
      "Permite que a Ciência seja contada ao calcular o valor do império (5 Ciência = 1 Valor do Império). +5 Ciência por Trabalhador Ocupado e Ocioso. Esta maravilha pode ser melhorada e cada melhoria adicional fornece +1 Ciência por Trabalhador Ocupado e Ocioso e +1 Multiplicador de Produção para edifícios que produzem Ciência.",
   MausoleumAtHalicarnassus: "Mausoléu de Halicarnasso",
   MausoleumAtHalicarnassusDescV2: "Transports from or to buildings within 2 tile range do not cost workers",
   MaxExplorers: "Exploradores Máximos",
   MaxTransportDistance: "Distância Máxima de Transporte",
   Meat: "Carne",
   Metallurgy: "Metalurgia",
   Michelangelo: "Michelangelo",
   MiddleAge: "Idade Média",
   MilitaryTactics: "Táticas Militares",
   Milk: "Leite",
   Moai: "Moai",
   MoaiDesc: "Moai",
   MobileOverride: "Mobile Override",
   MogaoCaves: "Mogao Caves",
   MogaoCavesDescV3: "+1 Felicidade para cada 10% de trabalhadores ocupados. Todos os edifícios adjacentes que produzem fé estão isentos de -1 felicidade.",
   MonetarySystem: "Sistema Monetário",
   MontSaintMichel: "Mont Saint-Michel",
   MontSaintMichelDesc: "Gera Cultura a partir de Trabalhadores Ociosos. Fornece +1 Multiplicador de Armazenamento para todos os edifícios dentro de um alcance de 2 tiles. Esta maravilha pode ser aprimorada usando a Cultura gerada, e cada nível fornece +1 adicional no Multiplicador de Armazenamento",
   Mosque: "Mesquita",
   MotionPicture: "Filme",
   MountArarat: "Mount Ararat",
   MountAraratDesc: "All buildings within 2 tile range get +X Production, Worker Capacity and Storage Multiplier. X = cubic root of Effective Great People Level",
   MountFuji: "Monte Fuji",
   MountFujiDescV2: "Quando Petra é construída ao lado dela, Petra obtém +8h de armazenamento de Warp. Quando o jogo está em execução, gera 20 warp a cada minuto em Petra (não acelerado pela própria Petra, não gerando quando o jogo está offline).",
   MountSinai: "Monte Sinai",
   MountSinaiDesc: "Quando descoberto, uma grande pessoa da era atual nasce. Todos os edifícios que produzem fé recebem +5 Multiplicador de Armazenamento.",
   MountTai: "Monte Tai",
   MountTaiDesc: "Todos os edifícios que produzem ciência recebem +1 Multiplicador de Produção. Duplique o efeito de Confucious (Figura Histórica). Quando descoberto, gere ciência única equivalente ao custo da tecnologia desbloqueada mais cara.",
   MoveBuilding: "Mover Edifício",
   MoveBuildingFail: "O bloco selecionado não é válido.",
   MoveBuildingNoTeleport: "Você não tem teletransporte suficiente.",
   MoveBuildingSelectTile: "Selecione um Bloco...",
   MoveBuildingSelectTileToastHTML: "Selecione <b>um bloco explorado vazio</b> no mapa como alvo",
   Movie: "Filme",
   MovieStudio: "Estúdio de Cinema",
   Museum: "Museu",
   Music: "Musica",
   MusiciansGuild: "Associação de Músicos",
   MutualAssuredDestruction: "Destruição Mútua Assegurada",
   MutualFund: "Fundo Mútuo",
   Name: "Nome",
   Nanotechnology: "Nanotecnologia",
   NapoleonBonaparte: "Napoleão Bonaparte",
   NaturalGas: "Gás Natural",
   NaturalGasWell: "Poço de Gás Natural",
   NaturalWonderName: "Maravilha Natural: %{name}",
   NaturalWonders: "Maravilhas Naturais",
   Navigation: "Navegação",
   NebuchadnezzarII: "Nabucodonosor II",
   Neuschwanstein: "Neuschwanstein",
   NeuschwansteinDesc: "+10 de Multiplicador de Capacidade de Construtores enquanto estiver construindo maravilhas",
   Newspaper: "Jornal",
   NextExplorersIn: "Próximos Exploradores em",
   NextMarketUpdateIn: "Próxima Atualização do Mercado Em",
   NiagaraFalls: "Cataratas do Niágara",
   NiagaraFallsDescV2: "Todos os armazéns, mercados e caravanas recebem +N multiplicador de armazenamento. N = número de eras desbloqueadas. Albert Einstein fornece +1 multiplicador de produção para o Fundo de Pesquisa (não afetado por outros aumentos como Broadway).",
   NielsBohr: "Niels Bohr",
   NielsBohrDescV2: "+%{value} Ciência de Todos os Trabalhadores se mais de 50% dos trabalhadores estiverem ocupados e menos de 50% dos trabalhadores ocupados trabalharem em transporte.",
   NileRiver: "Rio Nilo",
   NileRiverDesc: "Dobra o efeito de Hatshepsut. Todas as fazendas de trigo ganham +1 multiplicador de produção e armazenamento. Todas as fazendas de trigo adjacentes ganham +5 multiplicadores de produção e armazenamento.",
   NoPowerRequired: "Este edifício não necessita de energia",
   NothingHere: "Nada Aqui",
   NotProducingBuildings: "Construções Que Não Estão Produzindo",
   NuclearFission: "Fissão Nuclear",
   NuclearFuelRod: "Barra de Combustível Nuclear",
   NuclearMissile: "Míssil Nuclear",
   NuclearMissileSilo: "Silo de Mísseis Nucleares",
   NuclearPowerPlant: "Usina Nuclear",
   NuclearReactor: "Reator Nuclear",
   NuclearSubmarine: "Submarino Nuclear",
   NuclearSubmarineYard: "Estaleiro de Submarinos Nucleares",
   OdaNobunaga: "Oda Nobunaga",
   OfflineErrorMessage: "Você está atualmente offline, essa operação requer uma conexão com a Internet",
   OfflineProduction: "Produção Offline",
   OfflineProductionTime: "Tempo de Produção Offline",
   OfflineProductionTimeDescHTML: "Para os <b>primeiros %{time} de tempo offline</b>, você pode escolher entre produção offline ou salto temporal – você pode definir a divisão aqui. O <b>restante do tempo offline</b> só pode ser convertido em salto temporal",
   OfflineTime: "Tempo Offline",
   Oil: "Petróleo",
   OilPress: "Prensa de Óleo",
   OilRefinery: "Refinaria de Petróleo",
   OilWell: "Poço de Petróleo",
   Ok: "OK",
   Oktoberfest: "Oktoberfest: Dobra o efeito de Zugspitze",
   Olive: "Azeitona",
   OlivePlantation: "Plantação de Azeitona",
   Olympics: "Olimpíadas",
   OnlyAvailableWhenPlaying: "Disponível apenas ao jogar %{city}",
   OpenLogFolder: "Abrir Pasta de Log",
   OpenSaveBackupFolder: "Abrir Pasta de Backup",
   OpenSaveFolder: "Abrir Pasta Salvar",
   Opera: "Opera",
   OperationNotAllowedError: "Essa operação não é permitida",
   Opet: "Opet: A Grande Esfinge não aumenta mais o Multiplicador de Consumo",
   OpticalFiber: "Fibra Óptica",
   OpticalFiberPlant: "Fábrica de Fibra Óptica",
   Optics: "Óptica",
   OptionsMenu: "Opções",
   OptionsUseModernUIV2: "Use Anti-Aliased Font",
   OsakaCastle: "Castelo de Osaka",
   OsakaCastleDesc: "Fornece energia para todos os blocos dentro do alcance de 2 blocos. Permite eletrificação de edifícios de produção científica (incluindo Clone Lab).",
   OtherPlatform: "Outra Plataforma",
   Ottoman: "Ottoman",
   OttoVonBismarck: "Otto von Bismarck",
   OxfordUniversity: "Universidade de Oxford",
   OxfordUniversityDescV3: "+10% de produção científica para edifícios que produzem ciência. Quando concluído, gere ciência única equivalente ao custo da tecnologia desbloqueada mais cara.",
   PabloPicasso: "Pablo Picasso",
   Pagoda: "Pagoda",
   PaintersGuild: "Associação de Pintores",
   Painting: "Pintura",
   PalmJumeirah: "Palm Jumeirah",
   PalmJumeirahDesc: "+10 Capacidade do Construtor. Esta maravilha pode ser atualizada e cada atualização adicional fornece +2 Capacidade do Construtor.",
   Pamukkale: "Pamukkale",
   PamukkaleDesc: "When discovered, convert each one of the Permanent Great People Shards (except for Promotion and Wildcard) to the same Great Person From This Run",
   Panathenaea: "Panatenéias: Poseidon fornece +1 Multiplicador de Produção para todos os edifícios",
   Pantheon: "Panteão",
   PantheonDescV2: "Todos os edifícios dentro de 2 blocos ganham +1 Multiplicador de Capacidade de Trabalhador e Armazenamento. Gera ciência com base na produção de fé de todos os santuários.",
   Paper: "Papel",
   PaperMaker: "Fábrica de Papel",
   Parliament: "Parlamento",
   Parthenon: "Partenon",
   ParthenonDescV2: "Duas figuras históricas da Era Clássica nascem e você tem 4 escolhas para cada uma. Associação de Músicos e Associação de Pintores ganham +1 Multiplicador de Produção, Capacidade de Trabalho e Armazenamento e estão isentas de -1 Felicidade.",
   Passcode: "Código de Acesso",
   PasscodeToastHTML: "<b>%{code}</b> é seu código de acesso e é válido por 30 minutos",
   PatchNotes: "Notas de Atualização",
   Peace: "Paz",
   Peacekeeper: "Mantenedor da Paz",
   Penthouse: "Penthouse",
   PercentageOfProductionWorkers: "Porcentagem de Trabalhadores de Produção",
   Performance: "Desempenho",
   PermanentGreatPeople: "Figuras Históricas Permanentes",
   PermanentGreatPeopleAcquired: "Figuras Históricas Permanentes Adquiridas",
   PermanentGreatPeopleUpgradeUndo: "Desfazer atualização de figuras históricas permanentes: isso converterá o nível atualizado de volta em fragmentos - você receberá %{amount} fragmentos.",
   Persepolis: "Persépolis",
   PersepolisDesc: "Todas as Minas de Cobre, Madeireiras e Pedreiras ganham +1 de Multiplicador de Produção, Multiplicador de Capacidade de Trabalhadores e Multiplicador de Armazenamento",
   PeterHiggs: "Peter Higgs",
   PeterHiggsDesc: "+%{value} Ciência de Trabalhadores Ocupados",
   Petra: "Petra",
   PetraDesc: "Gera distorção temporal enquanto você estiver offline, o que você pode usar para acelerar seu império",
   PetraOfflineTimeReconciliation: "Você recebeu %{count} warp após a reconciliação do tempo offline do servidor.",
   Petrol: "Gasolina",
   PhiloFarnsworth: "Philo Farnsworth",
   Philosophy: "Filosofia",
   Physics: "Física",
   PierreDeCoubertin: "Pierre de Coubertin",
   Pizza: "Pizza",
   Pizzeria: "Pizzaria",
   PlanetaryRover: "Rover Planetário",
   Plastics: "Plásticos",
   PlasticsFactory: "Fábrica de Plásticos",
   PlatformAndroid: "Android",
   PlatformiOS: "iOS",
   PlatformSteam: "Steam",
   PlatformSyncInstructionHTML: "Se você quiser sincronizar seu progresso neste dispositivo com um novo dispositivo, clique em <b>Sincronizar com um Novo Dispositivo</b> e obtenha um código de uso único. No novo dispositivo, clique em <b>Conectar a um Dispositivo</b> e digite o código de uso único",
   Plato: "Platão",
   PlayerHandle: "Nome de Jogador",
   PlayerHandleOffline: "Você Está Atualmente Offline",
   PlayerMapClaimThisTile: "Reivindicar Esse Espaço",
   PlayerMapClaimTileCondition2: "Você não foi banido(a) pelo anti-cheat",
   PlayerMapClaimTileCondition3: "Você desbloqueou a tecnologia necessária: %{tech}",
   PlayerMapClaimTileCondition4: "Você não reivindicou um espaço ou o tempo para mover seu espaço passou",
   PlayerMapClaimTileCooldownLeft: "Tempo restante: %{time}",
   PlayerMapClaimTileNoLongerReserved: "Esse espaço não está mais reservado. Você pode evitar <b>%{name}</b> e reivindicar esse espaço para você",
   PlayerMapEstablishedSince: "Est. Desde",
   PlayerMapLastSeenAt: "Ultima Vez Visto(a)",
   PlayerMapMapTileBonus: "Bônus de Troca de Espaço",
   PlayerMapMenu: "Trocar",
   PlayerMapOccupyThisTile: "Occupy This Tile",
   PlayerMapOccupyTileCondition1: "This tile is adjacent to your home or occupied tiles",
   PlayerMapPageGoBackToCity: "Voltar Para a Cidade",
   PlayerMapSetYourTariff: "Defina Sua Tarifa",
   PlayerMapTariff: "Tarifa",
   PlayerMapTariffApply: "Aplicar Taxa Tarifária",
   PlayerMapTariffDesc: "Cada troca que passa pelo seu espaço vai pagar uma tarifa para você. é uma balança: se você aumentar a tarifa, mais você vai ganhar por cada troca, porém menos trocas irão passar pelo seu espaço.",
   PlayerMapTileAvailableTilePoint: "Available Tile Point",
   PlayerMapTileFromOccupying: "From Owned/Occupied Tiles",
   PlayerMapTileFromOccupyingTooltipHTML: "An owned/occupied tile generates <b>%{point}</b> tile point per hour (up to %{max} days from the first claimed tile)",
   PlayerMapTileFromRank: "From Account Rank",
   PlayerMapTileTilePoint: "Tile Point",
   PlayerMapTileUsedTilePoint: "Used Tile Point",
   PlayerMapTileUsedTilePointTooltipHTML: "You need <b>1 tile point</b> to own/occupy a tile",
   PlayerMapTradesFrom: "Trocas de %{name}",
   PlayerMapUnclaimedTile: "Espaço Não Reivindicado",
   PlayerMapYourTile: "Seu Espaço",
   PlayerTrade: "Troque com Jogadores",
   PlayerTradeAddSuccess: "Essa troca foi adicionada com sucesso",
   PlayerTradeAddTradeCancel: "Cancelar",
   PlayerTradeAmount: "Quantidade",
   PlayerTradeCancelDescHTML: "Você receberá <b>%{res}</b> de volta após cancelar esta negociação: <b>%{percent}</b> cobrado para reembolso e <b>%{discard}</b> descartado devido a estouro de armazenamento<br><b>Tem certeza de que deseja cancelar?</b>",
   PlayerTradeCancelTrade: "Cancelar Troca",
   PlayerTradeClaim: "Coletar",
   PlayerTradeClaimAll: "Coletar Todas",
   PlayerTradeClaimAllFailedMessageV2: "Falha ao reivindicar qualquer troca - o armazenamento está cheio?",
   PlayerTradeClaimAllMessageV2: "Você reivindicou: <b>%{resources}</b>",
   PlayerTradeClaimAvailable: "%{count} troca(s) foram concluídas disponíveis para coleta",
   PlayerTradeClaimTileFirst: "Reivindique Seu Espaço No Mapa de Trocas",
   PlayerTradeClaimTileFirstWarning: "Você só pode trocar com outros jogadores depois de ter reivindicado seu espaço no mapa de trocas",
   PlayerTradeClearAll: "Limpar Todos os Preenchimentos",
   PlayerTradeClearFilter: "Limpar Filtros",
   PlayerTradeDisabledBeta: "Você só pode criar trocas de jogadores quando a versão beta for lançada.",
   PlayerTradeFill: "Enviar",
   PlayerTradeFill50: "Fill 50%",
   PlayerTradeFill95: "Fill 95%",
   PlayerTradeFillAmount: "Enviar Quantidade",
   PlayerTradeFillAmountMaxV2: "Preenchimento Máximo",
   PlayerTradeFillBy: "Enviado Por",
   PlayerTradeFillPercentage: "Porcentagem de Preenchimento",
   PlayerTradeFillSuccessV2: "<b>%{success}/%{total}</b> negociações foram concluídas. Você pagou <b>%{fillAmount} %{fillResource}</b> e recebeu <b>%{receivedAmount} %{receivedResource}</b>",
   PlayerTradeFillTradeButton: "Enviar Troca",
   PlayerTradeFillTradeTitle: "Enviar Troca",
   PlayerTradeFilters: "Filtros",
   PlayerTradeFiltersApply: "Aplicar",
   PlayerTradeFiltersClear: "Limpar",
   PlayerTradeFilterWhatIHave: "Filtrar pelo que Tenho",
   PlayerTradeFrom: "De",
   PlayerTradeIOffer: "Eu Ofereço",
   PlayerTradeIWant: "Eu Quero",
   PlayerTradeMaxAll: "Máximo de Todos os Preenchimentos",
   PlayerTradeMaxTradeAmountFilter: "Quantidade Máxima",
   PlayerTradeMaxTradeExceeded: "Você excedeu o número máximo de trocas ativas para o ranque da sua conta",
   PlayerTradeNewTrade: "Nova Troca",
   PlayerTradeNoFillBecauseOfResources: "Nenhuma negociação foi preenchida devido a recursos insuficientes",
   PlayerTradeNoValidRoute: "Não foi possível encontrar um rota de troca válida entre você e %{name}",
   PlayerTradeOffer: "Oferece",
   PlayerTradePlaceTrade: "Colocar Troca",
   PlayerTradePlayerNameFilter: "Nome do Jogador",
   PlayerTradeResource: "Recurso",
   PlayerTradeStorageRequired: "Armazenamento Necessário",
   PlayerTradeTabImport: "Importar",
   PlayerTradeTabPendingTrades: "Trocas Pendentes",
   PlayerTradeTabTrades: "Trocas",
   PlayerTradeTariffTooltip: "Coletado de uma Tarifa de Troca",
   PlayerTradeWant: "Quer",
   PlayerTradeYouGetGross: "Você Recebe (Antes da Tarifa): %{res}",
   PlayerTradeYouGetNet: "Você Recebe (Depois da Tarifa): %{res}",
   PlayerTradeYouPay: "Você Paga: %{res}",
   Poem: "Poema",
   PoetrySchool: "Escola de Poesia",
   Politics: "Política",
   PolytheismLevelX: "Politeísmo %{level}",
   PorcelainTower: "Torre de Porcelana",
   PorcelainTowerDesc: "+5 Felicidade. Quando construído, todas as suas figuras históricas extras no renascimento ficarão disponíveis para esta corrida (elas são lançadas seguindo a mesma regra das figuras históricas permanentes).",
   PorcelainTowerMaxPickPerRoll: "Prefira a Escolha Máxima por Rolagem",
   PorcelainTowerMaxPickPerRollDescHTML: "Ao escolher figuras históricas após a conclusão da Torre de Porcelana, prefira a escolha máxima por jogada para a quantidade disponível",
   Poseidon: "Poseidon",
   PoseidonDescV2: "Todos os edifícios adjacentes recebem atualizações gratuitas para o Nível 25 e +N Produção, Capacidade de Trabalhadores e Multiplicador de Armazenamento. N = Tier do edifício",
   PoultryFarm: "Fazenda de Aves",
   Power: "Energia",
   PowerAvailable: "Energia Disponível",
   PowerUsed: "Energia Usada",
   PreciousMetal: "Metal Precioso",
   Printing: "Impressão",
   PrintingHouse: "Gráfica",
   PrintingPress: "Imprensa",
   PrivateOwnership: "Propriedade Privada",
   Produce: "Produção",
   ProduceResource: "Produção: %{resource}",
   ProductionMultiplier: "Multiplicador de Produção",
   ProductionPriority: "Prioridade de Produção",
   ProductionPriorityDescV4: "A prioridade determina a ordem em que os edifícios transportam e produzem - um número maior significa que um edifício transporta e produz antes de outros edifícios",
   ProductionWorkers: "Trabalhadores da Produção",
   Progress: "Progresso",
   ProgressTowardsNextGreatPerson: "Progresso Até a Próxima Figura Histórica ao Renascer",
   ProgressTowardsTheNextGreatPerson: "Progresso em direção à próxima Figura Histórica",
   PromotionGreatPersonDescV2: "Quando consumido, promove qualquer figura histórica permanente da mesma era para a próxima era.",
   ProphetsMosque: "Mesquita do Profeta",
   ProphetsMosqueDesc: "Duplique o efeito de Harun al-Rashid. Gere ciência com base na produção de fé de todas as mesquitas.",
   Province: "Província",
   ProvinceAegyptus: "Egito",
   ProvinceAfrica: "África",
   ProvinceAsia: "Ásia",
   ProvinceBithynia: "Bithynia",
   ProvinceCantabri: "Cantabri",
   ProvinceCappadocia: "Capadócia",
   ProvinceCilicia: "Cilícia",
   ProvinceCommagene: "Commagene",
   ProvinceCreta: "Creta",
   ProvinceCyprus: "Chipre",
   ProvinceCyrene: "Cyrene",
   ProvinceGalatia: "Galatia",
   ProvinceGallia: "Gália",
   ProvinceGalliaCisalpina: "Gália Cisalpina",
   ProvinceGalliaTransalpina: "Gália Transalpina",
   ProvinceHispania: "Hispania",
   ProvinceIllyricum: "Illyricum",
   ProvinceItalia: "Italia",
   ProvinceJudia: "Judeia",
   ProvinceLycia: "Lícia",
   ProvinceMacedonia: "Macedônia",
   ProvinceMauretania: "Mauretânia",
   ProvinceNumidia: "Numídia",
   ProvincePontus: "Pontus",
   ProvinceSardiniaAndCorsica: "Sardenha e Córsega",
   ProvinceSicillia: "Sicília",
   ProvinceSophene: "Sophene",
   ProvinceSyria: "Syria",
   PublishingHouse: "Casa de Publicidade",
   PyramidOfGiza: "Pirâmides de Gizé",
   PyramidOfGizaDesc: "Todas as construções que produzem trabalhadores ganham +1 de Multiplicador de Produção",
   QinShiHuang: "Qin Shi Huang",
   Radio: "Rádio",
   RadioStation: "Estação de Rádio",
   Railway: "Ferrovia",
   RamessesII: "Ramsés II",
   RamessesIIDesc: "+%{value} Multiplicador de Capacidade de Construtores",
   RandomColorScheme: "Esquema de Cores Aleatório",
   RapidFire: "Fogo Rápido",
   ReadFullPatchNotes: "Leia as notas do patch",
   RebirthHistory: "Rebirth History",
   RebirthTime: "Rebirth Time",
   Reborn: "Renascer",
   RebornModalDescV3:
      "Você começará um novo império, mas todas as suas figuras históricas <b>desta corrida</b> se tornarão fragmentos permanentes, que podem ser usados ​​para atualizar seu <b>nível de figuras históricas permanentes</b>. Você também obterá fragmentos extras de figuras históricas com base no seu <b>valor total do império</b>.",
   RebornOfflineWarning: "Você está atualmente offline. Você só pode renascer quando estiver conectado(a) ao servidor",
   RebornTradeWarning: "Você possui trocas ativas ou que podem ser coletadas. <b>Renascer irá apagá-las</b> - você deveria considerar cancelá-las ou coletá-las primeiro",
   RedistributeAmongSelected: "Redistribuir Entre os Selecionados",
   RedistributeAmongSelectedCap: "Limite",
   RedistributeAmongSelectedImport: "Importar",
   Refinery: "Refinaria",
   Reichstag: "Reichstag",
   Religion: "Religião",
   ReligionBuddhism: "Budismo",
   ReligionChristianity: "Cristianismo",
   ReligionDescHTML: "Escolha entre <b>Cristianismo, Islamismo, Budismo ou Politeísmo</b> como religião do seu império. Você <b>não pode trocar de religião</b> depois que ela for escolhida. Você pode desbloquear mais impulsos dentro de cada religião.",
   ReligionIslam: "Islamismo",
   ReligionPolytheism: "Politeísmo",
   Renaissance: "Renascimento",
   RenaissanceAge: "Renascimento",
   ReneDescartes: "René Descartes",
   RequiredDeposit: "Depósito Necessário",
   RequiredWorkersTooltipV2: "O número necessário de trabalhadores para a produção é igual à soma de todos os recursos consumidos e produzidos após multiplicadores (excluindo multiplicadores dinâmicos).",
   RequirePower: "Requer energia",
   RequirePowerDesc: "Este edifício precisa ser construído em um bloco com energia e pode estender a energia para os blocos adjacentes.",
   Research: "Pesquisa",
   ResearchFund: "Fundo de Pesquisa",
   ResearchLab: "Laboratório de Pesquisa",
   ResearchMenu: "Pesquisa",
   ResourceAmount: "Quantidade",
   ResourceBar: "Barra de Recursos",
   ResourceBarExcludeStorageFullHTML: "Exclua edifícios que tenham <b>armazenamento completo</b> de Edifícios Não Produtivos.",
   ResourceBarExcludeTurnedOffOrNoActiveTransportHTML: "Exclua edifícios que estão <b>desligados</b> de Edifícios Não Produtivos.",
   ResourceBarShowUncappedHappiness: "Mostre Felicidade sem Limites",
   ResourceCloneTooltip: "O multiplicador de produção se aplica somente ao recurso clonado (ou seja, a cópia extra).",
   ResourceColor: "Cor do Recurso",
   ResourceExportBelowCap: "Exportação Abaixo do Limite",
   ResourceExportBelowCapTooltip: "Permitir que outros edifícios transportem um recurso deste edifício, mesmo quando sua quantidade estiver abaixo do limite.",
   ResourceExportToSameType: "Exportar Para o Mesmo Tipo",
   ResourceExportToSameTypeTooltip: "Permitir que outros edifícios do mesmo tipo transportem um recurso deste edifício.",
   ResourceFromBuilding: "%{resource} de %{building}",
   ResourceImport: "Transporte de Recurso",
   ResourceImportCapacity: "Capacidade de Transporte de Recursos",
   ResourceImportImportCapV2: "Quantidade Máxima",
   ResourceImportImportCapV2Tooltip: "Este edifício deixará de transportar este recurso quando a quantidade máxima for atingida.",
   ResourceImportImportPerCycleV2: "Por ciclo",
   ResourceImportImportPerCycleV2ToolTip: "A quantidade deste recurso que é transportada por ciclo",
   ResourceImportPartialWarningHTML: "A capacidade total de transporte de recursos excedeu a capacidade máxima: <b>cada transporte de recursos transportará apenas parcialmente por ciclo</b>",
   ResourceImportResource: "Recurso",
   ResourceImportSettings: "Transporte de Recursos: %{res}",
   ResourceImportStorage: "Armazenamento",
   ResourceNeeded: "Extra %{resource} x%{amount} Needed",
   ResourceTransportPreference: "Preferência de Transporte",
   RevealDeposit: "Revelar",
   Revolution: "Revolução",
   RhineGorge: "Garganta do Reno",
   RhineGorgeDesc: "+2 Felicidade para cada maravilha dentro de um alcance de 2 casas",
   RichardFeynman: "Richard Feynman",
   RichardFeynmanDesc: "+%{value} Ciência de Todos os Trabalhadores se mais de 50% dos trabalhadores estiverem ocupados e menos de 50% dos trabalhadores ocupados trabalharem no transporte.",
   RichardJordanGatling: "Richard Jordan Gatling",
   Rifle: "Rifle",
   RifleFactory: "Fábrica de Rifles",
   Rifling: "Armamento",
   Rijksmuseum: "Rijksmuseum",
   RijksmuseumDesc: "+5 de Felicidade. Todas as construções que consomem ou produzem Cultura ganham +1 de Produção, Armazenamento e Capacidade de Trabalhadores",
   RoadAndWheel: "Estrada & Roda",
   RobertNoyce: "Robert Noyce",
   Robocar: "Robocar",
   RobocarFactory: "Fábrica de Robocar",
   Robotics: "Robótica",
   RockefellerCenterChristmasTree: "Árvore de Natal do Rockefeller Center",
   RockefellerCenterChristmasTreeDesc: "+3 Felicidade para cada era desbloqueada. Esta maravilha natural só pode ser descoberta em dezembro",
   Rocket: "Foguete",
   RocketFactory: "Fábrica de Foguetes",
   Rocketry: "Foguetes",
   Roman: "Romanos",
   RomanForum: "Fórum Romano",
   RudolfDiesel: "Rudolf Diesel",
   Rurik: "Rurik",
   RurikDesc: "+%{value} Felicidade",
   SagradaFamilia: "Sagrada Família",
   SagradaFamiliaDesc: "Todos os edifícios com alcance de 2 blocos recebem + N multiplicadores de produção, capacidade de trabalho e armazenamento. N = diferença máxima de nível entre edifícios adjacentes (intervalo de 1 bloco) à Sagrada Família.",
   SaintBasilsCathedral: "Catedral de São Basílio",
   SaintBasilsCathedralDescV2: "Permite que edifícios de extração de recursos trabalhem adjacentes a um depósito. Todos os edifícios de Tier I adjacentes a esta maravilha, recebem +1 Multiplicador de Produção, Multiplicador de Capacidade de Trabalhadores e Multiplicador de Armazenamento.",
   Saladin: "Saladin",
   Samsuiluna: "Samsu-iluna",
   Sand: "Areia",
   Sandpit: "Caixa de Areia",
   SantaClausVillage: "Santa Claus Village",
   SantaClausVillageDesc: "When completed, a great person of the current age is born. This wonder can be upgraded and each additional upgrade provides an extra great person. When choosing great people from this wonder, 4 choices are provided. This wonder can only be constructed in December",
   SargonOfAkkad: "Sargão da Acádia",
   Satellite: "Satélite",
   SatelliteFactory: "Fábrica de Satélites",
   SatoshiNakamoto: "Satoshi Nakamoto",
   Saturnalia: "Saturnalia: Alps não aumenta mais o Multiplicador de Consumo",
   SaveAndExit: "Salvar e Sair",
   School: "Escola",
   Science: "Ciência",
   ScienceFromBusyWorkers: "Ciência de Trabalhadores Ocupados",
   ScienceFromIdleWorkers: "Ciência de Trabalhadores Ociosos",
   SciencePerBusyWorker: "Por Trabalhador Ocupado",
   SciencePerIdleWorker: "Por Trabalhador Ocioso",
   ScrollSensitivity: "Sensibilidade de Rolagem",
   ScrollSensitivityDescHTML: "Ajuste a sensibilidade ao rolar a roda do mouse. <b>Deve estar entre 0,01 e 100. O padrão é 1</b>",
   ScrollWheelAdjustLevelTooltip: "Você pode usar a roda de rolagem para ajustar o nível quando o cursor estiver sobre isso",
   SeaTradeCost: "Custo do Comércio Marítimo",
   SeaTradeUpgrade: "Negociando com jogadores do outro lado do mar. Tarifa para cada bloco de mar.: %{tariff}",
   SelectCivilization: "Selecionar Civilização",
   SelectedAll: "Selecionar Todos",
   SelectedCount: "%{count} Selecionado(s)",
   Semiconductor: "Semicondutor",
   SemiconductorFab: "Fábrica de Semicondutores",
   SendExplorer: "Enviar Explorer",
   SergeiKorolev: "Sergei Korolev",
   SetAsDefault: "Definir Como Padrão",
   SetAsDefaultBuilding: "Definir Como Padrão Para Todos %{building}",
   Shamanism: "Xamanismo",
   Shelter: "Abrigo",
   Shortcut: "Atalhos",
   ShortcutBuildingPageSellBuildingV2: "Demolir Edifício",
   ShortcutBuildingPageToggleBuilding: "Alternar Produção",
   ShortcutBuildingPageToggleBuildingSetAllSimilar: "Alternar Produção e Aplicar a Todos",
   ShortcutBuildingPageUpgrade1: "Botão de Atualização 1 (+1)",
   ShortcutBuildingPageUpgrade2: "Botão de Atualização 2 (+5)",
   ShortcutBuildingPageUpgrade3: "Botão de Atualização 3 (+10)",
   ShortcutBuildingPageUpgrade4: "Botão de Atualização 4 (+15)",
   ShortcutBuildingPageUpgrade5: "Botão de Atualização 5 (+20)",
   ShortcutClear: "Redefinir",
   ShortcutConflict: "Seu atalho está em conflito com %{name}",
   ShortcutNone: "Nenhum",
   ShortcutPressShortcut: "Pressione Tecla de Atalho...",
   ShortcutSave: "Salvar",
   ShortcutScopeBuildingPage: "Página de Construção",
   ShortcutScopeConstructionPage: "Página de Construção/Atualização",
   ShortcutScopeEmptyTilePage: "Página de Espaço Vazio",
   ShortcutScopePlayerMapPage: "Página do Mapa de Trocas",
   ShortcutScopeTechPage: "Página de Tecnologia",
   ShortcutScopeUnexploredPage: "Página Inexplorada",
   ShortcutTechPageGoBackToCity: "Voltar Para a Cidade",
   ShortcutTechPageUnlockTech: "Desbloquear Tecnologia Selecionada",
   ShortcutUpgradePageCancelAllUpgrades: "Cancel All Upgrades",
   ShortcutUpgradePageCancelUpgrade: "Cancelar Atualização",
   ShortcutUpgradePageDecreaseLevel: "Diminuir Nível de Melhoria",
   ShortcutUpgradePageEndConstruction: "Parar a Construção",
   ShortcutUpgradePageIncreaseLevel: "Aumentar Nível de Melhoria",
   ShowTransportArrow: "Mostrar Seta de Transporte",
   ShowTransportArrowDescHTML: "Desativar isso ocultará as setas de transporte. Pode <i>melhorar ligeiramente</i> o desempenho em dispositivos de baixo custo. A melhoria de desempenho entra em vigor <b>após reiniciar o jogo</b>",
   ShowUnbuiltOnly: "Mostrar apenas edifícios que ainda não foram construídos",
   Shrine: "Santuário",
   SidePanelWidth: "Largura do Painel Lateral",
   SidePanelWidthDescHTML: "Altere a largura do painel lateral. <b>É necessário reiniciar o jogo para que tenha efeito</b>",
   SiegeRam: "Ariete de Cerco",
   SiegeWorkshop: "Oficina de Cerco",
   Silicon: "Silício",
   SiliconSmelter: "Fundição de Silício",
   Skyscraper: "Arranha-céu",
   Socialism: "Socialismo",
   SocialismLevel4DescHTMLV2: "Gera ciência única equivalente ao custo da tecnologia mais barata da <b>Era das Guerras Mundiais</b>",
   SocialismLevel5DescHTMLV2: "Gera ciência única equivalente ao custo da tecnologia mais barata da <b>Era da Guerra Fria</b>",
   SocialismLevelX: "Nível Socialismo %{level}",
   SocialNetwork: "Rede Social",
   Socrates: "Sócrates",
   SocratesDesc: "+%{value} Ciência de Trabalhadores Ocupados",
   Software: "Software",
   SoftwareCompany: "Empresa de Software",
   Sound: "Som",
   SoundEffect: "Efeitos Sonoros",
   SourceGreatPerson: "Figura Hsitórica: %{person}",
   SourceGreatPersonPermanent: "Figura Histórica Permanente: %{person}",
   SourceIdeology: "Ideologia: %{ideology}",
   SourceReligion: "Religião: %{religion}",
   SourceResearch: "Pesquisa: %{tech}",
   SourceTradition: "Tradição: %{tradition}",
   SpaceCenter: "Centro Espacial",
   Spacecraft: "Nave Espacial",
   SpacecraftFactory: "Fábrica de Naves Espaciais",
   SpaceNeedle: "Space Needle",
   SpaceNeedleDesc: "+1 Felicidade para cada maravilha construída",
   SpaceProgram: "Programa Espacial",
   Sports: "Esporte",
   Stable: "Estábulo",
   Stadium: "Estádio",
   StartFestival: "Que comece o festival!",
   Stateship: "Estado",
   StatisticsBuildings: "Construções",
   StatisticsBuildingsSearchText: "Digite o nome de um edifício para pesquisar",
   StatisticsEmpire: "Império",
   StatisticsExploration: "Exploração",
   StatisticsOffice: "Escritório de Estatísticas",
   StatisticsOfficeDesc: "Forneça estatísticas do seu império. Gera exploradores para explorar o mapa.",
   StatisticsResources: "Recursos",
   StatisticsResourcesDeficit: "Déficit",
   StatisticsResourcesDeficitDesc: "Produção: %{output} - Consumo: %{input}",
   StatisticsResourcesRunOut: "Faltando",
   StatisticsResourcesSearchText: "Digite um nome de recurso para pesquisar",
   StatisticsScience: "Ciência",
   StatisticsScienceFromBuildings: "Ciência a Partir de Edifícios",
   StatisticsScienceFromWorkers: "Ciência dos Trabalhadores",
   StatisticsScienceProduction: "Ciência da Produção",
   StatisticsStalledTransportation: "Transporte Parado",
   StatisticsTotalTransportation: "Transporte Total",
   StatisticsTransportation: "Transporte",
   StatisticsTransportationPercentage: "Porcentagem de Trabalhadores Transportadores",
   StatueOfLiberty: "Estátua da Liberdade",
   StatueOfLibertyDesc: "Todas as construções adjacentes ganham +N de Multiplicador de Capacidade de Produção, Armazenamento e Trabalhadores. N = Número de construções adjacentes do mesmo tipo",
   StatueOfZeus: "Estátua de Zeus",
   StatueOfZeusDesc: "Gera depósitos aleatórios, dos que foram revelados, em espaços vazios adjacentes. Todas as construções adjacentes de Tier I ganham +5 de Multiplicador de Produção e Armazenamento",
   SteamAchievement: "Conquista Steam",
   SteamAchievementDetails: "Ver Conquistas Steam",
   SteamEngine: "Motor a Vapor",
   Steamworks: "Fábrica de Motores",
   Steel: "Aço",
   SteelMill: "Siderúrgica",
   StephenHawking: "Stephen Hawking",
   Stock: "Ação",
   StockExchange: "Bolsa de Valores",
   StockMarket: "Bolsa de Valores",
   StockpileDesc: "Essa construção irá transportar %{capacity}x entrada de recursos por cíclo de produção até o máximo ser atingido",
   StockpileMax: "Estoque Máximo",
   StockpileMaxDesc: "Essa construção irá parar de transportar um recurso assim que tiver o suficiente para %{cycle} cíclos de produção",
   StockpileMaxUnlimited: "Ilimitado",
   StockpileMaxUnlimitedDesc: "Essa construção nunca irá parar de transportar recursos, apenas até o armazenamento ficar lotado",
   StockpileSettings: "Capacidade de Entrada de Estoque",
   Stone: "Pedra",
   StoneAge: "Idade da Pedra",
   Stonehenge: "Stonehenge",
   StonehengeDesc: "Todas as construções que consomem ou produzem pedra ganham +1 de Multiplicador de Produção",
   StoneQuarry: "Pedreira",
   StoneTool: "Ferramenta de Pedra",
   StoneTools: "Ferramentas de Pedra",
   Storage: "Armazenamento",
   StorageBaseCapacity: "Capacidade Base",
   StorageMultiplier: "Multiplicador de Armazenamento",
   StorageUsed: "Armazenamento Usado",
   StPetersBasilica: "Basílica de São Pedro",
   StPetersBasilicaDescV2: "Todas as igrejas recebem +5 Multiplicador de Armazenamento. Gera ciência com base na produção de fé de todas as igrejas.",
   Submarine: "Submarino",
   SubmarineYard: "Estaleiro de Submarino",
   SuleimanI: "Suleiman I",
   SummerPalace: "Palácio de Verão",
   SummerPalaceDesc: "Todas as construções adjacentes que consomem ou produzem Pólvora são isentas da penalidade de -1 de Felicidade. Todas as construções que consomem ou produzem Pólvora ganham +1 de Capacidade de Produção, Armazenamento e Trabalhadores",
   Supercomputer: "Supercomputador",
   SupercomputerLab: "Laboratório de Supercomputadores",
   SupporterPackRequired: "Pacote de Suporte necessário",
   SupporterThankYou: "O CivIdle se mantém à tona graças à generosidade dos seguintes proprietários de pacotes de suporte",
   SwissBank: "Swiss Bank",
   SwissBankDescV2: "Convert a chosen resource from adjacent warehouses to Koti (10 million in Sanskrit) - a tradeable resource that is worth 10M value. Each level of Swiss Bank add 1 Koti conversion per cycle, which is affected by Production Multiplier. Swiss Bank can store unlimited amount of Koti",
   Sword: "Espada",
   SwordForge: "Forja de Espadas",
   SydneyOperaHouse: "Ópera de Sydney",
   SydneyOperaHouseDescV2: "Sydney Opera House",
   SyncToANewDevice: "Sincronizar com um Novo Dispositivo",
   Synthetics: "Sintetização",
   TajMahal: "Taj Mahal",
   TajMahalDescV2: "Nasce uma figura histórica da Idade Clássica e uma figura histórica da Idade Média. Multiplicador de Capacidade do Construtor +5 ao melhorar edifícios acima do Nível 20",
   TangOfShang: "Tang de Shang",
   TangOfShangDesc: "+%{value} Ciência de Trabalhadores Ociosos",
   Tank: "Tanque",
   TankFactory: "Fábrica de Tanques",
   TechAge: "Era",
   TechGlobalMultiplier: "Boost",
   TechHasBeenUnlocked: "%{tech} foi desbloqueado",
   TechProductionPriority: "Desbloqueia prioridade de construção - permite ajustar a prioridade de produção para cada construção",
   TechResourceTransportPreference: "Desbloquear preferência de transporte de construção - permite definir como uma construção transporta os recursos necessários para sua produção",
   TechResourceTransportPreferenceAmount: "Quantia",
   TechResourceTransportPreferenceAmountTooltip: "Este edifício preferirá transportar recursos de edifícios que tenham uma quantidade maior de armazenamento",
   TechResourceTransportPreferenceDefault: "Padrão",
   TechResourceTransportPreferenceDefaultTooltip: "Não substitua a preferência de transporte para este recurso, usará a preferência de transporte do edifício",
   TechResourceTransportPreferenceDistance: "Distância",
   TechResourceTransportPreferenceDistanceTooltip: "Este edifício preferirá transportar recursos de edifícios que estejam mais próximos em distância",
   TechResourceTransportPreferenceOverrideTooltip: "Este recurso possui uma substituição de preferência de transporte: %{mode}",
   TechResourceTransportPreferenceStorage: "Armazenamento",
   TechResourceTransportPreferenceStorageTooltip: "Este edifício preferirá transportar recursos de edifícios que tenham maior porcentagem de armazenamento usado",
   TechStockpileMode: "Desbloqueia o modo de estoque - permite ajustar o estoque para cada construção",
   Teleport: "Teletransporte",
   TeleportDescHTML: "Um teletransporte é gerado <b>a cada %{time} segundos</b>. Um teletransporte pode ser usado para <b>mover um edifício (maravilhas excluídas)</b> uma vez",
   Television: "Televisão",
   TempleOfArtemis: "Templo de Artemis",
   TempleOfArtemisDesc: "Todas as Forjas de Espada e Arsenais ganham +5 Níveis quando completadas. Todas as Forjas de Espada e Arsenais ganham +1 de Multiplicador de Produção, Multiplicador de Capacidade de Trabalhadores e Multiplicador de Armazenamento",
   TempleOfHeaven: "Templo de Céu",
   TempleOfHeavenDesc: "Todas as construções que estão nível 10 ou maior ganham +1 de Multiplicador de Capacidade de Trabalhadores",
   TempleOfPtah: "Templo de Ptah",
   TerracottaArmy: "Exército de Terracota",
   TerracottaArmyDesc: "Todas as Minas de Ferro ganham +1 de Multiplicador de Produção, Multiplicador de Capacidade de Trabalhadores e Multiplicador de Armazenamento. Forjas de Ferramenta ganham +1 de Multiplicador de Produção para cada Mina de Ferro adjacente",
   Thanksgiving: "Ação de Graças: Wall Street fornece o dobro do impulso para edifícios e aplica-se a Fundos Mútuos, Fundos de Hedge e Mineradores de Bitcoin. Fundos de Pesquisa recebem +5 Multiplicadores de Produção.",
   Theater: "Teatro",
   Theme: "Tema",
   ThemeColor: "Cor do Tema",
   ThemeColorResearchBackground: "Fundo do Menu de Pesquisa",
   ThemeColorReset: "Cores Padrão",
   ThemeColorResetBuildingColors: "Cores de Construção Padrão",
   ThemeColorResetResourceColors: "Cores de Recurso Padrão",
   ThemeInactiveBuildingAlpha: "Transparência de Contruções Inativas",
   ThemePremiumTile: "This tile is only available for Supporter Pack owners",
   ThemeResearchHighlightColor: "Cor de Pesquisa Destacada",
   ThemeResearchLockedColor: "Cor de Pesquisa Bloqueada",
   ThemeResearchUnlockedColor: "Cor de Pesquisa Desbloqueada",
   ThemeTransportIndicatorAlpha: "Transparência do Indicador de Transporte",
   Theocracy: "Teocracia",
   TheoreticalData: "Dados Teóricos",
   ThePentagon: "O Pentágono",
   ThePentagonDesc: "Após ser construído, gera teletransportes que podem ser usados para mover edifícios. Todos os edifícios dentro de um alcance de 2 azulejos recebem +1 de Produção, Capacidade de Trabalhador e Multiplicador de Armazenamento",
   TheWhiteHouse: "A Casa Branca",
   ThomasEdison: "Thomas Edison",
   ThomasGresham: "Thomas Gresham",
   Tile: "Espaço",
   TileBonusRefreshIn: "Tile bonus will refresh in <b>%{time}</b>",
   TimBernersLee: "Tim Berners-Lee",
   TimeWarp: "Distorção Temporal",
   TimeWarpWarning: "Acelerar a uma velocidade maior do que seu computador aguenta pode resultar em perda de dados: USE A SEU PRÓPRIO RISCO",
   ToggleWonderEffect: "Alternar Efeito da Maravilha",
   Tool: "Ferramenta",
   TopkapiPalace: "Topkapı Palace",
   TopkapiPalaceDesc: "All buildings within 2 tile range get +X Storage Multiplier. X = 50% of its Production Multiplier (excluding Dynamic)",
   TotalEmpireValue: "Valor Total do Império",
   TotalEmpireValuePerCycle: "Valor Total do Império por Cíclo",
   TotalEmpireValuePerCyclePerGreatPeopleLevel: "Valor total do Império por Ciclo por Nível de Figuras Históricas",
   TotalEmpireValuePerWallSecond: "Valor Total de Muro do Império por Segundo",
   TotalEmpireValuePerWallSecondPerGreatPeopleLevel: "Valor Total de Muro do Império por Segundo por Nível de Figuras Históricas",
   TotalGameTimeThisRun: "Tempo Total de Jogo Nesta Corrida",
   TotalScienceRequired: "Ciência Total Necessária",
   TotalStorage: "Armazenamento Total",
   TotalWallTimeThisRun: "Tempo Total de Muro Nesta Corrida",
   TotalWallTimeThisRunTooltip: "O Tempo de Muro (também conhecido como tempo real decorrido) mede o tempo real gasto para esta corrida. Ele difere do tempo de jogo porque o Time Warp em Petra e na Produção Offline não afeta o tempo de muro, mas afeta o tempo de jogo.",
   TotalWorkers: "Total de Trabalhadores",
   TowerBridge: "Tower Bridge",
   TowerBridgeDesc: "Após ser construída, uma grande pessoa de idades desbloqueadas nasce a cada 3600 ciclos (1h de tempo de jogo)",
   TowerOfBabel: "Torre de Babel",
   TowerOfBabelDesc: "Fornece +2 Multiplicador de Produção para todos os edifícios que tenham pelo menos um edifício funcional localizado adjacente à maravilha.",
   TradeFillSound: "Som de 'Troca Preenchida'",
   TradeValue: "Valor da Troca",
   TraditionCommerce: "Comércio",
   TraditionCultivation: "Cultivo",
   TraditionDescHTML: "Escolha entre <b>Cultivo, Comércio, Expansão e Honra</b> como sua tradição de império. Você <b>não pode trocar de tradição</b> depois que ela for escolhida. Você pode desbloquear mais impulsos dentro de cada tradição",
   TraditionExpansion: "Expansão",
   TraditionHonor: "Honra",
   Train: "Trem",
   TranslationPercentage: "%{language} está %{percentage} traduzido. Ajude a melhorar esta tradução no GitHub",
   TranslatorCredit: "ifeellovevenus",
   Translators: "Tradutores",
   TransportAllocatedCapacityTooltip: "Capacidade de Construtores alocada para transportar esse recurso",
   TransportationWorkers: "Trabalhadores de Transporte",
   TransportCapacity: "Capacidade de Transporte",
   TransportCapacityMultiplier: "Multiplicador de Capacidade de Transporte",
   TransportManualControlTooltip: "Transporta esse recurso para construção/melhoria",
   TransportPlanCache: "Cache do Plano de Transporte",
   TransportPlanCacheDescHTML:
      "A cada ciclo, cada edifício calcula o melhor plano de transporte com base em suas configurações - esse processo requer alto poder de CPU. Habilitar isso tentará armazenar em cache o resultado do plano de transporte se ele ainda for válido e, portanto, reduzir o uso da CPU e a queda da taxa de quadros. <b>Recurso experimental</b>",
   TribuneUpgradeDescGreatPeopleWarning: "Sua tentativa atual possui figuras históricas. Você deve <b>renascer primeiro</b>. Melhorar para o ranque Quaestor irá reiniciar sua tentativa atual",
   TribuneUpgradeDescGreatPeopleWarningTitle: "Por Favor, Renasça Primeiro",
   TribuneUpgradeDescV4:
      "Você pode jogar o jogo completo como Tribuno se não planeja participar dos recursos online <b>opcionais</b>. Para adquirir acesso irrestrito aos recursos online, você precisará atualizar para Quaestor. <b>Esta é uma medida anti-bot para manter o jogo gratuito para todos.</b> No entanto, <b>ao atualizar para Quaestor</b> você pode transferir figuras históricas: <ul><li>Até o nível <b>3</b> para a Idade do Bronze, Ferro e Clássica</li><li>Até o nível <b>2</b> para a Idade Média, Renascimento e Era Industrial</li><li>Até o nível <b>1</b> para Guerras Mundiais, Guerra Fria e Era da Informação</li></ul>Fragmentos de Figuras Históricas acima do nível e níveis de <b>Sabedoria da Era</b> <b>não podem</b> ser transferidos.",
   TurnOffFullBuildings: "Desligar Todos os %{building} Com Armazenamento Cheio",
   TurnOnTimeWarpDesc: "Custa %{speed} distorções para cada segundo e acelera seu império em %{speed}x sua velocidade normal.",
   Tutorial: "Tutorial",
   TutorialPlayerFlag: "Escolha sua bandeira de jogador",
   TutorialPlayerHandle: "Escolha seu nome de jogador",
   TV: "TV",
   TVStation: "Estação de TV",
   UnclaimedGreatPersonPermanent: "Você tem <b>Figuras Históricas Permanentes</b> não coletadas, clique aqui para coletar",
   UnclaimedGreatPersonThisRun: "Você tem <b>Figuras Históricas Dessa Tentativa</b> não coletadas, clique aqui para coletar",
   UnexploredTile: "Espaço Inexplorado",
   UNGeneralAssemblyCurrent: "Atual Assembleia Geral da ONU #%{id}",
   UNGeneralAssemblyMultipliers: "<b>+%{count}</b> Multiplicadores de Produção, Capacidade de Trabalho e Armazenamento para <b>%{buildings}</b>",
   UNGeneralAssemblyNext: "Próxima Assembleia Geral da ONU #%{id}",
   UNGeneralAssemblyVoteEndIn: "Você pode alterar seu voto a qualquer momento antes do término da votação <b>%{time}</b>",
   UniqueBuildings: "Construções Únicas",
   UniqueTechMultipliers: "Multiplicadores de Tecnologia Exclusivos",
   UnitedNations: "Nações Unidas",
   UnitedNationsDesc: "Todos os edifícios de Nível IV, V e VI recebem +1 de Produção, Capacidade de Trabalhadores e Multiplicador de Armazenamento. Participe da Assembleia Geral da ONU e vote para um reforço adicional a cada semana.",
   University: "Universidade",
   UnlockableResearch: "Pesquisa Desbloqueável",
   UnlockBuilding: "Desbloquear",
   UnlockTechProgress: "Progresso",
   UnlockXHTML: "Desbloquear. <b>%{name}</b>",
   Upgrade: "Melhorar",
   UpgradeBuilding: "Melhorar",
   UpgradeBuildingNotProducingDescV2: "Este edifício está sendo atualizado - <b>a produção será interrompida até que a atualização seja concluída</b>",
   UpgradeTo: "Atualizar Para o Nível %{level}",
   Uranium: "Urânio",
   UraniumEnrichmentPlant: "Fábrica de Enriquecimento de Urânio",
   UraniumMine: "Mina de Urânio",
   Urbanization: "Urbanização",
   UserAgent: "Agente de Usuário: %{driver}",
   View: "Ver",
   ViewMenu: "Ver",
   ViewTechnology: "Ver",
   Vineyard: "Vinhedo",
   VirtualReality: "Realidade Virtual",
   Voltaire: "Voltaire",
   WallOfBabylon: "Muro da Babilônia",
   WallOfBabylonDesc: "Todos os edifícios recebem +N Multiplicador de Armazenamento. N = número de eras desbloqueadas / 2",
   WallStreet: "Wall Street",
   WallStreetDesc: "Todos os edifícios que produzem moedas, cédulas, títulos, ações e forex dentro do intervalo de 2 tiles recebem +N multiplicador de produção. N = Valor aleatório entre 1 e 5 que é diferente por edifício e muda a cada atualização do mercado. Dobra o efeito de John D. Rockefeller.",
   WaltDisney: "Walt Disney",
   Warehouse: "Armazém",
   WarehouseAutopilotSettings: "Configurações do Piloto Automático",
   WarehouseAutopilotSettingsEnable: "Habilitar Piloto Automático",
   WarehouseAutopilotSettingsRespectCapSetting: "Requer Armazenamento < Limite",
   WarehouseAutopilotSettingsRespectCapSettingTooltip: "O piloto automático transportará apenas recursos cuja quantidade armazenada esteja abaixo do limite",
   WarehouseDesc: "Transporta recursos específicos e proporciona armazenamento extra",
   WarehouseExtension: "Desbloqueie o modo de extensão de caravanas de armazém. Permita que armazéns adjacentes as caravanas sejam incluídos no comércio de jogadores.",
   WarehouseSettingsAutopilotDesc: "Esse Armazém irá usar sua capacidade ociosa para transportar recursos de construções que possuem armazenamento lotado. Capacidade ociosa atual: %{capacity}",
   WarehouseUpgrade: "Desbloqueia o modo piloto automático para o armazém. Transporte grátis entre o armazém e as construções adjacentes",
   WarehouseUpgradeDesc: "Transporte grátis entre o armazém e seus espaços adjacentes",
   Warp: "Distorção",
   WarpSpeed: "Velocidade da Distorção",
   Water: "Água",
   WellStockedTooltip: "Edifícios bem abastecidos são edifícios que têm recursos suficientes para sua produção, o que inclui edifícios que estão produzindo, que têm armazenamento completo ou que não estão produzindo devido à falta de trabalhadores.",
   WernherVonBraun: "Wernher von Braun",
   Westminster: "Westminster",
   Wheat: "Trigo",
   WheatFarm: "Campo de Trigo",
   WildCardGreatPersonDescV2: "Quando consumido, torne-se qualquer grande pessoa da mesma idade",
   WilliamShakespeare: "William Shakespeare",
   Wine: "Vinho",
   Winery: "Vinícola",
   WishlistSpaceshipIdle: "Wishlist Spaceship Idle",
   Wonder: "Maravilha",
   WonderBuilderCapacityDescHTML: "A <b>Capacidade de Contrutores</b> ao construir maravilhas é afetada pela <b>era</b> e <b>tecnologia</b> que desbloqueia a maravilha",
   WondersBuilt: "Maravilhas do Mundo Construídas",
   WondersUnlocked: "Maravilhas do Mundo Desbloqueadas",
   WonderUpgradeLevel: "Nível da Maravilha",
   Wood: "Madeira",
   Worker: "Trabalhador",
   WorkerCapacityMultiplier: "Multiplicador de Capacidade do Trabalhador",
   WorkerHappinessPercentage: "Multiplicador de Felicidade",
   WorkerMultiplier: "Capacidade de Trabalhadores",
   WorkerPercentagePerHappiness: "%{value}% de Multiplicador para Cada Felicidade",
   Workers: "Trabalhadores",
   WorkersAvailableAfterHappinessMultiplier: "Trabalhadores Pós Multiplicador de Felicidade",
   WorkersAvailableBeforeHappinessMultiplier: "Trabalhadores Pré Multiplicador de Felicidade",
   WorkersBusy: "Trabalhadores Ocupados",
   WorkerScienceProduction: "Produção de Ciência por Trabalhador",
   WorkersRequiredAfterMultiplier: "Trabalhadores Necessários",
   WorkersRequiredBeforeMultiplier: "Capacidade de Trabalhadores Necessária",
   WorkersRequiredForProductionMultiplier: "Capacidade de Produção por Trabalhador",
   WorkersRequiredForTransportationMultiplier: "Capacidade de Transporte por Trabalhador",
   WorkersRequiredInput: "Transporte",
   WorkersRequiredOutput: "Produção",
   WorldWarAge: "Guerras Mundiais",
   WorldWideWeb: "Rede Mundial de Computadores",
   WritersGuild: "Associação de Escritores",
   Writing: "Escrita",
   WuZetian: "Imperatriz Wu Zetian",
   WuZetianDesc: "+%{value} Multiplicador de Capacidade de Transporte",
   Xuanzang: "Xuanzang",
   YangtzeRiver: "Rio Yangtze",
   YangtzeRiverDesc:
      "Todos os edifícios que consomem água recebem +1 de Produção, Capacidade de Trabalhadores e Multiplicador de Armazenamento. Dobra o efeito de Zheng He (Figura Histórica). Cada nível de Imperatriz Permanente Wu Zetian (Figura Histórica) fornece +1 de Multiplicador de Armazenamento para todos os edifícios.",
   YearOfTheSnake: "Ano da Serpente",
   YearOfTheSnakeDesc:
      "Após ser completada, ao entrar em uma nova era, em vez de ganhar uma grande pessoa de cada era desbloqueada, ganhe a mesma quantidade de grandes pessoas na era atual. Todos os edifícios dentro de um raio de 2 quadrados recebem +1 Multiplicador de Produção. Esta maravilha pode ser atualizada, e cada atualização adicional fornece +1 Multiplicador de Produção para edifícios dentro de um raio de 2 quadrados. Esta maravilha só pode ser construída durante o período do Ano Novo Lunar (1.20 ~ 2.10)",
   YellowCraneTower: "Torre do Guindaste Amarelo",
   YellowCraneTowerDesc: "+1 escolha ao escolher figura histórica. Todos os edifícios dentro do alcance de 1 bloco recebem +1 Produção, Capacidade de Trabalhadores e Multiplicador de Armazenamento. Quando construído próximo ao Rio Yangtze, o alcance aumenta para 2 blocos.",
   YuriGagarin: "Yuri Gagarin",
   ZagrosMountains: "Montes Zagros",
   ZagrosMountainsDesc: "Todos os edifícios adjacentes que têm menos de 5 Multiplicadores de Produção recebem +2 Multiplicadores de Produção. Dobra o efeito de Nabucodonosor II (Figura Histórica).",
   ZahaHadid: "Zaha Hadid",
   ZahaHadidDesc: "+%{value} Multiplicador de Capacidade de Construtor",
   Zenobia: "Zenobia",
   ZenobiaDesc: "+%{value}h Armazenamento de Petra Warp",
   ZhengHe: "Zheng He",
   ZigguratOfUr: "Zigurate de Ur",
   ZigguratOfUrDescV2:
      "Cada 10 de felicidade (limitado) fornece +1 Multiplicador de Produção para todos os edifícios que não produzem trabalhadores e são desbloqueados em eras anteriores (máx. = número de eras desbloqueadas / 2). Maravilhas (incl. Naturais) não fornecem mais +1 de Felicidade. O efeito pode ser desativado.",
   Zoroaster: "Zoroaster",
   Zugspitze: "Zugspitze",
   ZugspitzeDesc: "Para cada era desbloqueada, ganhe um ponto que pode ser usado para fornecer um nível extra a qualquer Grande Pessoa que nascer nesta corrida",
};
