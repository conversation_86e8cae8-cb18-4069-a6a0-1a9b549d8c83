import type { Advisor } from "../../../shared/definitions/AdvisorDefinitions";
import Electricity from "../../images/Tutorial/Electricity.png";
import GreatPeople from "../../images/Tutorial/GreatPeople.png";
import Happiness from "../../images/Tutorial/Happiness.png";
import Science from "../../images/Tutorial/Science.png";
import Storage from "../../images/Tutorial/Storage.png";
import Tradition from "../../images/Tutorial/Tradition.png";
import Welcome1 from "../../images/Tutorial/Welcome1.png";
import Welcome2 from "../../images/Tutorial/Welcome2.png";
import Welcome3 from "../../images/Tutorial/Welcome3.png";
import Wonder from "../../images/Tutorial/Wonder.png";
import Worker from "../../images/Tutorial/Worker.png";

export const AdvisorImages: Record<Advisor, string> = {
   Happiness,
   Wonder,
   Science,
   Worker,
   Storage,
   Tradition,
   GreatPeople,
   Electricity,
   Welcome1,
   Welcome2,
   Welcome3,
};
