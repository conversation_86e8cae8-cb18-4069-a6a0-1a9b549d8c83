export const ES = {
   About: "Sobre CivIdle",
   AbuSimbel: "Abu Simbel",
   AbuSimbelDesc: "Duplica el efecto de Ramsés II. Todas las maravillas adyacentes obtienen +1 de felicidad",
   AccountActiveTrade: "Comercios activos",
   AccountChatBadge: "Insignia en el chat",
   AccountCustomColor: "Color personalizado",
   AccountCustomColorDefault: "Por defecto",
   AccountGreatPeopleLevelRequirement: "Required Great People Level",
   AccountLevel: "Rango de la cuenta",
   AccountLevelAedile: "Edil",
   AccountLevelConsul: "Cónsul",
   AccountLevelMod: "Moderador",
   AccountLevelPlayTime: "Tiempo de juego activo > %{requiredTime} (Tu tiempo de juego es %{actualTime})",
   AccountLevelPraetor: "Pretor",
   AccountLevelQuaestor: "Cuestor",
   AccountLevelSupporterPack: "Posee paquete de colaborador",
   AccountLevelTribune: "Tribuno",
   AccountLevelUpgradeConditionAnyHTML: "Para mejorar tu cuenta, solo necesitas cumplir <b>uno de los siguientes </b> criterios:",
   AccountPlayTimeRequirement: "Tiempo de Juego Requerido",
   AccountRankUp: "Mejorar Rango de Cuenta",
   AccountRankUpDesc: "All your progress will be carried over to your new rank",
   AccountRankUpTip: "Congratulations, your account is eligible for a higher rank - click here to upgrade!",
   AccountSupporter: "Comprar Supporter Pack",
   AccountTradePriceRange: "Rango de precio de comercios",
   AccountTradeTileReservationTime: "Reserva de casilla de comercio",
   AccountTradeTileReservationTimeDesc: "Este es el tiempo que tu casilla estará reservada desde la última vez que estiviste en línea. Despues de que el periodo de reserva termine, tu casilla estará disponible para otros jugadores",
   AccountTradeValuePerMinute: "Cantidad comerciable por minuto",
   AccountTypeShowDetails: "Ver detalles de la cuenta",
   AccountUpgradeButton: "Mejora al rango Cuestor",
   AccountUpgradeConfirm: "Mejorar cuenta",
   AccountUpgradeConfirmDescV2: "Actualizar tu cuenta <b>restablecerá tu carrera actual</b> y transferirá permanentemente a los personajes históricos dentro de los niveles permitidos. Esto <b>no</b> se puede deshacer. ¿Estás seguro de continuar?",
   Acknowledge: "Acknowledge",
   Acropolis: "Acrópolis",
   ActorsGuild: "Gremio de actores",
   AdaLovelace: "Ada Lovelace",
   AdamSmith: "Adam Smith",
   AdjustBuildingCapacity: "Capacidad de producción",
   AdvisorElectricityContent:
      "Las plantas de energía le brindan dos nuevos sistemas. El primero, 'Energía', está indicado por los mosaicos de rayos adyacentes a la planta de energía. Algunos edificios (comenzando con Radio en las Guerras Mundiales) tienen un indicador de 'requiere energía' en su lista de entradas. <b>Esto significa que deben construirse sobre una loseta de rayo para funcionar</b>. Los edificios que requieren energía y la tienen, también transmitirán energía a las losas adyacentes a ese edificio, por lo que pueden alimentarse entre sí siempre que al menos una esté tocando una planta de energía.<br><br>El otro sistema de electrificación se puede aplicar a <b>cualquier edificio</b> en cualquier lugar del mapa, siempre y cuando no produzca ciencia ni trabajadores. Esto utiliza la energía generada por la central eléctrica para aumentar tanto el consumo como la producción del edificio. Más niveles de electrificación requieren cantidades cada vez mayores de energía. Electrificar edificios que también 'requieren energía' es más eficiente que electrificar los que no la tienen.",
   AdvisorElectricityTitle: "Energía y electrificación",
   AdvisorGreatPeopleContent:
      "Cada vez que entres una nueva edad tecnológica, podrás seleccionar un Personaje Histórico, tanto de esa edad como de la edad anterior. Esos Personajes Históricos dan bonificaciones que aumentan la producción, la ciencia, la felicidad y muchas otras cosas.<br><br>Esas bonificaciones son permanentes para el resto de la partida actual. Cuando renazcas, todos esos Personajes Históricos se volverán permanentes, y sus bonificaciones durarán para siempre.<br><br>Elegir el mismo Personaje Histórico se acumulará con tus permanentes y las bonificaciones que tengas actualmente, y cuando renazcas con los duplicados, los extra se guardarán y podrán ser usados para mejorar las bonificaciones permanentes. Esto es accessible mediante el menú <b>Gestionar personajes históricos</b> en tu edificio principal.",
   AdvisorGreatPeopleTitle: "Personaje Histórico",
   AdvisorHappinessContent:
      "La Felicidad es una mecánica central en CivIdle la cual limita tu expansión. Ganas felicidad desbloqueando nueva tecnología, avanzando a nuevas edades, construyendo monumentos, de las bonificaciones de algunos Personajes Históricos, y otras pocas formas que descubrirás mientras aprendes. <b>Cada nuevo edificio cuesta 1 de felicidad</b>. Por cada punto por encima / por debajo de 0 de felicidad, consigues un bonus o penalización del 2% para tus trabajadores totales (el máximo es -50 y +50 de felicidad). Puedes ver un desglose más detallado de tu felicidad en <b>la sección de Felicidad de tu Edificio Principal</b>.",
   AdvisorHappinessTitle: "Manten tu población contenta",
   AdvisorOkay: "¡Entendido, gracias!",
   AdvisorScienceContent:
      "Tus trabajadores ocupados generan ciencia, lo que te permite desbloquear nuevas tecnologías y avanzar tu civilización. Puedes acceder al menú de investigación de varias formas: haciendo clic en el medidor de ciencia, accediendo a tus tecnologías desbloqueables en tu Edificio Principal, o usando el menú 'Ver'. Todas estas opciones te llevarán al árbol de tecnología, donde podrás ver todas las tecnologías, así como la cantidad de ciencia requerida para cada una. Si tienes suficiente ciencia para aprender una nueva tecnología, simplemente haz clic en ella y presiona 'desbloquear' en el menú lateral. <b>Cada nuevo nivel y era de tecnología requiere cada vez más ciencia, pero también desbloquearás nuevas y mejores formas de obtener ciencia.</b>",
   AdvisorScienceTitle: "Descubrimiento científico!",
   AdvisorSkipAllTutorials: "Saltar todos los tutoriales",
   AdvisorStorageContent:
      "Aunque los edificios tienen una cantidad decente de almacenamiento, pueden llenarse, especialmente si se dejan inactivos durante mucho tiempo. <b>Cuando los edificios están llenos, ya no pueden producir</b>. Esto no siempre es un problema, ya que claramente tienes una gran reserva si el edificio está lleno. Pero generalmente es mejor mantener las cosas produciendo.Una forma de solucionar el almacenamiento completo es mediante un almacén. Cuando construyes un almacén, obtienes un menú de cada producto que has descubierto y puedes configurar el almacén para extraer cualquier producto en cualquier cantidad, siempre que el total para todos los productos esté dentro de lo que el almacén puede manejar según su nivel y multiplicador de almacenamiento.Una manera sencilla de configurar un almacén es marcar cada producto que desees importar en el almacén y usar los botones de 'redistribuir entre seleccionados' para dividir equitativamente tu tasa de importación y almacenamiento. Si deseas que los edificios también puedan extraer del almacén, asegúrate de activar la opción de 'exportar por debajo de la cantidad máxima'.",
   AdvisorStorageTitle: "Almacenamiento y Almacenes",
   AdvisorTraditionContent:
      "Algunas maravillas (<b>Chogha Zanbil, Templo de Luxor, Big Ben</b>) proporcionan acceso a un nuevo conjunto de opciones, permitiéndote personalizar el camino de tu renacimiento. Cada una te permite elegir entre 1 de 4 opciones para la tradición, religión e ideología de tu civilización, respectivamente. Una vez que elijas una, esa elección quedará bloqueada para ese renacimiento, aunque podrás seleccionar otras en futuros renacimientos. Una vez elegidas, cada una también puede mejorarse varias veces proporcionando los recursos necesarios. Las recompensas en cada nivel son acumulativas, por lo que si el Nivel 1 da +1 de producción a X y el Nivel 2 da +1 de producción a X, en el Nivel 2 tendrás un total de +2 de producción a X.",
   AdvisorTraditionTitle: "Elegir Caminos y Maravillas Mejorables",
   AdvisorWonderContent:
      "Las maravillas son edificios especiales que proporcionan efectos globales que pueden tener un impacto significativo en tu juego. Además de sus funciones listadas, todas las maravillas también otorgan +1 de felicidad. Debes tener cuidado, ya que <b>las maravillas requieren MUCHOS materiales y tienen una capacidad de construcción más alta de lo normal</b>. Esto significa que pueden agotar fácilmente tus reservas de insumos necesarios, dejando a tus otros edificios sin recursos. <b>Puedes activar y desactivar cada insumo libremente</b>, permitiéndote construir en etapas mientras acumulas suficientes materiales para que todo siga funcionando.",
   AdvisorWonderTitle: "Maravillas del mundo",
   AdvisorWorkerContent:
      "Cada vez que un edificio produce o transporta bienes, esto requiere trabajadores. Si no tienes suficientes trabajadores disponibles, algunos edificios no podrán funcionar en ese ciclo. La solución obvia para esto es aumentar tu total de trabajadores disponibles construyendo o mejorando estructuras que generan trabajadores (Cabaña/Casa/Apartamento/Condominio).<b>Sin embargo, ten en cuenta que los edificios se apagan mientras se están mejorando y no pueden proporcionar ninguno de sus recursos, incluidos los trabajadores, por lo que quizá quieras mejorar solo un edificio de vivienda a la vez.</b> Un buen objetivo para las primeras etapas del juego es mantener aproximadamente al 70% de tus trabajadores ocupados. Si más del 70% están ocupados, mejora/construye viviendas. Si menos del 70% están ocupados, expande la producción.",
   AdvisorWorkerTitle: "Gestión de Trabajadores",
   Aeschylus: "Esquilo",
   Agamemnon: "Agamenón",
   AgeWisdom: "Sabiduría de la Era",
   AgeWisdomDescHTML: "Cada nivel de Sabiduría de la Era proporciona <b>un nivel equivalente</b> de Personajes Históricos Permanentes elegibles de esa era; se puede mejorar con fragmentos de Personajes Históricos Permanentes elegibles.",
   AgeWisdomGreatPeopleShardsNeeded: "Necesitas %{amount} más fragmentos de personajes históricos para la próxima mejora de la Sabiduría de la Era.",
   AgeWisdomGreatPeopleShardsSatisfied: "Tienes suficientes fragmentos de personajes históricos para la próxima mejora de la Sabiduría de la Era.",
   AgeWisdomNeedMoreGreatPeopleShards: "Necesitas más Fragmentos de Personajes Históricos",
   AgeWisdomNotEligible: "Este Personaje Históricos no es elegible para la Sabiduría de la Era.",
   AgeWisdomSource: "%{age} Sabiduría: %{person}",
   AgeWisdomUpgradeWarningHTMLV3: "Age Wisdom <b>does not carry over</b> when upgrading from Tribune to Quaestor",
   AGreatPersonIsBorn: "Ha nacido un Personaje Histórico",
   AircraftCarrier: "Portaviones",
   AircraftCarrierYard: "Puerto de portaviones",
   Airplane: "Avión",
   AirplaneFactory: "Fábrica de aviones",
   Akitu: "Akitu: El Zigurat de Ur y el Río del Éufrates se aplican a los edificios desbloqueados en la edad actual",
   AlanTuring: "Alan Turing",
   AlanTuringDesc: "+%{value} Ciencia de trabajadores inactivos",
   AlbertEinstein: "Albert Einstein",
   Alcohol: "Alcohol",
   AldersonDisk: "Alderson Disk",
   AldersonDiskDesc: "+25 Felicidad. Esta maravilla se puede actualizar y cada mejora adicional proporciona +5 de felicidad.",
   Alloy: "Aleación",
   Alps: "Alpes",
   AlpsDesc: "Cada 10 niveles de una construcción se obtiene +1 de capacidad de producción (+1 multiplicador de consumo, +1 multiplicador de producción)",
   Aluminum: "Aluminio",
   AluminumSmelter: "Fundición de aluminio",
   AmeliaEarhart: "Amelia Earhart",
   American: "American",
   AndrewCarnegie: "Andrew Carnegie",
   AngkorWat: "Angkor Wat",
   AngkorWatDesc: "Todas las construcciones adyacentes obtienen +1 multiplicador de capacidad de trabajo. Añade 1000 trabajadores",
   AntiCheatFailure: "El rango de su cuenta ha sido restringido debido a que <b>no pasó la verificación anti-trampas</b>. Ponte en contacto con el desarrollador si quieres apelar a esta decisión.",
   AoiMatsuri: "Aoi Matsuri: Mount Fuji generates double the warp",
   Apartment: "Apartamento",
   Aphrodite: "Afrodita",
   AphroditeDescV2: "+1 multiplicador de capacidad de construcción para cada nivel al mejorar edificios por encima del nivel 20. Todas los personajes históricos permanentes desbloqueadas de la Edad Clásica obtienen +1 nivel en esta carrera.",
   ApolloProgram: "Programa Apollo",
   ApolloProgramDesc: "Todas las fábricas de cohetes obtienen +2 en producción, capacidad de trabajadores y multiplicador de almacenamiento. Las fábricas de satélites, fábricas de naves espaciales y silos de misiles nucleares obtienen +1 multiplicador de producción por cada fábrica de cohetes adyacente.",
   ApplyToAll: "Aplicar a todos",
   ApplyToAllBuilding: "Aplicar a todos %{building}",
   ApplyToBuildingInTile: "Aplicar a todos %{building} dentro de %{tile} casillas",
   ApplyToBuildingsToastHTML: "Aplicado con éxito a <b>%{count} %{building}</b>",
   Aqueduct: "Acueducto",
   ArcDeTriomphe: "Arco del Triunfo",
   ArcDeTriompheDescV2: "Every 1 happiness (capped) provides +1 builder capacity to all buildings",
   Archimedes: "Arquímedes",
   Architecture: "Arquitectura",
   Aristophanes: "Aristófanes",
   AristophanesDesc: "+%{value} de felicidad",
   Aristotle: "Aristotle",
   Arithmetic: "Artimética",
   Armor: "Armadura",
   Armory: "Taller de armaduras",
   ArtificialIntelligence: "Inteligencia artificial",
   Artillery: "Artillería",
   ArtilleryFactory: "Fabrica de artillería",
   AshokaTheGreat: "Ashoka the Great",
   Ashurbanipal: "Ashurbanipal",
   Assembly: "Ensamblaje",
   Astronomy: "Astronomía",
   AtomicBomb: "Bomba atómica",
   AtomicFacility: "Atomic Facility",
   AtomicTheory: "Teoría Atómica",
   Atomium: "Atomium",
   AtomiumDescV2:
      "Todos los edificios que producen ciencia dentro del rango de 2 casillas obtienen +5 multiplicador de producción. Genera ciencia que sea igual a la producción científica dentro del rango de 2 casillas. Cuando esté completo, genere ciencia por única vez equivalente al costo de la tecnología desbloqueada más cara.",
   Autocracy: "Autocracia",
   Aviation: "Aviación",
   Babylonian: "Babylonian",
   BackToCity: "Volver a la ciudad",
   BackupRecovery: "Recuperar copia de seguridad",
   Bakery: "Panadería",
   Ballistics: "Ballistics",
   Bank: "Banco",
   Banking: "Banca",
   BankingAdditionalUpgrade: "Todas las construcciones de nivel 10 o mayor obtienen +1 multiplicador de almacenamiento",
   Banknote: "Billete",
   BaseCapacity: "Capacidad Base",
   BaseConsumption: "Consumo base",
   BaseMultiplier: "Multiplicador base",
   BaseProduction: "Producción base",
   BastilleDay: "Bastille Day: Double the effect of Centre Pompidou and Arc de Triomphe. Double the Culture generation from Mont Saint-Michel",
   BatchModeTooltip: "%{count} edificios están actualmente seleccionados. La actualización se aplicará a todos los edificios seleccionados.",
   BatchSelectAllSameType: "Todos del mismo tipo",
   BatchSelectAnyType1Tile: "Cualquier tipo en 1 casilla",
   BatchSelectAnyType2Tile: "Cualquier tipo en 2 casillas",
   BatchSelectAnyType3Tile: "Cualquier tipo en 3 casillas",
   BatchSelectSameType1Tile: "Mismo tipo en 1 casilla",
   BatchSelectSameType2Tile: "Mismo tipo en 2 casilla",
   BatchSelectSameType3Tile: "Mismo tipo en 3 casilla",
   BatchSelectSameTypeSameLevel: "Mismo tipo, Mismo nivel",
   BatchSelectThisBuilding: "Este edificio",
   BatchStateSelectActive: "Activo",
   BatchStateSelectAll: "Todos",
   BatchStateSelectTurnedFullStorage: "Almacenaje completo",
   BatchStateSelectTurnedOff: "Desactivado",
   BatchUpgrade: "Actualización por lotes",
   Battleship: "Buque de guerra",
   BattleshipBuilder: "Constructor de buques de guerra",
   BigBen: "Big Ben",
   BigBenDesc: "+2 Ciencia de trabajadores ocupados. Elige una ideología de imperio y desbloquea más impulso con cada elección.",
   Biplane: "Biplano",
   BiplaneFactory: "Fábrica de biplanos",
   Bitcoin: "Bitcoin",
   BitcoinMiner: "Minero de Bitcoins",
   BlackForest: "Black Forest",
   BlackForestDesc: "Cuando es descubierto, revela todas las casillas de Madera en el mapa. Spawn wood on adjacent tiles. Todas las construcciones que consumen Madera o Lumber obtienen +5 de Multiplicador de Producción.",
   Blacksmith: "Herrero",
   Blockchain: "Blockchain",
   BlueMosque: "Blue Mosque",
   BlueMosqueDesc: "All wonders provide +1 Production, Worker Capacity and Storage Multiplier to adjacent buildings. When constructed next to Hagia Sophia, provide extra +1 Production, Worker Capacity and Storage Multiplier",
   BobHope: "Bob Hope",
   BobHopeDesc: "+%{value} Felicidad",
   Bond: "Bono",
   BondMarket: "Mercado de bonos",
   Book: "Libro",
   BoostCyclesLeft: "Boost Cycles Left",
   BoostDescription: "+%{value} %{multipliers} para %{buildings}",
   Borobudur: "Borobudur",
   BorobudurDesc: "Borobudur",
   BranCastle: "Castillo de Bran",
   BranCastleDesc: "Castillo de Bran",
   BrandenburgGate: "Puerta de Brandeburgo",
   BrandenburgGateDesc: "Todas las minas de carbón y pozos de petróleo obtienen +1 multiplicador de producción, almacenamiento y capacidad de trabajo. Las refinerias de petróleo obtienen +1 multiplicador de producción, almacenamiento y capacidad de trabajo por cada casilla de petróleo adyacente",
   Bread: "Pan",
   Brewery: "Cervecería",
   Brick: "Ladrillo",
   Brickworks: "Fábrica de ladrillos",
   BritishMuseum: "British Museum",
   BritishMuseumChooseWonder: "Choose a Wonder",
   BritishMuseumDesc: "Tras ser construido, puede transformarse en otra Maravilla de otras Civilizaciones.",
   BritishMuseumTransform: "Transformar",
   Broadway: "Broadway",
   BroadwayCurrentlySelected: "Seleccionado actualmente",
   BroadwayDesc: "Nace un Personaje Histórico de la época actual y un Personaje Histórico de la época anterior. Selecciona un Personaje Histórico y duplica su efecto.",
   BronzeAge: "Edad de bronce",
   BronzeTech: "Bronce",
   BuddhismLevelX: "Budismo %{level}",
   Build: "Construir",
   BuilderCapacity: "Capacidad de construcción",
   BuildingColor: "Color de la construcción",
   BuildingColorMatchBuilding: "Copiar color de la construcción",
   BuildingColorMatchBuildingTooltip: "Copia el color de las construcciones de producción a sus recursos. Si hay más de una opción, elige una de forma aleatoria",
   BuildingDefaults: "Building Defaults",
   BuildingDefaultsCount: "%{count} properties are overriden in building default",
   BuildingDefaultsRemove: "Clear all property overrides",
   BuildingEmpireValue: "Building Empire Value / Resource Empire Value",
   BuildingMultipliers: "Aumento",
   BuildingName: "Nombre",
   BuildingNoMultiplier: "%{building} <b>no está afectado</b> por ningún multiplicador (producción, capacidad de los trabajadores, almacenaje, etc)",
   BuildingSearchText: "Escribe el nombre de una construcción o recurso para buscar",
   BuildingTier: "Rango",
   Cable: "Cable",
   CableFactory: "Fábrica de cables",
   Calendar: "Calendario",
   CambridgeUniversity: "Cambridge University",
   CambridgeUniversityDesc: "+1 Age Wisdom level for Renaissance and ages after",
   CambridgeUniversitySource: "Cambridge University (%{age})",
   Cancel: "Cancelar",
   CancelAllUpgradeDesc: "Cancelar todas las %{building} mejoras",
   CancelUpgrade: "Cancelar mejora",
   CancelUpgradeDesc: "Todos los recursos que ya han sido transportados permanecerán en el almacén",
   Cannon: "Cañón",
   CannonWorkshop: "Taller de cañones",
   CannotEarnPermanentGreatPeopleDesc: "Debido a que es una partida de prueba, no se pueden conseguir personajes históricos permanentes",
   Capitalism: "Capitalismo",
   Cappadocia: "Cappadocia",
   CappadociaDesc: "All buildings within 3 tile range get +1 Production, Worker Capacity and Storage Multiplier for every level above Level 30",
   Car: "Coche",
   Caravansary: "Caravana",
   CaravansaryDesc: "Comercia recursos con otros jugadores y provee almacenamiento adicional",
   Caravel: "Carabela",
   CaravelBuilder: "Astillero de carabelas",
   CarFactory: "Fábrica de coches",
   CarlFriedrichGauss: "Carl Friedrich Gauss",
   CarlFriedrichGaussDesc: "+%{idle} Ciencia de Trabajadores Inactivos. +%{busy} Ciencia de Trabajadores Ocupados",
   CarlSagan: "Carl Sagan",
   Census: "Censo",
   CentrePompidou: "Centre Pompidou",
   CentrePompidouDesc:
      "Once constructed, all buildings get +1 Production and +2 Storage Multiplier. The wonder will persist if the current run reaches Information Age and the next run is a different civilization. The wonder gets +1 level at rebirth for each run that reaches Information Age with a unique civilization. Each level provides +1 Production and +2 Storage Multiplier. The value of this wonder is excluded from total empire value and British Museum cannot transform into this wonder",
   CentrePompidouWarningHTML: "Centre Pompidou will disappear if you rebirth as <b>%{civ}</b>",
   CerneAbbasGiant: "Cerne Abbas Giant",
   CerneAbbasGiantDesc: "Un Personaje histórico de la Era actual nacerá cuando la Maravilla sea completada.",
   ChangePlayerHandle: "Cambiar",
   ChangePlayerHandleCancel: "Cancelar",
   ChangePlayerHandledDesc: "Elige un nombre de jugador único de entre 5 a 16 caracteres. Tu nombre de jugador sólo puede contener letras y números",
   Chariot: "Carruaje",
   ChariotWorkshop: "Taller de carruajes",
   Charlemagne: "Carlomagno",
   CharlesDarwin: "Charles Darwin",
   CharlesDarwinDesc: "+%{value} de ciencia por trabajadores ocupados",
   CharlesMartinHall: "Charles Martin Hall",
   CharlesParsons: "Charles Parsons",
   CharlieChaplin: "Charlie Chaplin",
   CharlieChaplinDesc: "+%{value} Felicidad",
   Chat: "Chat",
   ChatChannel: "Canales del chat",
   ChatChannelLanguage: "Idioma",
   ChatHideLatestMessage: "Ocultar el contenido del último mensaje",
   ChatNoMessage: "No hay mensajes",
   ChatReconnect: "Desconectado, reconectando...",
   ChatSend: "Enviar",
   CheckInAndExit: "Check In And Exit",
   CheckInCloudSave: "Check In Save",
   CheckOutCloudSave: "Check Out Save",
   Cheese: "Queso",
   CheeseMaker: "Quesería",
   Chemistry: "Química",
   ChesterWNimitz: "Chester W. Nimitz",
   ChichenItza: "Chichén Itzá",
   ChichenItzaDesc: "Todas las construcciones adyacentes obtienen +1 multiplicador de producción, almacenamiento y capacidad de trabajo",
   Chinese: "China",
   ChoghaZanbil: "Chogha Zanbil",
   ChoghaZanbilDescV2: "Elige una tradición del imperio, desbloquea impulsos con cada elección",
   ChooseGreatPersonChoicesLeft: "Te quedan por elegir %{count} personajes",
   ChristianityLevelX: "Cristiandad %{level}",
   Church: "Iglesia",
   CircusMaximus: "Circo Máximo",
   CircusMaximusDescV2: "+5 de felicidad. Todos los gremios de músicos, gremios de escritores y gremios de pintores obtienen +1 multiplicador de producción y de almacenamiento",
   CityState: "Ciudad Estado",
   CityViewMap: "Ciudad",
   CivGPT: "CivGPT",
   CivIdle: "CivIdle",
   CivIdleInfo: "Presentado con orgullo por Fish Pond Studio",
   Civilization: "Civilización",
   CivilService: "Servicio Civil",
   CivOasis: "CivOasis",
   CivTok: "CivTok",
   ClaimedGreatPeople: "Personajes históricos reclamados",
   ClaimedGreatPeopleTooltip: "You have %{total} great people at rebirth, %{claimed} of them are already claimed",
   ClassicalAge: "Edad Antigua",
   ClearAfterUpdate: "Borrar todos los comercios cuando el mercado se actualiza",
   ClearSelected: "Borrar seleccionados",
   ClearSelection: "Borrar",
   ClearTransportPlanCache: "Clear Transport Plan Cache",
   Cleopatra: "Cleopatra",
   CloneFactory: "Fabrica de clonación",
   CloneFactoryDesc: "Clonar cualquier recurso",
   CloneFactoryInputDescHTML: "Clone Factory can only clone <b>%{res}</b> directly transported from <b>%{buildings}</b>",
   CloneLab: "Laboratorio de clonación",
   CloneLabDesc: "Convierte cualquier recurso en ciencia",
   CloneLabScienceMultiplierHTML: "Production multipliers that <b>only apply to science production buildings</b> (e.g. production multipliers from Atomium) <b>do not apply</b> to Clone Lab",
   Cloth: "Tela",
   CloudComputing: "Computación en la nube",
   CloudSaveRefresh: "Refresh",
   CloudSaveReturnToGame: "Return To Game",
   CNTower: "CN Tower",
   CNTowerDesc: "All movie studios, radio stations and TV stations are exempt from -1 Happiness. All buildings unlocked in World Wars and Cold War get +N Production, Worker Capacity and Storage Multiplier. N = Difference between the tier and the age of the building",
   Coal: "Carbón",
   CoalMine: "Mina de carbón",
   CoalPowerPlant: "Central energética de carbón",
   Coin: "Moneda",
   CoinMint: "Fábrica de monedas",
   ColdWarAge: "Guerra fría",
   CologneCathedral: "Cologne Cathedral",
   CologneCathedralDesc:
      "When constructed, generate one-time science equivalent to the cost of the most expensive technology in the current age. All buildings that produce science (excluding Clone Lab) get +1 Production Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Production Multiplier to all buildings that produce science (excluding Clone Lab)",
   Colonialism: "Colonialismo",
   Colosseum: "Coliseo",
   ColosseumDescV2: "Chariot Workshops are exempt from -1 happiness. Consumes 10 chariots and produce 10 happiness. Each unlocked age gives 2 extra happiness",
   ColossusOfRhodes: "Coloso de Rodas",
   ColossusOfRhodesDesc: "Todas las construcciones adyacentes que no producen trabajadores obtienen +1 de felicidad",
   Combustion: "Combustión",
   Commerce4UpgradeHTMLV2: "When unlocked, all <b>adjacent banks</b> get free upgrade to <b>level 30</b>",
   CommerceLevelX: "Commerce %{level}",
   Communism: "Communism",
   CommunismLevel4DescHTML: "Un Personaje Histórico de la <b>Industrial Age</b>, y un Personaje histórico de la <b>World Wars Age</b> nacen.",
   CommunismLevel5DescHTML: "Un Personaje histórico de la <b>Cold War Age</b> nace. Cuando entres a una nueva Era, obtendrás <b>2 additional</b> Personajes históricos de dicha Era",
   CommunismLevelX: "Communismo nivel %{level}",
   Computer: "Computación",
   ComputerFactory: "Fábrica de Computadores",
   ComputerLab: "Computer Lab",
   Concrete: "Hormigón",
   ConcretePlant: "Planta de cemento",
   Condo: "Condo",
   ConfirmDestroyResourceContent: "Estás a punto de destruir %{amount} %{resource}. Esto no puede deshacerse",
   ConfirmNo: "No",
   ConfirmYes: "Sí",
   Confucius: "Confucio",
   ConfuciusDescV2: "+%{value} Ciencia de Todos los Trabajadores si más del 50% de los trabajadores están ocupados y menos que el 50% de los trabajadores ocupados trabajan en trasportación",
   ConnectToADevice: "Connect To A Device",
   Conservatism: "Conservatismo",
   ConservatismLevelX: "Nivel de Conservatismo %{level}",
   Constitution: "Constitución",
   Construction: "Construcción",
   ConstructionBuilderBaseCapacity: "Capacidad base",
   ConstructionBuilderCapacity: "Capacidad de construcción",
   ConstructionBuilderMultiplier: "Multiplicador de capacidad",
   ConstructionBuilderMultiplierFull: "Multiplicador de capacidad de construcción",
   ConstructionCost: "Coste de construcción: %{cost}",
   ConstructionDelivered: "Entregado",
   ConstructionPriority: "Prioridad de construcción",
   ConstructionProgress: "Progreso",
   ConstructionResource: "Recurso",
   Consume: "Consume",
   ConsumeResource: "Consume: %{resource}",
   ConsumptionMultiplier: "Multiplicador de consumo",
   ContentInDevelopment: "Contenido en desarrollo",
   ContentInDevelopmentDesc: "Este contenido está en desarrollo, estará disponible en una futura actualización del juego. ¡Estate pendiente!",
   Copper: "Cobre",
   CopperMiningCamp: "Mina de cobre",
   CosimoDeMedici: "Cosme de Médici",
   Cotton: "Algodón",
   CottonMill: "Telar",
   CottonPlantation: "Plantación de algodón",
   Counting: "Cálculo",
   Courthouse: "Palacio de justicia",
   CristoRedentor: "Cristo Redentor",
   CristoRedentorDesc: "Todos los edificios dentro de un rango de 2 casillas están exentos de -1 de Felicidad.",
   CrossPlatformAccount: "Plataforma de la Cuenta",
   CrossPlatformConnect: "Conectar",
   CrossPlatformSave: "Guardado en Plataformas Cruzadas",
   CrossPlatformSaveLastCheckIn: "Última Comprobación",
   CrossPlatformSaveStatus: "Current Status",
   CrossPlatformSaveStatusCheckedIn: "Checked In",
   CrossPlatformSaveStatusCheckedOut: "Checked Out on %{platform}",
   CrossPlatformSaveStatusCheckedOutTooltip: "Your cross platform save has been checked out on another platform, you have to check in on that platform before you can check out on this platform",
   Cultivation4UpgradeHTML: "Un Personaje Histórico de la <b>Edad del Renacimiento</b> ha nacido",
   CultivationLevelX: "%{level} de Cultivo",
   Culture: "Cultura",
   Culus: "Cülus: Double the effect of Cappadocia. Mount Ararat's effect becomes based on square root of Effective Great People Level, instead of cubic root",
   CurrentLanguage: "Español",
   CurrentPlatform: "Current Platform",
   CursorBigOldFashioned: "3D (Grande)",
   CursorOldFashioned: "3D",
   CursorStyle: "Estilo del cursor",
   CursorStyleDescHTML: "Cambia el estilo del cursor. <b>Requiere reiniciar el juego para que surta efecto</b>",
   CursorSystem: "Sistema",
   Cycle: "Ciclo",
   CyrusII: "Ciro II",
   DairyFarm: "Granja lechera",
   DefaultBuildingLevel: "Nivel de edificio predeterminado",
   DefaultConstructionPriority: "Prioridad de construcción predeterminada",
   DefaultProductionPriority: "Prioridad de producción predeterminada",
   DefaultStockpileMax: "Reserva máxima predeterminada",
   DefaultStockpileSettings: "Capacidad de entrada de reserva predeterminada",
   DeficitResources: "Deficit de recursos",
   Democracy: "Democracia",
   DemolishAllBuilding: "Demolish All %{building} Within %{tile} Tile",
   DemolishAllBuildingConfirmContent: "Are you sure about demolishing %{count} %{name}?",
   DemolishAllBuildingConfirmTitle: "Demolish %{count} Building(s)?",
   DemolishBuilding: "Demoler edificio",
   DennisRitchie: "Dennis Ritchie",
   Deposit: "Depósito de recursos",
   DepositTileCountDesc: "%{count} casilla(s) de %{deposit} se pueden encontrar en %{city}",
   Dido: "Dido",
   Diplomacy: "Diplomacia",
   DistanceInfinity: "Ilimitado",
   DistanceInTiles: "Distancia (en casillas)",
   DolmabahcePalace: "Dolmabahçe Palace",
   Drilling: "Perforación",
   DukeOfZhou: "Duque de Zhou",
   DuneOfPilat: "Dune of Pilat",
   DuneOfPilatDesc: "En cada Era, duplica la Sabiduría de la Era de la anterior Era.",
   DynamicMultiplierTooltip: "Este multiplicador es dinámico: no afectará a los trabajadores ni al almacenamiento.",
   Dynamite: "Dinamita",
   DynamiteWorkshop: "Taller de dinamita",
   DysonSphere: "Esfera de Dyson",
   DysonSphereDesc: "Todos los edificios obtienen +5 Multiplicador de Producción. Esta maravilla se puede mejorar y cada mejora adicional proporciona +1 Multiplicador de Producción a todos los edificios",
   EasterBunny: "Conejo de Pascua",
   EasterBunnyDesc: "Once constructed, 10% of the extra Great People at Rebirth from this run will carry forward to the next run and are born after building the Easter Bunny in the new run. This wonder can only be constructed during April",
   EastIndiaCompany: "Compañía Británica de las Indias Orientales",
   EastIndiaCompanyDescV2:
      "This wonder accumulates the total value of your completed player trade transactions. For every 2,000 trade value, all buildings adjacent to caravansaries get a +0.5 Production Multiplier for 1 cycle. This wonder can be upgraded and each upgrade provides an additional +0.5 Production Multiplier. A trade transaction counts when you either fulfill another player's trade request or when your own trade request is fulfilled. Multiple boosts stack by extending the duration",
   Education: "Educación",
   EffectiveGreatPeopleLevel: "Effective Great People Level",
   EffectiveGreatPeopleLevelDesc: "Effective great people level is the sum of all permanent great people level and age wisdom level. It measures the effect boost provided by great people and age wisdom",
   Egyptian: "Egyptian",
   EiffelTower: "Torre Eiffel",
   EiffelTowerDesc: "Todas las plantas siderúrgicas adjacentes obtienen +N multiplicadores de producción, almacenamiento y capacidad de trabajo. N = Número de plantas siderúrgicas adyacentes",
   Elbphilharmonie: "Elbphilharmonie",
   ElbphilharmonieDesc: "All buildings within 3 tile range get +1 Production Multiplier for each adjacent working building that has different tier",
   Electricity: "Electricidad",
   Electrification: "Electrificación",
   ElectrificationPowerRequired: "Energía requerida",
   ElectrificationStatusActive: "Activo",
   ElectrificationStatusDesc: "Se pueden electrificar tanto los edificios que requieren energía como los que no la necesitan. Sin embargo, los edificios que requieren energía proporcionan una mayor eficiencia de electrificación.",
   ElectrificationStatusNoPowerV2: "No hay suficiente energía",
   ElectrificationStatusNotActive: "Inactivo",
   ElectrificationStatusV2: "Electrification Status",
   ElectrificationUpgrade: "Desbloquea la electrificación. Permite a las construcciones consumir energía para aumentar la producción",
   Electrolysis: "Electrolisis",
   ElvisPresley: "Elvis Presley",
   ElyseePalace: "Élysée Palace",
   EmailDeveloper: "Email Desarrollador",
   Embassy: "Embajada",
   EmperorWuOfHan: "Emperador Wu de Han",
   EmpireValue: "Valor del imperio",
   EmpireValueByHour: "Valor del imperio por hora",
   EmpireValueFromBuilding: "Valor del imperio por edificios",
   EmpireValueFromBuildingsStat: "De Edificios",
   EmpireValueFromResources: "De Recursos",
   EmpireValueFromResourcesStat: "De Recursos",
   EmpireValueIncrease: "Empire Value Increase",
   EmptyTilePageBuildLastBuilding: "Construir la última construcción",
   EndConstruction: "Cancelar construcción",
   EndConstructionDescHTML: "Cuando cancelas una construcción <b>no se devolverán</b> los recursos que ya hayan sido usados",
   Engine: "Motor",
   Engineering: "Ingeniería",
   English: "English",
   Enlightenment: "Ilustración",
   Enrichment: "Enriquecimiento",
   EnricoFermi: "Enrico Fermi",
   EstimatedTimeLeft: "Tiempo restante estimado",
   EuphratesRiver: "Río del Éufrates",
   EuphratesRiverDesc:
      "Cada 10% de trabajadores ocupados que estén en producción (no transportando) proporciona +1 Multiplicador de Producción a todos los edificios que no produzcan trabajadores (máximo = número de eras desbloqueadas / 2). Cuando se construye el Jardín Colgante junto a este, el Jardín Colgante obtiene un efecto +1 por cada era después de que se desbloquee. Cuando se descubre, genera agua en todas las casillas adyacentes que no tengan depósitos",
   ExpansionLevelX: "Expansión %{level}",
   Exploration: "Exploración",
   Explorer: "Explorador",
   ExplorerRangeUpgradeDesc: "Aumenta el rango del explorador a %{range}",
   ExploreThisTile: "Enviar un Explorador",
   ExploreThisTileHTML: "Un explorador explorará <b>esta casilla y las casillas adyacentes</b>. Los exploradores se generan en %{name}. Tienes %{count} exploradores restantes",
   ExtraGreatPeople: "%{count} Personajes Históricos adicionales",
   ExtraGreatPeopleAtReborn: "Personajes Históricos adicionales al renacer",
   ExtraTileInfoType: "Información Extra sobre las Casillas",
   ExtraTileInfoTypeDesc: "Elija qué información se muestra debajo de cada mosaico",
   ExtraTileInfoTypeEmpireValue: "Valor del Imperio",
   ExtraTileInfoTypeNone: "Nada",
   ExtraTileInfoTypeStoragePercentage: "Porcentaje de Almacenamiento",
   Faith: "Fé",
   Farming: "Cultivo",
   FavoriteBuildingAdd: "Añadir a favoritos",
   FavoriteBuildingEmptyToast: "No tienes construcciones favoritas",
   FavoriteBuildingRemove: "Eliminar de favoritos",
   FeatureRequireQuaestorOrAbove: "Esta característica requiere rango de Cuestor o superior",
   Festival: "Festival",
   FestivalCycle: "Ciclos de Festivales",
   FestivalTechTooltipV2: "Positive Happiness (max. 50) is converted into festival points. For every %{point} festival points, your empire enters a festival cycle, granting a significant map-specific boost. The festival on this map is %{desc}",
   FestivalTechV2: "Unlock festival - positive Happiness (max. 50) is converted into festival points. For every %{point} festival points, your empire enters a festival cycle, granting a significant map-specific boost",
   Feudalism: "Feudalismo",
   Fibonacci: "Fibonacci",
   FibonacciDescV2: "+%{idle} de ciencia por trabajadores en paro. +%{busy} de ciencia por trabajadores ocupados. El coste de la mejora permanente de Fibonacci sigue la sucesión de Fibonacci",
   FighterJet: "Fighter Jet",
   FighterJetPlant: "Fighter Jet Plant",
   FilterByAge: "Filtrar por Era",
   FinancialArbitrage: "Financial Arbitrage",
   FinancialLeverage: "Financial Leverage",
   Fire: "Fuego",
   Firearm: "Armas de fuego",
   FirstTimeGuideNext: "Siguiente",
   FirstTimeTutorialWelcome: "Bienvenido a Civildle",
   FirstTimeTutorialWelcome1HTML:
      "Welcome to CivIdle. In this game, you will run your own empire: <b>manage productions, unlock technologies, trade resources with other players, create great people and build world wonders</b>.<br><br>Drag your mouse to move around. Use the scroll wheel to zoom in or out. Click an empty tile to build new buildings, click a building to inspect it.<br><br>Certain buildings like Stone Quarry and Logging Camp need to be built on top of the resource tile. I recommend placing a Hut, which provides worker, next to the fog - the building will take some time to build. After the completion, it will reveal the fog nearby.",
   FirstTimeTutorialWelcome2HTML:
      "Buildings can be upgraded - it costs resources and takes time. When a buildings is being upgraded, <b>it will no longer produce</b>. This includes buildings that provide workers, <b>so never upgrade all your buildings at the same time!</b><br><br>As your empire grows, you will get more science and unlock new technologies. I will tell you more about it when we get there but you can go to View -> Research to take a quick look<br><br>",
   FirstTimeTutorialWelcome3HTML: "Now you know all the basics of the game, you can start building your empire. But before I let you go, you should <b>choose yourself a player handle</b> and say hi in the in-game chat. We have an amazingly helpful community: if you get lost, don't be afraid to ask!",
   Fish: "Pescado",
   FishPond: "Estanque de peces",
   FlorenceNightingale: "Florence Nightingale",
   FlorenceNightingaleDesc: "+%{value} de felicidad",
   Flour: "Harina",
   FlourMill: "Molino de harina",
   FontSizeScale: "Font Size Scale",
   FontSizeScaleDescHTML: "Change the font size scale of the game's UI. <b>Setting the scale greater than 1x might break some UI layouts</b>",
   ForbiddenCity: "La Ciudad Prohibida",
   ForbiddenCityDesc: "Todas las papeleras, gremios de escritores, e imprentas obtienen +1 multiplicador de producción, capacidad de trabajo y almacenamiento",
   Forex: "Forex",
   ForexMarket: "Forex Market",
   FrankLloydWright: "Frank Lloyd Wright",
   FrankLloydWrightDesc: "+%{value} Builder Capacity Multiplier",
   FrankWhittle: "Frank Whittle",
   FreeThisWeek: "Free This Week",
   FreeThisWeekDescHTMLV2: "<b>Every week</b>, one of the premium civilizations is free to play. This week's free civilization is <b>%{city}</b>",
   French: "French",
   Frigate: "Fragata",
   FrigateBuilder: "Astillero de fragatas",
   Furniture: "Mobiliario",
   FurnitureWorkshop: "Carpintería",
   Future: "Future",
   GabrielGarciaMarquez: "Gabriel García Márquez",
   GabrielGarciaMarquezDesc: "+%{value} Happiness",
   GalileoGalilei: "Galileo Galilei",
   GalileoGalileiDesc: "+%{value} Ciencia de Trabajadores no Ocupados.",
   Galleon: "Galeón",
   GalleonBuilder: "Astillero de galeones",
   Gameplay: "Juego",
   Garment: "Prenda",
   GarmentWorkshop: "Taller de confección",
   GasPipeline: "Gaseoducto",
   GasPowerPlant: "Gas Power Plant",
   GatlingGun: "Ametralladora Gatling",
   GatlingGunFactory: "Fábrica de ametralladoras Gatling",
   Genetics: "Genética",
   Geography: "Geografía",
   GeorgeCMarshall: "George C. Marshall",
   GeorgeWashington: "George Washington",
   GeorgiusAgricola: "Georgius Agricola",
   German: "German",
   Glass: "Cristal",
   Glassworks: "Fábrica de vidrio",
   GlobalBuildingDefault: "Global Builing Default",
   Globalization: "Globalización",
   GoBack: "Volver atrás",
   Gold: "Oro",
   GoldenGateBridge: "Puente Golden Gate",
   GoldenGateBridgeDesc: "Todas las centrales eléctricas obtienen +1 multiplicador de producción. Proporciona energía a todas las fichas dentro del rango de 2 casillas.",
   GoldenPavilion: "Golden Pavilion",
   GoldenPavilionDesc: "All buildings within 3 tile range get +1 Production Multiplier for each adjacent building that produces any of its consumed resources (excluding Clone Lab and Clone Factory and the building cannot be turned off)",
   GoldMiningCamp: "Mina de oro",
   GordonMoore: "Gordon Moore",
   GrandBazaar: "Gran Bazar",
   GrandBazaarDesc: "¡Controla todos los mercados desde un lugar! Todas las construcciones adyacentes obtienen +5 multiplicadores de almacenamiento",
   GrandBazaarFilters: "Filtros",
   GrandBazaarFilterWarningHTML: "Debes seleccionar un filtro para que se muestren los intercambios",
   GrandBazaarFilterYouGet: "Obtienes",
   GrandBazaarFilterYouPay: "Pagas",
   GrandBazaarSeach: "Search",
   GrandBazaarSearchGet: "Obtienes",
   GrandBazaarSearchPay: "Pagar",
   GrandBazaarTabActive: "Activo",
   GrandBazaarTabTrades: "Intercambio",
   GrandCanyon: "Gran Cañón",
   GrandCanyonDesc: "Buildings unlocked in the current age get +2 Production Multiplier. Double the effect of J.P. Morgan",
   GraphicsDriver: "Graphics Driver: %{driver}",
   GreatDagonPagoda: "Shwedagon",
   GreatDagonPagodaDescV2: "All pagodas are exempt from -1 happiness. Generate science based on faith production of all pagodas",
   GreatMosqueOfSamarra: "Gran Mezquita de Samarra",
   GreatMosqueOfSamarraDescV2: "+1 de rango de visión. Revela 5 casillas inexploradas aleatorias con depósitos y construye una construcción de extracción de recursos de nivel 10 en cada una",
   GreatPeople: "Personajes históricos",
   GreatPeopleEffect: "Efecto",
   GreatPeopleFilter: "Type name or age to filter great people",
   GreatPeopleName: "Nombre",
   GreatPeoplePermanentColumn: "Permanente",
   GreatPeoplePermanentShort: "Permanente",
   GreatPeoplePickPerRoll: "Great People Pick Per Roll",
   GreatPeopleThisRun: "Personajes históricos de esta partida",
   GreatPeopleThisRunColumn: "Esta partida",
   GreatPeopleThisRunShort: "Esta partida",
   GreatPersonLevelRequired: "Permanent Great People Level Required",
   GreatPersonLevelRequiredDescV2: "%{city} civilization requires %{required} permanent great people levels. You currently have %{current}",
   GreatPersonPromotionPromote: "Mejorar",
   GreatPersonThisRunEffectiveLevel: "Actualmente tienes %{count} %{person} en esta partida. Un %{person} adicional tendrá un 1/%{effect} de efecto",
   GreatPersonWildCardBirth: "Nacer",
   GreatSphinx: "Great Sphinx",
   GreatSphinxDesc: "All Tier II or above buildings within 2 tiles get +N Consumption, Production Multiplier. N = Number of its adjacent buildings of the same type",
   GreatWall: "Gran Muralla",
   GreatWallDesc: "All buildings within 1 tile range get +N Production, Worker Capacity and Storage Multiplier. N = the number of the different ages between the current age and the age where the building is first unlocked. When constructed next to Forbidden City, the range increases to 2 tile",
   GreedyTransport: "Construction/Upgrade Greedy Transport",
   GreedyTransportDescHTML: "This will make buildings keep transporting resources even if it has enough resources for the current upgrade, which can make upgrading multiple levels <b>faster</b> but end up transport <b>more resources than needed</b>",
   Greek: "Greek",
   GrottaAzzurra: "Gruta Azul",
   GrottaAzzurraDescV2: "When discovered, all your Tier I buildings get +5 Level and +1 Production, Worker Capacity and Storage Multiplier",
   Gunpowder: "Pólvora",
   GunpowderMill: "Molino de pólvora",
   GuyFawkesNightV2: "Guy Fawkes Night: East India Company provides double the Production Multiplier to buildings adjacent to caravansaries. Tower Bridge generates great people 20% faster",
   HagiaSophia: "Santa Sofía",
   HagiaSophiaDescV2: "+5 Happiness. Buildings with 0% Production Capacity are exempt from -1 happiness. During the game bootstrap, provide extra happiness to avoid production halt",
   HallOfFame: "Hall of Fame",
   HallOfSupremeHarmony: "Hall of Supreme Harmony",
   Hammurabi: "Hammurabi",
   HangingGarden: "Jardines colgantes",
   HangingGardenDesc: "+1 multiplicador de capacidad de construcción. Los acueductos adyacentes obtienen +1 multiplicador de producción, almacenamiento y capacidad de trabajo",
   Happiness: "Felicidad",
   HappinessFromBuilding: "Por construcciones (excl. maravillas)",
   HappinessFromBuildingTypes: "Por construcciones abastecidas",
   HappinessFromHighestTierBuilding: "Por construcciones de rango más alto",
   HappinessFromUnlockedAge: "Por eras desbloqueadas",
   HappinessFromUnlockedTech: "Por tecnologías desbloqueadas",
   HappinessFromWonders: "De maravillas (incl. naturales)",
   HappinessUncapped: "Felicidad (calculada)",
   HarryMarkowitz: "Harry Markowitz",
   HarunAlRashid: "Harún al-Rashid",
   Hatshepsut: "Hatshepsut",
   HatshepsutTemple: "Templo de Hatshepsut",
   HatshepsutTempleDesc: "Revela todas las casillas de agua en el mapa. Los cultivos de trigo obtienen +1 multiplicador de producción por cada casilla de agua adyacente al cultivo",
   Headquarter: "Sede",
   HedgeFund: "Hedge Fund",
   HelpMenu: "Ayuda",
   HenryFord: "Henry Ford",
   Herding: "Pastoreo",
   Herodotus: "Heródoto",
   HighlightBuilding: "Highlight %{building}",
   HimejiCastle: "Castillo de Himeji",
   HimejiCastleDesc: "Todos los astilletos de caravelas, astilleros de galeones y astilleros de fragatas obtienen +1 multiplicador de producción, capacidad de trabajo y almacenamiento",
   Hollywood: "Hollywood",
   HollywoodDesc: "+5 Felicidad. +1 Felicidad por cada construcción abastecida que consuma o produzca cultura en un rango de 2 casillas.",
   HolyEmpire: "Santo Imperio",
   Homer: "Homer",
   Honor4UpgradeHTML: "Duplica el efecto de <b>Zheng He</b> (Great Person)",
   HonorLevelX: "Honor %{level}",
   Horse: "Caballo",
   HorsebackRiding: "Montar a caballo",
   House: "Casa",
   Housing: "Vivienda",
   Hut: "Cabaña",
   HydroDam: "Presa Hidráulica",
   Hydroelectricity: "Hidroelectricidad",
   HymanGRickover: "Hyman G. Rickover",
   IdeologyDescHTML: "Choose from <b>Liberalism, Conservatism, Socialism or Communism</b> as your empire ideology. You <b>cannot switch ideology</b> after it is chosen. You can unlock more boost within each ideology",
   IMPei: "I. M. Pei",
   IMPeiDesc: "+%{value} Builder Capacity Multiplier",
   Imperialism: "Imperialismo",
   ImperialPalace: "Imperial Palace",
   IndustrialAge: "Industrialización",
   InformationAge: "Era de la información",
   InputResourceForCloning: "Input Resource For Cloning",
   InternationalSpaceStation: "International Space Station",
   InternationalSpaceStationDesc: "All buildings get +5 Storage Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Storage Multiplier to all buildings",
   Internet: "Internet",
   InternetServiceProvider: "Internet Service Provider",
   InverseSelection: "Invertir",
   Iron: "Hierro",
   IronAge: "Edad del hierro",
   Ironclad: "Acorazado",
   IroncladBuilder: "Astillero de acorazados",
   IronForge: "Forja de hierro",
   IronMiningCamp: "Mina de hierro",
   IronTech: "Hierro",
   IsaacNewton: "Isaac Newton",
   IsaacNewtonDescV2: "+%{value} Science from All Workers if more than 50% of workers are busy and less than 50% of busy workers work in transportation",
   IsambardKingdomBrunel: "Isambard Kingdom Brunel",
   IsidoreOfMiletus: "Isidoro de Mileto",
   IsidoreOfMiletusDesc: "+%{value} multiplicador de capacidad de construcción",
   Islam5UpgradeHTML: "When unlocked, generate one-time science equivalent to the cost of the most expensive <b>Industrial</b> technology",
   IslamLevelX: "Islam %{level}",
   ItsukushimaShrine: "Santuario Itsukushima",
   ItsukushimaShrineDescV2: "When all technologies within an age are unlocked, generate one-time science equivalent to the cost of the cheapest technology in the next age",
   JamesWatson: "James Watson",
   JamesWatsonDesc: "+%{value} Science From Busy Workers",
   JamesWatt: "James Watt",
   Japanese: "Japanese",
   JetPropulsion: "Jet Propulsion",
   JohannesGutenberg: "Johannes Gutenberg",
   JohannesKepler: "Johannes Kepler",
   JohnCarmack: "John Carmack",
   JohnDRockefeller: "John D. Rockefeller",
   JohnMcCarthy: "John McCarthy",
   JohnVonNeumann: "John von Neumann",
   JohnVonNeumannDesc: "+%{value} Science From Busy Workers",
   JoinDiscord: "Únete a Discord",
   JosephPulitzer: "Joseph Pulitzer",
   Journalism: "Periodismo",
   JPMorgan: "J.P. Morgan",
   JRobertOppenheimer: "J. Robert Oppenheimer",
   JuliusCaesar: "Julius Caesar",
   Justinian: "Justiniano",
   Kanagawa: "Kanagawa",
   KanagawaDesc: "All great people of the current age get an additional level for this run (excluding Zenobia)",
   KarlMarx: "Karl Marx",
   Knight: "Caballero",
   KnightCamp: "Campamento de caballeros",
   Koti: "Koti",
   KotiInStorage: "Koti In Storage",
   KotiProduction: "Koti Production",
   LandTrade: "Comercio por tierra",
   Language: "Idioma",
   Lapland: "Lapland",
   LaplandDesc: "When discovered, reveal the whole map. All buildings within 2-tile range get +5 Production Multiplier. This natural wonder can only be discovered in December",
   LargeHadronCollider: "Large Hadron Collider",
   LargeHadronColliderDescV2: "All Information Age great people get +2 level for this run. This wonder can be upgraded and each additional upgrade provides +1 level to all Information Age great people for this run",
   Law: "Ley",
   Lens: "Lentes",
   LensWorkshop: "Taller de lentes",
   LeonardoDaVinci: "Leonardo da Vinci",
   Level: "Nivel",
   LevelX: "Nivel %{level}",
   Liberalism: "Liberalismo",
   LiberalismLevel3DescHTML: "Free transport <b>from</b> and <b>to</b> warehouses",
   LiberalismLevel5DescHTML: "<b>Duplica</b> el efecto de la electrificación",
   LiberalismLevelX: "Nivel de Liberalismo %{level}",
   Library: "Biblioteca",
   LighthouseOfAlexandria: "Faro de Alejandría",
   LighthouseOfAlexandriaDesc: "Todas las construcciones adyacentes obtienen +5 multiplicadores de almacenaje",
   LinusPauling: "Linus Pauling",
   LinusPaulingDesc: "+%{value} Ciencia de los Trabajadores Inactivos",
   Literature: "Literatura",
   LiveData: "Live Value",
   LocomotiveFactory: "Fábrica de locomotoras",
   Logging: "Tala",
   LoggingCamp: "Campamento maderero",
   LouisSullivan: "Louis Sullivan",
   LouisSullivanDesc: "+%{value} Builder Capacity Multiplier",
   Louvre: "Louvre",
   LouvreDesc: "For every 10 Extra Great People at Rebirth, one great person from all unlocked ages is born",
   Lumber: "Tabla",
   LumberMill: "Aserradero",
   LunarNewYear: "Lunar New Year: Great Wall provides double the boost to buildings. Porcelain Tower provides +1 level to all great people from this run",
   LuxorTemple: "Templo de Lúxor",
   LuxorTempleDescV2: "+1 Ciencia de trabajadores ocupados. Elige una religión del Imperio, puedes mejorar dicha religión",
   Machinery: "Maquinaria",
   Magazine: "Revista",
   MagazinePublisher: "Revista",
   Maglev: "Maglev",
   MaglevFactory: "Maglev Factory",
   MahatmaGandhi: "Mahatma Gandhi",
   ManageAgeWisdom: "Gestionar Sabiduría de la Era",
   ManagedImport: "Managed Import",
   ManagedImportDescV2: "This building will automatically import resources produced within %{range} tile range. Resource transports for this building cannot be manually changed. Max transport distance will be ignored",
   ManageGreatPeople: "Gestionar personajes históricos",
   ManagePermanentGreatPeople: "Gestionar personajes históricos permanentes",
   ManageSave: "Gestionar partida guardada",
   ManageWonders: "Gestionar maravillas",
   Manhattan: "Manhattan",
   ManhattanProject: "Proyecto Manhattan",
   ManhattanProjectDesc:
      "Todas las minas de uranio obtienen +2 al multiplicador de producción, capacidad de trabajo y almacenamiento. Las plantas de enriquecimiento de uranio y las instalaciones atómicas obtienen +1 al multiplicador de producción por cada mina de uranio adyacente que se construya sobre un depósito de uranio.",
   Marble: "Mármol",
   Marbleworks: "Marmolería",
   MarcoPolo: "Marco Polo",
   MarieCurie: "Marie Curie",
   MarinaBaySands: "Marina Bay Sands",
   MarinaBaySandsDesc: "All buildings get +5 Worker Capacity Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Worker Capacity Multiplier to all buildings",
   Market: "Mercado",
   MarketDesc: "Intercambia un recurso por otro. Los recursos disponibles se actualizan cada hora.",
   MarketRefreshMessage: "Trades in %{count} markets has been refreshed",
   MarketSell: "Vender",
   MarketSettings: "Ajustes del mercado",
   MarketValueDesc: "%{value} comparado con el precio medio",
   MarketYouGet: "Obtienes",
   MarketYouPay: "Pagas",
   MartinLuther: "Martin Luther",
   MaryamMirzakhani: "Maryam Mirzakhani",
   MaryamMirzakhaniDesc: "+%{value} Science From Idle Workers",
   Masonry: "Albañilería",
   MatrioshkaBrain: "Matrioshka Brain",
   MatrioshkaBrainDescV2: "Allow Science to be counted when calculating empire value (5 Science = 1 Empire Value). +5 Science Per Busy and Idle Worker. This wonder can be upgraded and each additional upgrade provides +1 Science Per Busy and Idle Worker and +1 Production Multiplier for buildings that produce Science",
   MausoleumAtHalicarnassus: "Mausoleo de Halicarnaso",
   MausoleumAtHalicarnassusDescV2: "Transports from or to buildings within 2 tile range do not cost workers",
   MaxExplorers: "Exploradores Máximos",
   MaxTransportDistance: "Distancia máxima de transporte",
   Meat: "Carne",
   Metallurgy: "Metalurgia",
   Michelangelo: "Michelangelo",
   MiddleAge: "Edad Media",
   MilitaryTactics: "Military Tactics",
   Milk: "Leche",
   Moai: "Moai",
   MoaiDesc: "Moai",
   MobileOverride: "Mobile Override",
   MogaoCaves: "Cuevas de Mogao",
   MogaoCavesDescV3: "+1 de Felicidad por cada 10% de trabajadores ocupados. Todos los edificios adyacentes que produzcan Fe estarán exentos -1 de Ferlicidad",
   MonetarySystem: "Sistema Monetario",
   MontSaintMichel: "Mont Saint-Michel",
   MontSaintMichelDesc: "Generate Culture from Idle Workers. Provide +1 Storage Multiplier to all buildings within 2-tile range. This wonder can be upgraded using the generated Culture and each level provides addtional +1 Storage Multiplier",
   Mosque: "Mezquita",
   MotionPicture: "Imagen en movimiento",
   MountArarat: "Mount Ararat",
   MountAraratDesc: "All buildings within 2 tile range get +X Production, Worker Capacity and Storage Multiplier. X = cubic root of Effective Great People Level",
   MountFuji: "Mount Fuji",
   MountFujiDescV2: "When Petra is built next to it, Petra gets +8h Warp storage. When the game is running, generate 20 warp every minute in Petra (not accelerated by Petra itself, not generating when the game is offline)",
   MountSinai: "Mount Sinai",
   MountSinaiDesc: "When discovered, a great person of the current age is born. All buildings that produce faith get +5 Storage Multiplier",
   MountTai: "Mount Tai",
   MountTaiDesc: "All buildings that produce science get +1 Production Multiplier. Double the effect of Confucious (Great Person). When discovered, generate one-time science equivalent to the cost of the most expensive unlocked technology",
   MoveBuilding: "Move Building",
   MoveBuildingFail: "Selected tile is not valid",
   MoveBuildingNoTeleport: "You don't have enough teleport",
   MoveBuildingSelectTile: "Selecciona una casilla...",
   MoveBuildingSelectTileToastHTML: "Selecciona <b>una casilla explorada vacía</b> en el mapa como objetivo",
   Movie: "Película",
   MovieStudio: "Movie Studio",
   Museum: "Museo",
   Music: "Música",
   MusiciansGuild: "Gremio de músicos",
   MutualAssuredDestruction: "Mutual Assured Destruction",
   MutualFund: "Mutual Fund",
   Name: "Nombre",
   Nanotechnology: "Nanotecnología",
   NapoleonBonaparte: "Napoleon Bonaparte",
   NaturalGas: "Gas natural",
   NaturalGasWell: "Pozo de gas natural",
   NaturalWonderName: "Maravilla natural: %{name}",
   NaturalWonders: "Maravillas naturales",
   Navigation: "Navegación",
   NebuchadnezzarII: "Nabucodonosor II",
   Neuschwanstein: "Neuschwanstein",
   NeuschwansteinDesc: "+10 multiplicadores de capacidad de construcción al construir maravillas",
   Newspaper: "Periódico",
   NextExplorersIn: "Next Explorers In",
   NextMarketUpdateIn: "Próxima actualización del mercado en",
   NiagaraFalls: "Cataratas del Niágara",
   NiagaraFallsDescV2: "All warehouses, markets and caravansaries get +N storage multiplier. N = number of unlocked ages. Albert Einstein provides +1 Production Multiplier to Research Fund (not affected by other boosts like Broadway)",
   NielsBohr: "Niels Bohr",
   NielsBohrDescV2: "+%{value} Science from All Workers if more than 50% of workers are busy and less than 50% of busy workers work in transportation",
   NileRiver: "Río Nilo",
   NileRiverDesc: "Double the effect of Hatshepsut. All wheat farms get +1 Production and Storage Multiplier. All adjacent wheat farms get +5 Production and Storage Multiplier",
   NoPowerRequired: "This building does not require power",
   NothingHere: "No hay nada aquí",
   NotProducingBuildings: "Construcciones que no están produciendo",
   NuclearFission: "Fisión nuclear",
   NuclearFuelRod: "Nuclear Fuel Rod",
   NuclearMissile: "Misil Nuclear",
   NuclearMissileSilo: "Nuclear Missile Silo",
   NuclearPowerPlant: "Reactor Power Plant",
   NuclearReactor: "Reactor Nuclear",
   NuclearSubmarine: "Submarino Nuclear",
   NuclearSubmarineYard: "Nuclear Submarine Yard",
   OdaNobunaga: "Oda Nobunaga",
   OfflineErrorMessage: "Estás desconectado. Esta operación requiere conexión a Internet",
   OfflineProduction: "Producción durante la desconexión",
   OfflineProductionTime: "Tiempo de producción desconectado.",
   OfflineProductionTimeDescHTML: "For the <b>first %{time} offline time</b>, you can choose either offline production or time warp - you can set the split here. The <b>rest of the offline time</b> can only be converted to time warp",
   OfflineTime: "Tiempo de desconexión",
   Oil: "Petróleo",
   OilPress: "Prensa de aceite",
   OilRefinery: "Refinería de petróleo",
   OilWell: "Pozo de petróleo",
   Ok: "OK",
   Oktoberfest: "Oktoberfest: Duplica el efecto de Zugspitze",
   Olive: "Oliva",
   OlivePlantation: "Olivar",
   Olympics: "Juegos Olímpicos",
   OnlyAvailableWhenPlaying: "Sólo disponible mientras juegas con %{city}",
   OpenLogFolder: "Open Log Folder",
   OpenSaveBackupFolder: "Abrir copias de seguridad",
   OpenSaveFolder: "Abrir directorio de guardado",
   Opera: "Ópera",
   OperationNotAllowedError: "Esta operación no está permitida",
   Opet: "Opet: Great Sphinx no longer increases Consumption Multiplier",
   OpticalFiber: "Fibra Óptica",
   OpticalFiberPlant: "Fábrica de fibra óptica",
   Optics: "Óptica",
   OptionsMenu: "Opciones",
   OptionsUseModernUIV2: "Use Anti-Aliased Font",
   OsakaCastle: "Castillo de Osaka",
   OsakaCastleDesc: "Provide power to all tiles within 2 tile range. Allow electrification of science producing buildings (including Clone Lab)",
   OtherPlatform: "Otra Plataforma",
   Ottoman: "Ottoman",
   OttoVonBismarck: "Otto von Bismarck",
   OxfordUniversity: "Universidad de Oxford",
   OxfordUniversityDescV3: "+10% science output for buildings that produce science. When completed, generate one-time science equivalent to the cost of the most expensive unlocked technology",
   PabloPicasso: "Pablo Picasso",
   Pagoda: "Pagoda",
   PaintersGuild: "Gremio de pintores",
   Painting: "Pintura",
   PalmJumeirah: "Palm Jumeirah",
   PalmJumeirahDesc: "+10 Builder Capacity. This wonder can be upgraded and each additional upgrade provides +2 Builder Capacity",
   Pamukkale: "Pamukkale",
   PamukkaleDesc: "When discovered, convert each one of the Permanent Great People Shards (except for Promotion and Wildcard) to the same Great Person From This Run",
   Panathenaea: "Panathenaea: Poseidon proporciona +1 Multiplicador de Producción a todas las Construcciones",
   Pantheon: "Panteón",
   PantheonDescV2: "All buildings within 2 tile range get +1 Worker Capaicity and Storage Multiplier. Generate science based on faith production of all shrines",
   Paper: "Papel",
   PaperMaker: "Papelera",
   Parliament: "Parlamento",
   Parthenon: "Partenón",
   ParthenonDescV2: "Two great people of Classical Age are born and you get 4 choices for each. Musician's Guilds and Painter's Guilds get +1 Production, Worker Capacity and Storage Multiplier and are exempt from -1 Happiness",
   Passcode: "Passcode",
   PasscodeToastHTML: "<b>%{code}</b> is your passcode and it's valid for 30 minutes",
   PatchNotes: "Notas del parche",
   Peace: "Paz",
   Peacekeeper: "Peacekeeper",
   Penthouse: "Penthouse",
   PercentageOfProductionWorkers: "Percentage of Production Workers",
   Performance: "Performance",
   PermanentGreatPeople: "Personajes históricos permanentes",
   PermanentGreatPeopleAcquired: "Personajes históricos Permanentes adquiridos",
   PermanentGreatPeopleUpgradeUndo: "Undo permanent great people upgrade: this will convert upgraded level back to shards - you will get %{amount} shards",
   Persepolis: "Persépolis",
   PersepolisDesc: "Todas las minas de cobre, campamentos madederos y canteras de piedra obtienen +1 multiplicador de producción, capacidad de trabajo y almacenamiento",
   PeterHiggs: "Peter Higgs",
   PeterHiggsDesc: "+%{value} Ciencia de Trabajadores Ocupados",
   Petra: "Petra",
   PetraDesc: "Genera saltos temporales cuando estás desconectado, que se pueden usar para acelerar tu imperio",
   PetraOfflineTimeReconciliation: "You have been credited %{count} warp after server offline time reconciliation",
   Petrol: "Gasolina",
   PhiloFarnsworth: "Philo Farnsworth",
   Philosophy: "Filosofía",
   Physics: "Física",
   PierreDeCoubertin: "Pierre de Coubertin",
   Pizza: "Pizza",
   Pizzeria: "Pizzería",
   PlanetaryRover: "Planetary Rover",
   Plastics: "Plásticos",
   PlasticsFactory: "Fábrica de Plásticos",
   PlatformAndroid: "Android",
   PlatformiOS: "iOS",
   PlatformSteam: "Steam",
   PlatformSyncInstructionHTML: "If you want to sync your progress on this device to a new device, click <b>Sync To A New Device</b> and get a one-time passcode. On your new device, click <b>Connect To A Device</b> and type in the one-time passcode",
   Plato: "Platón",
   PlayerHandle: "Información del jugador",
   PlayerHandleOffline: "Actualmente estás desconectado",
   PlayerMapClaimThisTile: "Reclamar casilla seleccionada",
   PlayerMapClaimTileCondition2: "No has sido baneado por el anti-cheat",
   PlayerMapClaimTileCondition3: "Has desbloqueado la tecnología necesaria: %{tech}",
   PlayerMapClaimTileCondition4: "No has reclamado una casilla, o ha pasado el tiempo de espera para moverte de casilla",
   PlayerMapClaimTileCooldownLeft: "Tiempo de espera restante: %{time}",
   PlayerMapClaimTileNoLongerReserved: "Esta casilla ya no está reservada. Puedes desalojar a <b>%{name}</b> y reclamar esta casilla para ti",
   PlayerMapEstablishedSince: "Est. desde",
   PlayerMapLastSeenAt: "Última vez",
   PlayerMapMapTileBonus: "Bonificación de casilla de comercio",
   PlayerMapMenu: "Comercio",
   PlayerMapOccupyThisTile: "Ocupar esta casilla",
   PlayerMapOccupyTileCondition1: "This tile is adjacent to your home or occupied tiles",
   PlayerMapPageGoBackToCity: "Volver a la ciudad",
   PlayerMapSetYourTariff: "Establece tu arancel",
   PlayerMapTariff: "Arancel",
   PlayerMapTariffApply: "Aplicar tarifa",
   PlayerMapTariffDesc: "Cada comercio que pase a través de tu casilla te pagará un arancel. Si incrementas la tarifa, ganarás más, pero pasarán menos comercios por tu casilla.",
   PlayerMapTileAvailableTilePoint: "Available Tile Point",
   PlayerMapTileFromOccupying: "From Owned/Occupied Tiles",
   PlayerMapTileFromOccupyingTooltipHTML: "An owned/occupied tile generates <b>%{point}</b> tile point per hour (up to %{max} days from the first claimed tile)",
   PlayerMapTileFromRank: "From Account Rank",
   PlayerMapTileTilePoint: "Tile Point",
   PlayerMapTileUsedTilePoint: "Used Tile Point",
   PlayerMapTileUsedTilePointTooltipHTML: "You need <b>1 tile point</b> to own/occupy a tile",
   PlayerMapTradesFrom: "Comercios de %{name}",
   PlayerMapUnclaimedTile: "Casilla no reclamada",
   PlayerMapYourTile: "Tu casilla",
   PlayerTrade: "Comercio con jugadores",
   PlayerTradeAddSuccess: "El comercio ha sido añadido con éxito",
   PlayerTradeAddTradeCancel: "Cancelar",
   PlayerTradeAmount: "Cantidad",
   PlayerTradeCancelDescHTML: "You will get <b>%{res}</b> back after cancelling this trade: <b>%{percent}</b> charged for refund and <b>%{discard}</b> discarded due to storage overflow<br><b>Are you sure you want to cancel?</b>",
   PlayerTradeCancelTrade: "Cancelar comercio",
   PlayerTradeClaim: "Reclamar",
   PlayerTradeClaimAll: "Reclamar todo",
   PlayerTradeClaimAllFailedMessageV2: "Failed to claim any trades - is the storage full?",
   PlayerTradeClaimAllMessageV2: "Has reclamado: <b>%{resources}</b>",
   PlayerTradeClaimAvailable: "%{count} comercio(s) están disponibles para reclamar",
   PlayerTradeClaimTileFirst: "Reclama tu casilla en el mapa de comercio",
   PlayerTradeClaimTileFirstWarning: "Sólo puedes comerciar con otros jugadores una vez hayas reclamado tu casilla en el mapa de comercio",
   PlayerTradeClearAll: "Clear All Fills",
   PlayerTradeClearFilter: "Clear Filters",
   PlayerTradeDisabledBeta: "You can ony create player trades once the beta version is released",
   PlayerTradeFill: "Enviar",
   PlayerTradeFill50: "Fill 50%",
   PlayerTradeFill95: "Fill 95%",
   PlayerTradeFillAmount: "Enviar cantidad",
   PlayerTradeFillAmountMaxV2: "Llenar al máximo",
   PlayerTradeFillBy: "De",
   PlayerTradeFillPercentage: "Porcentaje de llenado",
   PlayerTradeFillSuccessV2: "<b>%{success}/%{total}</b> comercios han sido enviados. Pagas <b>%{fillAmount} de %{fillResource}</b> y recibes <b>%{receivedAmount} de %{receivedResource}</b>",
   PlayerTradeFillTradeButton: "Enviar comercio",
   PlayerTradeFillTradeTitle: "Enviar comercio",
   PlayerTradeFilters: "Filtros",
   PlayerTradeFiltersApply: "Aplicar",
   PlayerTradeFiltersClear: "Clear",
   PlayerTradeFilterWhatIHave: "Filtrar por lo que tengo",
   PlayerTradeFrom: "De",
   PlayerTradeIOffer: "Ofrezco",
   PlayerTradeIWant: "Quiero",
   PlayerTradeMaxAll: "Max All Fills",
   PlayerTradeMaxTradeAmountFilter: "Cantidad máxima",
   PlayerTradeMaxTradeExceeded: "Has excedido el máximo número de comercios activos para el rango de tu cuenta",
   PlayerTradeNewTrade: "Nuevo comercio",
   PlayerTradeNoFillBecauseOfResources: "No trade has been filled due to insufficient resources",
   PlayerTradeNoValidRoute: "No se puede encontrar una ruta comercial válida entre tu ciudad y la de %{name}",
   PlayerTradeOffer: "Ofrece",
   PlayerTradePlaceTrade: "Crear comercio",
   PlayerTradePlayerNameFilter: "Nombre de Jugador",
   PlayerTradeResource: "Recurso",
   PlayerTradeStorageRequired: "Almacenamiento necesario",
   PlayerTradeTabImport: "Importar",
   PlayerTradeTabPendingTrades: "Intercambios pendientes",
   PlayerTradeTabTrades: "Intercambios",
   PlayerTradeTariffTooltip: "Recolectado de un arancel",
   PlayerTradeWant: "Quiere",
   PlayerTradeYouGetGross: "Obtienes (antes de aranceles): %{res}",
   PlayerTradeYouGetNet: "Obtienes (después de aranceles): %{res}",
   PlayerTradeYouPay: "Pagas: %{res}",
   Poem: "Poema",
   PoetrySchool: "Escuela de Poetas",
   Politics: "Política",
   PolytheismLevelX: "Polytheism %{level}",
   PorcelainTower: "Torre de Porcelana",
   PorcelainTowerDesc: "+5 Happiness. When constructed, all your extra great people at rebirth will become available for this run (they are rolled following the same rule as permanent great people)",
   PorcelainTowerMaxPickPerRoll: "Prefer Max Pick Per Roll",
   PorcelainTowerMaxPickPerRollDescHTML: "When choosing great people after Porcelain Tower completed, prefer max pick per roll for the available amount",
   Poseidon: "Poseidón",
   PoseidonDescV2: "All adjacent buildings get free upgrades to Level 25 and +N Production, Worker Capacity and Storage Multiplier. N = Tier of the building",
   PoultryFarm: "Granja Avícola",
   Power: "Energía",
   PowerAvailable: "Energía disponible",
   PowerUsed: "Energía usada",
   PreciousMetal: "Metal precioso",
   Printing: "Imprenta",
   PrintingHouse: "Imprenta",
   PrintingPress: "Imprenta",
   PrivateOwnership: "Propiedad privada",
   Produce: "Producir",
   ProduceResource: "Produce: %{resource}",
   ProductionMultiplier: "Multiplicador de producción",
   ProductionPriority: "Prioridad de producción",
   ProductionPriorityDescV4: "Priority determins the order that buildings transport and produce - a bigger number means a building transports and produces before other buildings",
   ProductionWorkers: "Production Workers",
   Progress: "Progreso",
   ProgressTowardsNextGreatPerson: "Progreso para conseguir el siguiente personaje histórico al renacer",
   ProgressTowardsTheNextGreatPerson: "Progreso hasta el próximo Personaje histórico",
   PromotionGreatPersonDescV2: "Cuando es consumido, asciende a cualquier personaje histórico permanente de su misma era a otro personaje de la siguiente era.",
   ProphetsMosque: "Mezquita del Profeta",
   ProphetsMosqueDesc: "Duplica el efecto de Harun al-Rashid. Genera ciencia en base a la cantidad de Fé generada en las Mezquitas.",
   Province: "Provincia",
   ProvinceAegyptus: "Egipto",
   ProvinceAfrica: "África",
   ProvinceAsia: "Asia",
   ProvinceBithynia: "Bithynia",
   ProvinceCantabri: "Cantabri",
   ProvinceCappadocia: "Cappadocia",
   ProvinceCilicia: "Cilicia",
   ProvinceCommagene: "Commagene",
   ProvinceCreta: "Creta",
   ProvinceCyprus: "Chipre",
   ProvinceCyrene: "Cyrene",
   ProvinceGalatia: "Galatia",
   ProvinceGallia: "Galia",
   ProvinceGalliaCisalpina: "Gallia Cisalpina",
   ProvinceGalliaTransalpina: "Gallia Transalpina",
   ProvinceHispania: "Hispania",
   ProvinceIllyricum: "Illyricum",
   ProvinceItalia: "Italia",
   ProvinceJudia: "Judia",
   ProvinceLycia: "Lycia",
   ProvinceMacedonia: "Macedonia",
   ProvinceMauretania: "Mauretania",
   ProvinceNumidia: "Numidia",
   ProvincePontus: "Pontus",
   ProvinceSardiniaAndCorsica: "Cerdeña y Córcega",
   ProvinceSicillia: "Sicilia",
   ProvinceSophene: "Sophene",
   ProvinceSyria: "Siria",
   PublishingHouse: "Editorial",
   PyramidOfGiza: "Pirámide de Giza",
   PyramidOfGizaDesc: "Todas las construcciones que producen trabajadores obtienen +1 multiplicador de producción",
   QinShiHuang: "Qin Shi Huang",
   Radio: "Radio",
   RadioStation: "Radio Station",
   Railway: "Ferrocarril",
   RamessesII: "Ramsés II",
   RamessesIIDesc: "+%{value} multiplicador de capacidad de construcción",
   RandomColorScheme: "Random Color Scheme",
   RapidFire: "Fuego rápido",
   ReadFullPatchNotes: "Leer notas de parche",
   RebirthHistory: "Historial de Renacimientos",
   RebirthTime: "Duración del Renacimiento",
   Reborn: "Renacer",
   RebornModalDescV3: "You will start a new empire but all your great people <b>from this run</b> becomes permanent shards, which can be used to upgrade your <b>permanent great people level</b>. You will also get extra great people shards based on your <b>total empire value</b>",
   RebornOfflineWarning: "Actualmente estás sin conexión. Sólo puedes renacer cuando tienes conexión con el servidor",
   RebornTradeWarning: "Tienes comercios activos o que pueden ser reclamados. <b>Renacer los eliminará</b> - deberías considerar cancelarlos o reclamarlos antes",
   RedistributeAmongSelected: "Redistribuir entre los seleccionados",
   RedistributeAmongSelectedCap: "Límite",
   RedistributeAmongSelectedImport: "Importar",
   Refinery: "Refinería",
   Reichstag: "Reichstag",
   Religion: "Religión",
   ReligionBuddhism: "Budismo",
   ReligionChristianity: "Cristianismo",
   ReligionDescHTML: "Elige entre <b>Cristianismo, Islam, Budismo or Politeísmo</b> como religiones de tu Imperio. <b>No podrás cambiar de religión</b> tras haber escogido una. Puedes mejorar dicha religión",
   ReligionIslam: "Islam",
   ReligionPolytheism: "Politeísmo",
   Renaissance: "Renacimiento",
   RenaissanceAge: "Renacimiento",
   ReneDescartes: "René Descartes",
   RequiredDeposit: "Recurso necesario",
   RequiredWorkersTooltipV2: "Required number of workers for production is equal to the sum of all resources consumed and produced after multipliers (excluding dynamic multipliers)",
   RequirePower: "Require Power",
   RequirePowerDesc: "This building needs to be built on a tile with power and can extend the power to its adjacent tiles",
   Research: "Investigación",
   ResearchFund: "Research Fund",
   ResearchLab: "Laboratorio de investigación",
   ResearchMenu: "Investigación",
   ResourceAmount: "Cantidad",
   ResourceBar: "Resource Bar",
   ResourceBarExcludeStorageFullHTML: "Exclude buildings that have <b>full storage</b> from Not Producing Buildings",
   ResourceBarExcludeTurnedOffOrNoActiveTransportHTML: "Exclude buildings that are <b>turned off</b> from Not Producing Buildings",
   ResourceBarShowUncappedHappiness: "Show Uncapped Happiness",
   ResourceCloneTooltip: "The production multiplier only applies to the cloned resource (i.e. the extra copy)",
   ResourceColor: "Color del recurso",
   ResourceExportBelowCap: "Exportar por debajo del límite",
   ResourceExportBelowCapTooltip: "Permite a otras construcciones transportar un recurso desde esta construcción incluso cuando su cantidad está por debajo del límite",
   ResourceExportToSameType: "Exportar del mismo tipo",
   ResourceExportToSameTypeTooltip: "Permite que otras construcciones del mismo tipo transporten un recurso a esta construcción",
   ResourceFromBuilding: "%{resource} de %{building}",
   ResourceImport: "Transporte de recursos",
   ResourceImportCapacity: "Capacidad de transporte de recursos",
   ResourceImportImportCapV2: "Cantidad máxima",
   ResourceImportImportCapV2Tooltip: "Esta construcción dejará de transportar este recurso cuando se alcance la cantidad máxima",
   ResourceImportImportPerCycleV2: "Por ciclo",
   ResourceImportImportPerCycleV2ToolTip: "La cantidad de este recurso que es transportada por ciclo",
   ResourceImportPartialWarningHTML: "The total resource transport capacity has exceeds the maximum capacity: <b>each resource transport will only transport partially per cycle</b>",
   ResourceImportResource: "Recurso",
   ResourceImportSettings: "Transportando recurso: %{res}",
   ResourceImportStorage: "Total",
   ResourceNeeded: "Extra %{resource} x%{amount} Needed",
   ResourceTransportPreference: "Preferencia de transporte",
   RevealDeposit: "Revelar",
   Revolution: "Revolución",
   RhineGorge: "Rhine Gorge",
   RhineGorgeDesc: "+2 de Felicidad por cada Maravilla en un rango de 2 casillas.",
   RichardFeynman: "Richard Feynman",
   RichardFeynmanDesc: "+%{value} Science from All Workers if more than 50% of workers are busy and less than 50% of busy workers work in transportation",
   RichardJordanGatling: "Richard Jordan Gatling",
   Rifle: "Rifle",
   RifleFactory: "Fábrica de rifles",
   Rifling: "Cañón estriado",
   Rijksmuseum: "Rijksmuseum",
   RijksmuseumDesc: "+5 de felicidad. Todas las construcciones que consumen o producen cultura obtienen +1 de producción, almacenamiento y capacidad de trabajo",
   RoadAndWheel: "Caminos y ruedas",
   RobertNoyce: "Robert Noyce",
   Robocar: "Robocar",
   RobocarFactory: "Robocar Factory",
   Robotics: "Robótica",
   RockefellerCenterChristmasTree: "Rockefeller Center Christmas Tree",
   RockefellerCenterChristmasTreeDesc: "+3 Happiness for each unlocked age. This natural wonder can only be discovered in December",
   Rocket: "Rocket",
   RocketFactory: "Rocket Factory",
   Rocketry: "Cohetería",
   Roman: "Roma",
   RomanForum: "Foro Romano",
   RudolfDiesel: "Rudolf Diesel",
   Rurik: "Riúrik",
   RurikDesc: "+%{value} de felicidad",
   SagradaFamilia: "Sagrada Família",
   SagradaFamiliaDesc: "Todos los edificios dentro del rango de 2 casillas obtienen +N multiplicadores de producción, capacidad de trabajadores y almacenamiento. N = diferencia máxima de niveles entre edificios adyacentes (1 rango de mosaicos) a la Sagrada Familia",
   SaintBasilsCathedral: "Catedral de San Basilio",
   SaintBasilsCathedralDescV2: "Permite que las construcciones de extracción de recursos funcionen en casillas adyacentes a un depósito. Todas las construcciones adyacentes de rango I obtienen +1 multiplicador de producción, capacidad de trabajo, y almacenamiento",
   Saladin: "Saladino",
   Samsuiluna: "Samsu-iluna",
   Sand: "Arena",
   Sandpit: "Arenal",
   SantaClausVillage: "Santa Claus Village",
   SantaClausVillageDesc: "When completed, a great person of the current age is born. This wonder can be upgraded and each additional upgrade provides an extra great person. When choosing great people from this wonder, 4 choices are provided. This wonder can only be constructed in December",
   SargonOfAkkad: "Sargón de Acadia",
   Satellite: "Satélite",
   SatelliteFactory: "Fábrica de Satélites",
   SatoshiNakamoto: "Satoshi Nakamoto",
   Saturnalia: "Saturnalia: Los Alpes no incrementarán el multiplicador de consumo",
   SaveAndExit: "Guardar y salir",
   School: "Escuela",
   Science: "Ciencia",
   ScienceFromBusyWorkers: "Ciencia de trabajadores ocupados",
   ScienceFromIdleWorkers: "Ciencia de trabajadores en paro",
   SciencePerBusyWorker: "Por trabajador ocupado",
   SciencePerIdleWorker: "Por trabajador en paro",
   ScrollSensitivity: "Scroll Sensitivity",
   ScrollSensitivityDescHTML: "Adjust sensitivity when scrolling mousewheel. <b>Must be between 0.01 to 100. Default is 1</b>",
   ScrollWheelAdjustLevelTooltip: "Puedes usar la rueda del ratón para ajustar el nivel cuando el cursor está encima",
   SeaTradeCost: "Coste del comercio marítimo",
   SeaTradeUpgrade: "Comercia con jugadores a través del mar. Se aplicará una tarifa por cada casilla de mar de: %{tariff}",
   SelectCivilization: "Seleccionar Civilización",
   SelectedAll: "Seleccionar todo",
   SelectedCount: "%{count} seleccionados",
   Semiconductor: "Semiconductor",
   SemiconductorFab: "Semiconductor Fab",
   SendExplorer: "Enviar explorador",
   SergeiKorolev: "Sergei Korolev",
   SetAsDefault: "Guardar como predeterminado",
   SetAsDefaultBuilding: "Guardar como predeterminao para todos %{building}",
   Shamanism: "Chamanismo",
   Shelter: "Refugio",
   Shortcut: "Atajos",
   ShortcutBuildingPageSellBuildingV2: "Demolish Building",
   ShortcutBuildingPageToggleBuilding: "Alternar producción",
   ShortcutBuildingPageToggleBuildingSetAllSimilar: "Alternar producción y aplicar a todos",
   ShortcutBuildingPageUpgrade1: "Upgrade Button 1 (+1)",
   ShortcutBuildingPageUpgrade2: "Upgrade Button 2 (+5)",
   ShortcutBuildingPageUpgrade3: "Upgrade Button 3 (+10)",
   ShortcutBuildingPageUpgrade4: "Upgrade Button 4 (+15)",
   ShortcutBuildingPageUpgrade5: "Upgrade Button 5 (+20)",
   ShortcutClear: "Borrar",
   ShortcutConflict: "El atajo está en conflicto con %{name}",
   ShortcutNone: "Ninguno",
   ShortcutPressShortcut: "Pulsa las teclas del atajo...",
   ShortcutSave: "Guardar",
   ShortcutScopeBuildingPage: "Construcción",
   ShortcutScopeConstructionPage: "Consctruction/Upgrade Page",
   ShortcutScopeEmptyTilePage: "Casilla vacía",
   ShortcutScopePlayerMapPage: "Mapa de comercio",
   ShortcutScopeTechPage: "Sección de investigaciones",
   ShortcutScopeUnexploredPage: "Unexplored Page",
   ShortcutTechPageGoBackToCity: "Volver a la ciudad",
   ShortcutTechPageUnlockTech: "Desbloquear tecnología seleccionada",
   ShortcutUpgradePageCancelAllUpgrades: "Cancelar Todas las mejoras",
   ShortcutUpgradePageCancelUpgrade: "Cancelar Mejora",
   ShortcutUpgradePageDecreaseLevel: "Mejorar un nivel menos",
   ShortcutUpgradePageEndConstruction: "Terminar Construcción",
   ShortcutUpgradePageIncreaseLevel: "Mejorar un nivel más",
   ShowTransportArrow: "Mostrar flechas de transporte",
   ShowTransportArrowDescHTML: "Turning this off will hide transport arrows. It might <i>slightly</i> improve performance on low end devices. Performance improvement takes effect <b>after restarting your game</b>",
   ShowUnbuiltOnly: "Solo mostrar construcciones que aún no han sido construidas",
   Shrine: "Santuario",
   SidePanelWidth: "Side Panel Width",
   SidePanelWidthDescHTML: "Change the width of the side panel. <b>Require restarting your game to take effect</b>",
   SiegeRam: "Ariete",
   SiegeWorkshop: "Taller de asedio",
   Silicon: "Silicon",
   SiliconSmelter: "Silicon Smelter",
   Skyscraper: "Rascacielos",
   Socialism: "Socialismo",
   SocialismLevel4DescHTMLV2: "Genera una sola vez, la cantidad de ciencia equivalente al coste de la tecnología más barata de <b>World Wars Age</b>",
   SocialismLevel5DescHTMLV2: "Genera una sola vez, la cantidad de ciencia equivalente al coste de la tecnología más barata de <b>Cold War Age</b>",
   SocialismLevelX: "Nivel de Socialismo %{level}",
   SocialNetwork: "Red social",
   Socrates: "Sócrates",
   SocratesDesc: "+%{value} de ciencia por los trabajadores ocupados",
   Software: "Software",
   SoftwareCompany: "Compañía de Software",
   Sound: "Sonido",
   SoundEffect: "Efectos de sonido",
   SourceGreatPerson: "Personaje histórico: %{person}",
   SourceGreatPersonPermanent: "Personaje histórico permanente: %{person}",
   SourceIdeology: "Ideología: %{ideology}",
   SourceReligion: "Religión: %{religion}",
   SourceResearch: "Investigación: %{tech}",
   SourceTradition: "Tradición: %{tradition}",
   SpaceCenter: "Space Center",
   Spacecraft: "Spacecraft",
   SpacecraftFactory: "Spacecraft Factory",
   SpaceNeedle: "Space Needle",
   SpaceNeedleDesc: "+1 Happiness for each wonder constructed",
   SpaceProgram: "Programa espacial",
   Sports: "Deportes",
   Stable: "Establo",
   Stadium: "Estadio",
   StartFestival: "¡Que empiece el Festival!",
   Stateship: "Estado",
   StatisticsBuildings: "Construcciones",
   StatisticsBuildingsSearchText: "Type a building name to search",
   StatisticsEmpire: "Imperio",
   StatisticsExploration: "Exploración",
   StatisticsOffice: "Oficina de estadísticas",
   StatisticsOfficeDesc: "Otorga estadísticas sobre tu Imperio. Además genera exploradores, con los que puedes explorar el mapa.",
   StatisticsResources: "Recursos",
   StatisticsResourcesDeficit: "Déficit",
   StatisticsResourcesDeficitDesc: "Producción: %{output} - Consumo: %{input}",
   StatisticsResourcesRunOut: "Se agotará en",
   StatisticsResourcesSearchText: "Type a resource name to search",
   StatisticsScience: "Ciencia",
   StatisticsScienceFromBuildings: "Ciencia de construcciones",
   StatisticsScienceFromWorkers: "Ciencia de trabajadores",
   StatisticsScienceProduction: "Producción de ciencia",
   StatisticsStalledTransportation: "Stalled Transportation",
   StatisticsTotalTransportation: "Total Transportation",
   StatisticsTransportation: "Transporte",
   StatisticsTransportationPercentage: "Porcentaje de trabajadores de transporte",
   StatueOfLiberty: "Estatua de la Libertad",
   StatueOfLibertyDesc: "Todas las construcciones adyacentes obtienen +N multiplicadores de producción, almacenamiento y capacidad de trabajo. N = Número de construcciones adyacentes del mismo tipo",
   StatueOfZeus: "Estatua de Zeus",
   StatueOfZeusDesc: "Genera depósitos aleatorios de entre los que han sido revelados en casillas adyacentes vacías. Todas las construcciones de rango I obtienen +5 multiplicadores de producción y almacenamiento",
   SteamAchievement: "Logros de Steam",
   SteamAchievementDetails: "Ver logros de Steam",
   SteamEngine: "Máquina de vapor",
   Steamworks: "Fábrica de motores",
   Steel: "Acero",
   SteelMill: "Planta siderúrgica",
   StephenHawking: "Stephen Hawking",
   Stock: "Acciones",
   StockExchange: "Bolsa",
   StockMarket: "Mercado de valores",
   StockpileDesc: "Esta construcción transportará %{capacity}x recursos por ciclo de producción hasta que se alcance el máximo",
   StockpileMax: "Reserva máxima",
   StockpileMaxDesc: "Esta construcción dejará de transportar un recurso una vez haya suficiente para %{cycle} ciclos de producción",
   StockpileMaxUnlimited: "Ilimitado",
   StockpileMaxUnlimitedDesc: "Esta construcción nunca dejará de transportar recursos, hasta que el almacén esté lleno",
   StockpileSettings: "Capacidad de entrada de reserva",
   Stone: "Piedra",
   StoneAge: "Edad de piedra",
   Stonehenge: "Stonehenge",
   StonehengeDesc: "Todas las construcciones que consumen o producen piedra obtienen +1 multiplicador de producción",
   StoneQuarry: "Cantera de piedra",
   StoneTool: "Herramienta de piedra",
   StoneTools: "Herramientas de piedra",
   Storage: "Total",
   StorageBaseCapacity: "Capacidad base",
   StorageMultiplier: "Multiplicador de almacenamiento",
   StorageUsed: "Almacenamiento usado",
   StPetersBasilica: "Basílica de San Pedro",
   StPetersBasilicaDescV2: "All churches get +5 Storage Multiplier. Generate science based on faith production of all churches",
   Submarine: "Submarine",
   SubmarineYard: "Submarine Yard",
   SuleimanI: "Suleiman I",
   SummerPalace: "Palacio de Verano",
   SummerPalaceDesc: "Todas las construcciones adyacentes que consumen o producen pólvora están extentas del -1 de felicidad. Todas las construcciones que consumen o producen pólvora obtienen +1 de producción, almacenamiento y capacidad de trabajo",
   Supercomputer: "Supercomputer",
   SupercomputerLab: "Supercomputer Lab",
   SupporterPackRequired: "Se requiere del Paquete de Apoyo",
   SupporterThankYou: "CivIdle se mantiene a flote gracias a la generosidad de los siguientes propietarios del paquete de apoyo",
   SwissBank: "Swiss Bank",
   SwissBankDescV2: "Convert a chosen resource from adjacent warehouses to Koti (10 million in Sanskrit) - a tradeable resource that is worth 10M value. Each level of Swiss Bank add 1 Koti conversion per cycle, which is affected by Production Multiplier. Swiss Bank can store unlimited amount of Koti",
   Sword: "Espada",
   SwordForge: "Forja de espadas",
   SydneyOperaHouse: "Ópera de Sídney",
   SydneyOperaHouseDescV2: "Ópera de Sídney",
   SyncToANewDevice: "Sincronizar con un nuevo dispositivo",
   Synthetics: "Sintéticos",
   TajMahal: "Taj Mahal",
   TajMahalDescV2: "A great person of Classical Age and a great person of Middle Age are born. +5 Builder Capacity Multiplier when upgrading buildings over Level 20",
   TangOfShang: "Shāng Tāng",
   TangOfShangDesc: "+%{value} de ciencia por trabajadores en paro",
   Tank: "Tanque",
   TankFactory: "Fábrica de tanques",
   TechAge: "Era",
   TechGlobalMultiplier: "Aumento",
   TechHasBeenUnlocked: "%{tech} ha sido desbloqueada",
   TechProductionPriority: "Desbloquea la prioridad de construcción - permite configurar la prioridad de producción para cada construcción",
   TechResourceTransportPreference: "Desbloquear preferencia de transporte - permite configurar cómo una construcción transporta los recursos necesarios para su producción",
   TechResourceTransportPreferenceAmount: "Cantidad",
   TechResourceTransportPreferenceAmountTooltip: "Esta construcción preferirá transportar recursos desde las construcciones que tengan una mayor cantidad almacenada",
   TechResourceTransportPreferenceDefault: "Predeterminado",
   TechResourceTransportPreferenceDefaultTooltip: "No sobreescribir la preferencia de transporte para este recurso, se usará la preferencia de transporte de la construcción en su lugar",
   TechResourceTransportPreferenceDistance: "Distancia",
   TechResourceTransportPreferenceDistanceTooltip: "Esta construcción preferirá transportar recursos desde las construcciones más cercanas",
   TechResourceTransportPreferenceOverrideTooltip: "Este recurso tiene sobreescrita la preferencia: %{mode}",
   TechResourceTransportPreferenceStorage: "Almacenamiento",
   TechResourceTransportPreferenceStorageTooltip: "Esta construcción preferirá transportar recursos de construcciones que tengan un mayor porcentaje de almacenamiento utilizado",
   TechStockpileMode: "Desbloquea el modo de reserva - permite ajustar la reserva para cada construcción",
   Teleport: "Teletransporte",
   TeleportDescHTML: "A teleport is generated <b>every %{time} seconds</b>. A teleport can be used to <b>move a building (wonders excluded)</b> once",
   Television: "Televisión",
   TempleOfArtemis: "Templo de Artemis",
   TempleOfArtemisDesc: "Todas las forjas de espadas y talleres de armaduras obtienen +5 niveles cuando se completan. Todas las forjas de espadas y talleres de armaduras obtienen +1 multiplicador de producción, capacidad de trabajo y almacenamiento",
   TempleOfHeaven: "Templo del Cielo",
   TempleOfHeavenDesc: "Todas las construcciones de nivel 10 o mayor obtienen +1 multiplicador de capacidad de trabajo",
   TempleOfPtah: "Templo de Ptah",
   TerracottaArmy: "Guerreros de terracota",
   TerracottaArmyDesc: "Todas las minas de hierro obtienen +1 multiplicador de producción, capacidad de trabajo y almacenamiento. Las forjas de hierro obtienen +1 multiplicador de producción por cada mina de hierro adyacente",
   Thanksgiving: "Thanksgiving: Wall Street provides double the boost to buildings and applies to Mutual Fund, Hedge Fund and Bitcoin Miner. Research Funds get +5 Production Multiplier",
   Theater: "Teatro",
   Theme: "Tema",
   ThemeColor: "Color del tema",
   ThemeColorResearchBackground: "Fondo de la sección de investigación",
   ThemeColorReset: "Reiniciar al valor predeterminado",
   ThemeColorResetBuildingColors: "Reiniciar los colores de las construcciones",
   ThemeColorResetResourceColors: "Reiniciar los colores de los recursos",
   ThemeInactiveBuildingAlpha: "Transparencia de construcción inactiva",
   ThemePremiumTile: "This tile is only available for Supporter Pack owners",
   ThemeResearchHighlightColor: "Color de investigación seleccionada",
   ThemeResearchLockedColor: "Color de investigación bloqueada",
   ThemeResearchUnlockedColor: "Color de investigación desbloqueada",
   ThemeTransportIndicatorAlpha: "Transparencia del indicador de transporte",
   Theocracy: "Teocracia",
   TheoreticalData: "Theoretical Data",
   ThePentagon: "El Pentágono",
   ThePentagonDesc: "After constructed, generate teleports that can be used to move buildings. All buildings within 2 tile range get +1 Production, Worker Capacity and Storage Multiplier",
   TheWhiteHouse: "La Casa Blanca",
   ThomasEdison: "Thomas Edison",
   ThomasGresham: "Thomas Gresham",
   Tile: "Casilla",
   TileBonusRefreshIn: "Tile bonus will refresh in <b>%{time}</b>",
   TimBernersLee: "Tim Berners-Lee",
   TimeWarp: "Avance del tiempo",
   TimeWarpWarning: "Acelerar a velocidades más altas de las que tu ordenador pueda soportar puede resultar en pérdida de información: ÚSALO BAJO TU PROPIO RIESGO",
   ToggleWonderEffect: "Activar efecto de la Maravilla",
   Tool: "Herramientas",
   TopkapiPalace: "Topkapı Palace",
   TopkapiPalaceDesc: "All buildings within 2 tile range get +X Storage Multiplier. X = 50% of its Production Multiplier (excluding Dynamic)",
   TotalEmpireValue: "Valor total del imperio",
   TotalEmpireValuePerCycle: "Valor total del imperio por ciclo",
   TotalEmpireValuePerCyclePerGreatPeopleLevel: "Total Empire Value Per Cycle Per Great People Level",
   TotalEmpireValuePerWallSecond: "Total Empire Value Wall Second",
   TotalEmpireValuePerWallSecondPerGreatPeopleLevel: "Total Empire Value Per Wall Second Per Great People Level",
   TotalGameTimeThisRun: "Duración total de partida actual",
   TotalScienceRequired: "Ciencia total requerida",
   TotalStorage: "Almacenamiento total",
   TotalWallTimeThisRun: "Total Wall Time This Run",
   TotalWallTimeThisRunTooltip: "Wall time (aka. elapsed real time) measures the actual time taken for this run. The differs from the game time in that Time Warp in Petra and Offline Production does not affect wall time but it does affect game time",
   TotalWorkers: "Trabajadores totales",
   TowerBridge: "Tower Bridge",
   TowerBridgeDesc: "After constructed, a great person from unlocked ages is born every 3600 cycles (1h game time)",
   TowerOfBabel: "Torre de Babel",
   TowerOfBabelDesc: "Provides +2 Production Multiplier to all buildings that has at least one working building located adjacent to the wonder",
   TradeFillSound: "'Trade Filled' Sound",
   TradeValue: "Valor de Intercambio",
   TraditionCommerce: "Comercio",
   TraditionCultivation: "Agricultura",
   TraditionDescHTML: "Elige entre <b>Agricultura, Commercio, Expansión y Honor</b> como tradiciones de tu Imperio. <b>No podrás cambiar de tradición</b> tras haber escogido una. Puedes mejorar dicha tradición.",
   TraditionExpansion: "Expansión",
   TraditionHonor: "Honor",
   Train: "Tren",
   TranslationPercentage: "Está traducido un %{percentage} del juego a %{language}. Ayuda a mejorar esta traducción en GitHub",
   TranslatorCredit: "Nuzkito, JC",
   Translators: "Traductores",
   TransportAllocatedCapacityTooltip: "Capacidad de construcción asignada para transportar este recurso",
   TransportationWorkers: "Transportation Workers",
   TransportCapacity: "Capacidad de transporte",
   TransportCapacityMultiplier: "Multiplicador de capacidad de transporte",
   TransportManualControlTooltip: "Transporta este recurso para construir/mejorar",
   TransportPlanCache: "Transport Plan Cache",
   TransportPlanCacheDescHTML:
      "Every cycle, each building calculates the best transport plan based on its settings - this process requires high CPU power. Enabling this will attempt to cache the result of the transport plan if it is still valid and therefore reduce CPU usage and frame rate drop. <b>Experimental Feature</b>",
   TribuneUpgradeDescGreatPeopleWarning: "Tu partida actual tiene personajes históricos. Deberías <b>renacer primero</b>. Mejorar al rango Cuestor reiniciará tu partida actual",
   TribuneUpgradeDescGreatPeopleWarningTitle: "Por favor, tienes que renacer para poder hacerlo",
   TribuneUpgradeDescV4:
      "Puedes jugar el juego completo como Tribuno si no planeas participar en las funciones en línea <b>opcionales</b>. Para adquirir acceso sin restricciones a las funciones en línea, necesitarás ascender a Cuestor. <b>Esta es una medida anti-bots para mantener el juego gratuito para todos.</b> Sin embargo, <b>al ascender a Cuestor</b> puedes transferir Personajes Históricos: <ul><li>Hasta el nivel <b>3</b> para la Edad de Bronce, la Edad de Hierro y la Edad Antigua</li><li>Hasta el nivel <b>2</b> para la Edad Media, el Renacimiento y la Industrialización</li><li>Hasta el nivel <b>1</b> para las Guerras Mundiales, la Guerra Fría y la Era de la Información</li></ul>Los Fragmentos de Personajes Históricos por encima del nivel y los niveles de <b>Sabiduría de la Era</b> <b>no</b> pueden transferirse",
   TurnOffFullBuildings: "Turn Off All %{building} With Full Storage",
   TurnOnTimeWarpDesc: "Gasta %{speed} saltos de tiempo por cada segundo y acelera tu imperio para ir a velocidad x%{speed}.",
   Tutorial: "Tutorial",
   TutorialPlayerFlag: "Elige tu bandera",
   TutorialPlayerHandle: "Elige tu nombre de jugador",
   TV: "TV",
   TVStation: "TV Station",
   UnclaimedGreatPersonPermanent: "Tienes <b>personajes históricos permanentes</b> sin reclamar, haz click aquí para reclamarlos",
   UnclaimedGreatPersonThisRun: "Tienes <b>personajes históricos</b> sin reclamar, haz click aquí para reclamarlos",
   UnexploredTile: "Casilla sin explorar",
   UNGeneralAssemblyCurrent: "Actual Asamblea General de la ONU #%{id}",
   UNGeneralAssemblyMultipliers: "<b>+%{count}</b> Multiplicadores de Producción, Capacidad de Trabajo y Almacenamiento para <b>%{buildings}</b>",
   UNGeneralAssemblyNext: "Próxima Asamblea General de la ONU #%{id}",
   UNGeneralAssemblyVoteEndIn: "Puedes cambiar tu voto en cualquier momento antes de que finalice la votación en <b>%{time}</b>",
   UniqueBuildings: "Construcciones únicas",
   UniqueTechMultipliers: "Multiplicadores de Tecnología Únicos",
   UnitedNations: "Naciones Unidas",
   UnitedNationsDesc: "Todos los edificios de nivel IV, V y VI obtienen +1 Multiplicador de Producción, Capacidad de Trabajadores y Almacenamiento. Participa en la Asamblea General de la ONU y vota por un aumento adicional cada semana",
   University: "Universidad",
   UnlockableResearch: "Investigaciones desbloqueables",
   UnlockBuilding: "Desbloquear",
   UnlockTechProgress: "Progreso",
   UnlockXHTML: "Desbloquear <b>%{name}</b>",
   Upgrade: "Mejora",
   UpgradeBuilding: "Mejorar",
   UpgradeBuildingNotProducingDescV2: "Esta construcción está siendo mejorada - <b>la producción se detendrá hasta que la mejora se complete</b>",
   UpgradeTo: "Mejorar a Nivel %{level}",
   Uranium: "Uranio",
   UraniumEnrichmentPlant: "Planta de enriquecimiento de uranio",
   UraniumMine: "Mina de uranio",
   Urbanization: "Urbanización",
   UserAgent: "Agente de Usuario: %{driver}",
   View: "Ver",
   ViewMenu: "Ver",
   ViewTechnology: "Ver",
   Vineyard: "Viñedo",
   VirtualReality: "Realidad virtual",
   Voltaire: "Voltaire",
   WallOfBabylon: "Muro de Babilonia",
   WallOfBabylonDesc: "Todos los edificios obtienen +N Multiplicador de Almacenamiento. N = número de edades desbloqueadas / 2",
   WallStreet: "Wall Street",
   WallStreetDesc: "Todos los edificios que producen monedas, billetes, bonos, acciones y divisas en un rango de 2 casillas obtienen un +N multiplicador de producción. N = valor aleatorio entre 1 y 5 que es diferente por edificio y cambia con cada actualización del mercado. Duplica el efecto de John D. Rockefeller",
   WaltDisney: "Walt Disney",
   Warehouse: "Almacén",
   WarehouseAutopilotSettings: "Ajustes del piloto automático",
   WarehouseAutopilotSettingsEnable: "Activar piloto automático",
   WarehouseAutopilotSettingsRespectCapSetting: "Almacenamiento necesario < Límite",
   WarehouseAutopilotSettingsRespectCapSettingTooltip: "El piloto automático sólo transportará recursos cuya cantidad almacenada esté por debajo del límite",
   WarehouseDesc: "Transporta recursos específicos y provee de almacenamiento adicional",
   WarehouseExtension: "Desbloquea el modo de extensión del almacén para las caravanas. Permite que los almacenes adyacentes a las caravanas se incluyan en el comercio de jugadores",
   WarehouseSettingsAutopilotDesc: "Este almacén usará su capacidad de transporte restante para transportar recursos de construcciones que tienen el almacén lleno. Capacidad restante actual: %{capacity}",
   WarehouseUpgrade: "Desbloquea el piloto automático del almacén. Transporte gratuito entre un almacén y las construcciones adyacentes",
   WarehouseUpgradeDesc: "Transporte gratuito entre un almacén y las construcciones adyacentes",
   Warp: "Saltos de tiempo",
   WarpSpeed: "Warp Speed",
   Water: "Agua",
   WellStockedTooltip: "Los edificios bien abastecidos son aquellos que cuentan con recursos suficientes para su producción, entre los que se incluyen los edificios que están produciendo, que tienen el almacenamiento lleno o que no producen por falta de trabajadores",
   WernherVonBraun: "Wernher von Braun",
   Westminster: "Westminster",
   Wheat: "Trigo",
   WheatFarm: "Cultivo de trigo",
   WildCardGreatPersonDescV2: "Cuando se usa, se convierte en un Personaje Histórico de la misma edad",
   WilliamShakespeare: "William Shakespeare",
   Wine: "Vino",
   Winery: "Bodega",
   WishlistSpaceshipIdle: "Wishlist Spaceship Idle",
   Wonder: "Maravilla",
   WonderBuilderCapacityDescHTML: "La <b>capacidad de construcción</b> al construir maravillas está  afectada por la <b>era</b> y la <b>tecnología</b> que desbloquea a la maravilla",
   WondersBuilt: "Maravillas del mundo construidas",
   WondersUnlocked: "Maravillas del mundo desbloqueadas",
   WonderUpgradeLevel: "Nivel de la Maravilla",
   Wood: "Madera",
   Worker: "Trabajador",
   WorkerCapacityMultiplier: "Multiplicador de capacidad de trabajo",
   WorkerHappinessPercentage: "Multiplicador de felicidad",
   WorkerMultiplier: "Capacidad de trabajo",
   WorkerPercentagePerHappiness: "%{value}% multiplicador de trabajo por felicidad",
   Workers: "Trabajadores",
   WorkersAvailableAfterHappinessMultiplier: "Trabajadores después del multiplicador de felicidad",
   WorkersAvailableBeforeHappinessMultiplier: "Trabajadores antes del multiplicador de felicidad",
   WorkersBusy: "Trabajadores ocupados",
   WorkerScienceProduction: "Producción de ciencia por trabajadores",
   WorkersRequiredAfterMultiplier: "Trabajadores necesarios",
   WorkersRequiredBeforeMultiplier: "Capacidad de trabajo necesaria",
   WorkersRequiredForProductionMultiplier: "Capacidad de producción por trabajador",
   WorkersRequiredForTransportationMultiplier: "Capacidad de transporte por trabajador",
   WorkersRequiredInput: "Transporte",
   WorkersRequiredOutput: "Producción",
   WorldWarAge: "Guerras mundiales",
   WorldWideWeb: "Red Informática Mundial",
   WritersGuild: "Gremio de escritores",
   Writing: "Escritura",
   WuZetian: "Emperatriz Wu Zetian",
   WuZetianDesc: "+%{value} multiplicador de capacidad de transporte",
   Xuanzang: "Xuanzang",
   YangtzeRiver: "Río Yangtze",
   YangtzeRiverDesc:
      "Todos los edificios que consumen agua obtienen +1 Multiplicador de Producción, Capacidad de Trabajadores y Almacenamiento. Duplica el efecto de Zheng He (Personaje Histórico). Cada nivel permanente de la Emperatriz Wu Zetian (Personaje Histórico) proporciona +1 Multiplicador de Almacenamiento a todos los edificios",
   YearOfTheSnake: "Año de la Serpiente",
   YearOfTheSnakeDesc:
      "After completed, when entering a new age, instead of getting one great person of each unlocked age, get the same amount of great people in the current age. All buildings within 2-tile range get +1 Production Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Production Multiplier to buildings within 2-tile range. This wonder can only be constructed during the lunar new year period (1.20 ~ 2.10)",
   YellowCraneTower: "Torre de la Grulla Amarilla",
   YellowCraneTowerDesc: "+1 elección al elegir Personajes Históricos. Todos los edificios dentro de un rango de 1 casilla obtienen +1 Multiplicador de Producción, Capacidad de Trabajadores y Almacenamiento. Cuando se construyen junto al Río Yangtze, el rango aumenta a 2 casillas",
   YuriGagarin: "Yuri Gagarin",
   ZagrosMountains: "Montañas Zagros",
   ZagrosMountainsDesc: "Todos los edificios adyacentes que tengan menos de 5 Multiplicadores de Producción obtienen +2 Multiplicadores de Producción. Duplica el efecto de Nabucodonosor II (Personaje Histórico)",
   ZahaHadid: "Zaha Hadid",
   ZahaHadidDesc: "+%{value} Multiplicador de capacidad del constructor",
   Zenobia: "Zenobia",
   ZenobiaDesc: "+%{value}h de almacenamiento de saltos de tiempo de Petra",
   ZhengHe: "Zheng He",
   ZigguratOfUr: "Ziggurat of Ur",
   ZigguratOfUrDescV2:
      "Cada 10 puntos de felicidad (limitado) otorga +1 Multiplicador de Producción a todos los edificios que no produzcan trabajadores y que se hayan desbloqueado en eras anteriores (máximo = número de eras desbloqueadas / 2). Las maravillas (incluidas las naturales) ya no otorgan +1 de felicidad. El efecto se puede desactivar",
   Zoroaster: "Zaratustra",
   Zugspitze: "Zugspitze",
   ZugspitzeDesc: "Por cada Era desbloqueada, obtienes una casilla que puede utilizarse para mejorar un nivel extra a cualquier Personaje histórico nacido durante esta partida.",
};
