@import "xp.css/dist/98.css";
@import "Modern.css";
@import "UIExtension.css";
@import "ChatPanel.css";
@import "ResourcePanel.css";

:root {
   --game-ui-width: 45rem;
   --base-font-size: 62.5%;
}

html {
   font-size: var(--base-font-size);
}

body.old-fashioned-cursor,
body.old-fashioned-cursor * {
   cursor: url("../images/w98normal.png"), default;
}

body.old-fashioned-cursor .text-link,
body.old-fashioned-cursor .text-link *,
body.old-fashioned-cursor .pointer,
body.old-fashioned-cursor .pointer * {
   cursor: url("../images/w98hand.png"), pointer;
}

body.big-old-fashioned-cursor,
body.big-old-fashioned-cursor * {
   cursor: url("../images/w98big.png"), default;
}

body.big-old-fashioned-cursor .text-link,
body.big-old-fashioned-cursor .text-link *,
body.big-old-fashioned-cursor .pointer,
body.big-old-fashioned-cursor .pointer * {
   cursor: url("../images/w98handbig.png"), pointer;
}

body {
   cursor: default;
   user-select: none;
   -webkit-user-select: none;
   overflow: hidden;
   position: fixed;
   left: 0;
   top: 0;
   bottom: 0;
   right: 0;
   margin: 0;
   padding: 0;
}

* {
   box-sizing: border-box;
}

#game-ui {
   position: absolute;
   width: var(--game-ui-width);
   right: 0;
   top: 0;
   bottom: 0;
}

#debug-ui .window {
   position: absolute;
   width: 350px;
   left: 10px;
   top: 50px;
}

#debug-ui .window .table-view {
   overflow-x: hidden;
   overflow-y: auto;
   height: 400px;
}

#game-canvas {
   position: absolute;
   left: 0;
   top: 0;
   bottom: 0;
   right: var(--game-ui-width);
}

#game-ui .window {
   height: 100%;
   display: flex;
   flex-direction: column;
}

#game-ui .window .window-body,
#global-modal .window .window-body {
   padding: 8px;
   margin: 0;
   overflow-x: hidden;
   overflow-y: auto;
}

#global-modal.overlay {
   position: absolute;
   left: 0;
   right: 0;
   top: 0;
   bottom: 0;
   display: flex;
   align-items: center;
   justify-content: center;
   background: rgba(0, 0, 0, 0.5);
   z-index: 200;
}

#global-modal .window {
   margin: 0 auto;
   width: 350px;
}

#global-toast {
   display: flex;
   align-items: center;
   justify-content: center;
}

#global-toast .toast {
   max-width: 50vw;
   position: absolute;
   top: 50px;
   z-index: 201;
}

body .tippy-box {
   font-size: 1.1rem;
   font-family: "Pixelated MS Sans Serif", sans-serif;
   -webkit-font-smoothing: none;
}

#patch-notes-panel {
   position: absolute;
   top: 5.5rem;
   left: 1rem;
}
