@font-face {
   font-family: "Roboto Mono";
   font-style: normal;
   font-weight: 500;
   font-display: swap;
   src: url(../fonts/RobotoMono.woff2) format("woff2");
}

.modern .code,
.code,
code {
   font-family: "Roboto Mono", monospace;
   font-weight: 500;
}

.menus {
   display: flex;
   background: silver;
}

.menu-button {
   padding: 4px 6px;
   background: silver;
   position: relative;
}

.menu-popover {
   box-shadow: inset -1px -1px #0a0a0a, inset 1px 1px #dfdfdf, inset -2px -2px grey, inset 2px 2px #fff;
   background: silver;
   padding: 3px;
}

.menu-button .menu-popover {
   position: absolute;
   z-index: 1;
   top: 2rem;
   left: 0;
   display: none;
}

.menu-button .menu-popover.active {
   display: block;
   z-index: 9;
}

.menu-popover-item {
   padding: 3px 20px 3px 0;
   white-space: nowrap;
}

.menu-popover-item:hover {
   background: #000080;
   color: #fff;
}

input[type="email"],
input[type="password"],
input[type="text"] {
   border: 0 none;
}

input[type="email"]:disabled,
input[type="email"]:read-only,
input[type="number"]:disabled,
input[type="number"]:read-only,
input[type="password"]:disabled,
input[type="password"]:read-only,
input[type="text"]:disabled,
input[type="text"]:read-only,
textarea:disabled {
   background-color: silver;
   cursor: not-allowed;
}

input[type="range"] {
   margin: 0;
   height: 16px;
   cursor: url("../images/w98normal.png"), default;
}

input,
button,
select {
   color: #000000;
}

input:disabled,
button:disabled,
select:disabled {
   color: rgba(16, 16, 16, 0.3);
}

select::-webkit-scrollbar {
   all: unset;
   width: 12px;
}
select::-webkit-scrollbar-thumb {
   all: unset;
   background: #999;
   border-radius: 6px;
   border: 2px solid #dfdfdf;
}
select::-webkit-scrollbar-track {
   background: #dfdfdf;
}

summary::-webkit-details-marker {
   display: none;
}

.separator {
   border-top: 1px solid #808080;
   border-bottom: 1px solid #fff;
   margin: 0 2px;
   height: 2px;
   position: relative;
}

.separator-vertical {
   border-left: 1px solid #808080;
   border-right: 1px solid #fff;
   width: 2px;
   height: 100%;
}

.separator.has-title > div {
   background: silver;
   position: absolute;
   left: 10px;
   top: -6px;
   padding: 0 2px;
}

fieldset .separator {
   border-top: 1px solid #808080;
   border-bottom: 1px solid #fff;
   margin: 10px -10px;
   height: 2px;
}

.menu-button:hover {
   box-shadow: 1px 1px 0 rgb(255, 255, 255) inset, -1px -1px 0 rgb(128, 128, 128) inset;
}

.menu-button.active {
   box-shadow: 1px 1px 0 rgb(128, 128, 128) inset, -1px -1px 0 rgb(255, 255, 255) inset;
}

.menu-hotkey {
   text-decoration: underline;
}

.p5 {
   padding: 5px;
}

.pv5 {
   padding-top: 5px;
   padding-bottom: 5px;
}

.pv10 {
   padding-top: 10px;
   padding-bottom: 10px;
}

.ph5 {
   padding-left: 5px;
   padding-right: 5px;
}

.ph10 {
   padding-left: 10px;
   padding-right: 10px;
}

.p0 {
   padding: 0;
}

.p10 {
   padding: 10px;
}

.p8 {
   padding: 8px;
}

.p5 {
   padding: 5px;
}

.inset-deep {
   box-shadow: inset -1px -1px #fff, inset 1px 1px grey, inset -2px -2px #dfdfdf, inset 2px 2px #0a0a0a;
}

.inset-deep-2 {
   border: 2px solid #666;
   border-left: 2px solid #666;
   border-right: 2px solid #dfdfdf;
   border-bottom: 2px solid #dfdfdf;
}

.inset-deep-2.white,
.inset-deep.white,
.outset-shallow-2.white,
.inset-shallow-2.white {
   background: #fff;
}

.inset-shallow {
   border-style: solid;
   border-width: 1px;
   border-color: rgb(128, 128, 128) rgb(255, 255, 255) rgb(255, 255, 255) rgb(128, 128, 128);
   background-color: rgb(192, 192, 192);
}

.inset-shallow-2 {
   border: 1px inset #eee;
}

.outset-shallow-2 {
   border: 1px outset #eee;
}

.inset-shallow.white {
   background-color: #fff;
}

.text-none {
   color: transparent;
}

.text-green {
   color: #047e00;
}

.text-orange {
   color: #e67e22;
}

.text-red {
   color: #e74c3c;
}

.text-right {
   text-align: right;
}

.text-strong {
   font-weight: bold;
}

.text-large {
   font-size: 150%;
}

.text-justify {
   text-align: justify;
}

.text-desc {
   color: rgba(16, 16, 16, 0.6);
}

.text-grey {
   color: #999;
}

.text-center {
   text-align: center;
}

@font-face {
   font-family: "Material Symbols Outlined";
   font-style: normal;
   font-weight: 400;
   src: url(../fonts/MaterialIcons.woff2) format("woff2");
}

@font-face {
   font-family: "Material Symbols Filled";
   font-style: normal;
   font-weight: 400;
   src: url(../fonts/MaterialIconsFilled.woff2) format("woff2");
}

.m-icon {
   font-family: "Material Symbols Outlined";
   font-weight: normal;
   font-style: normal;
   font-size: 2.4rem;
   line-height: 1;
   letter-spacing: normal;
   text-transform: none;
   white-space: nowrap;
   word-wrap: normal;
   direction: ltr;
   -webkit-font-feature-settings: "liga";
   -webkit-font-smoothing: antialiased;
}

.m-icon.fill {
   font-family: "Material Symbols Filled";
}

.m-icon.small {
   font-size: 1.2rem;
}

.mr2 {
   margin-right: 2px;
}

.mr5 {
   margin-right: 5px;
}

.mr10 {
   margin-right: 10px;
}

.mr15 {
   margin-right: 15px;
}

.mr20 {
   margin-right: 20px;
}

.ml5 {
   margin-left: 5px;
}

.ml10 {
   margin-left: 10px;
}

.ml15 {
   margin-left: 15px;
}

.ml20 {
   margin-left: 20px;
}

.row {
   display: flex;
   flex-direction: row;
   align-items: center;
}

.g5 {
   gap: 5px;
}

.g10 {
   gap: 10px;
}

.col {
   display: flex;
   flex-direction: column;
}

.cc {
   align-items: center;
   justify-content: center;
}

.jcc {
   justify-content: center;
}

.jce {
   justify-content: flex-end;
}

.f1,
.row .f1,
.col .f1 {
   flex: 1;
}

.row .fs {
   align-self: flex-start;
}

.w100 {
   width: 100%;
}

.mv5 {
   margin-top: 5px;
   margin-bottom: 5px;
}

.mh5 {
   margin-left: 5px;
   margin-right: 5px;
}

.mh10 {
   margin-left: 10px;
   margin-right: 10px;
}

.mt5 {
   margin-top: 5px;
}

.mb5 {
   margin-bottom: 5px;
}

.mt10 {
   margin-top: 10px;
}

.mt20 {
   margin-top: 20px;
}

.mb10 {
   margin-bottom: 10px;
}

.mb15 {
   margin-bottom: 15px;
}

.mb20 {
   margin-bottom: 20px;
}

.mv10 {
   margin-top: 10px;
   margin-bottom: 10px;
}

.mh0 {
   margin-left: 0;
   margin-right: 0;
}

.sep5 {
   height: 5px;
}

.sep10 {
   height: 10px;
}

.sep15 {
   height: 15px;
}

.sep20 {
   height: 20px;
}

.text-blue,
.text-link,
a {
   color: #0000ff;
}

.text-link,
.pointer {
   cursor: pointer;
}

.text-link.disabled {
   color: #666;
}

.lh2 {
   line-height: 2;
}

.meter {
   height: 18px;
   position: relative;
   box-shadow: inset -1px -1px #fff, inset 1px 1px grey, inset -2px -2px #dfdfdf, inset 2px 2px #0a0a0a;
   padding: 4px 4px 3px;
   border: none;
   background-color: #fff;
   box-sizing: border-box;
   -webkit-appearance: none;
   -moz-appearance: none;
   appearance: none;
   border-radius: 0;
}

.meter > .fill {
   display: block;
   height: 100%;
   background-color: #00289e;
   position: relative;
   overflow: hidden;
   background-image: linear-gradient(
      90deg,
      #00289e 40%,
      #fff 0,
      #fff 50%,
      #00289e 0,
      #00289e 90%,
      #fff 0,
      #fff
   );
   background-size: 24px 24px;
}

ul.tree-view > li:first-child {
   margin-top: 0;
}

.toast {
   background: #ffc;
   padding: 5px 10px;
   border: 1px solid grey;
   color: #333;
   border-radius: 3px;
   font-family: "Pixelated MS Sans Serif", Arial;
   font-size: 1.1rem;
   z-index: 300;
}

.warning-banner {
   background: #ffc;
   padding: 5px;
   border: 1px solid grey;
   color: #333;
   border-radius: 3px;
}

.warning-banner ul {
   padding: 0;
   margin: 5px 0;
}

.warning-banner ul li {
   list-style-type: square;
}

.table-view {
   background: #fff;
   border: 2px solid #666;
   border-left: 2px solid #666;
   border-right: 2px solid #eee;
   border-bottom: 2px solid #eee;
}

.table-view table {
   width: 100%;
   border-collapse: collapse;
}

.table-view th {
   background-color: rgb(192, 192, 192);
   font-weight: normal;
   text-align: left;
   box-shadow: inset -1px -1px #404040, inset 1px 1px #fff, inset -2px -2px grey, inset 2px 2px #dfdfdf;
}

.table-view.sticky-header {
   overflow: auto;
}

.table-view.sticky-header th {
   position: sticky;
   top: 0;
   z-index: 1;
}

.table-view th.text-right {
   text-align: right;
}

.table-view th.text-center {
   text-align: center;
}

.table-view th,
.table-view td {
   vertical-align: middle;
   padding: 3px 6px;
}

.table-view tr:nth-child(even) {
   background: #efefef;
}

.table-view tr.blue {
   background: rgb(188, 235, 255);
}

.table-view tr:hover,
.table-view tr.blue:hover {
   background: #ffc;
}

.table-view tr.selected {
   position: relative;
   background-color: #0a246a;
   color: #fff;
}

.table-view tr.selected:after {
   content: " ";
   position: absolute;
   left: 0;
   right: 0;
   top: 0;
   bottom: 0;
   border: 1px dotted #f5db95;
   pointer-events: none;
}

.table-view tr.selected .text-link {
   color: #fff;
}

.table-view .right {
   text-align: right;
}

.nowrap {
   white-space: nowrap;
}

.window-content {
   background: #fff;
   padding: 6px;
   flex: 1;
}

button {
   min-width: unset;
   display: block;
}

.production-warning {
   color: #e74c3c;
   font-weight: bold;
}

.player-flag {
   height: 17px;
   display: block;
}

.player-flag-large {
   height: 24px;
   display: block;
}

.chat-message-item .player-flag {
   height: 15px;
   display: block;
   margin: 0 0 0 2px;
}

.player-level {
   height: 12px;
   display: block;
}

.with-help {
   text-decoration: dotted underline;
   text-underline-offset: 0.25em;
   cursor: help;
}

.help-cursor {
   cursor: help;
}

.title-bar-controls button[aria-label="Restore"],
.title-bar-controls button[aria-label].restore {
   background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='8' height='9' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='%23000' d='M2 0h6v2H2zM7 2h1v4H7zM2 2h1v1H2zM6 5h1v1H6zM0 3h6v2H0zM5 5h1v4H5zM0 5h1v4H0zM1 8h4v1H1z'/%3E%3C/svg%3E");
   background-position: top 2px left 3px;
   background-repeat: no-repeat;
}

.app-region-drag {
   app-region: drag;
}

.app-region-none {
   app-region: none;
}
