export const JP = {
   About: "CivIdleについて",
   AbuSimbel: "アブ・シンベル神殿",
   AbuSimbelDesc: "ラムセス二世の効果を二倍。全ての隣り合う遺産は+1幸福度を得る",
   AccountActiveTrade: "同時にトレード可能な数",
   AccountChatBadge: "チャットバッジ",
   AccountCustomColor: "カスタム色",
   AccountCustomColorDefault: "デフォルト",
   AccountGreatPeopleLevelRequirement: "必要偉人レベル",
   AccountLevel: "アカウントレベル",
   AccountLevelAedile: "アエディル",
   AccountLevelConsul: "コンスル",
   AccountLevelMod: "モデレーター",
   AccountLevelPlayTime: "プレイ時間が %{requiredTime} を上回る(現在のプレイ時間は %{actualTime})",
   AccountLevelPraetor: "プラエター",
   AccountLevelQuaestor: "クエスター",
   AccountLevelSupporterPack: "サポーターパックを所持",
   AccountLevelTribune: "トリビューン",
   AccountLevelUpgradeConditionAnyHTML: "アカウントをアップグレードするには、<b>以下の条件のうち一つ</b>を達成する必要があります。",
   AccountPlayTimeRequirement: "必要なプレイ時間",
   AccountRankUp: "アカウントランクをアップグレード",
   AccountRankUpDesc: "全ての進捗は新しいランクに引き継がれます",
   AccountRankUpTip: "おめでとうございます！あなたのアカウントは上位ランクの対象となります - ここをクリックしてアップグレードしてください！",
   AccountSupporter: "サポーターパック所持者",
   AccountTradePriceRange: "トレード価格範囲",
   AccountTradeTileReservationTime: "トレードタイル予約期間",
   AccountTradeTileReservationTimeDesc: "これは、あなたが最後にオンラインになってから、あなたのトレードタイルが予約される時間です。予約期間が終了すると、あなたのタイルは他のプレイヤーが使用できるようになります",
   AccountTradeValuePerMinute: "1分あたりのトレード可能額",
   AccountTypeShowDetails: "アカウントレベルの詳細を表示",
   AccountUpgradeButton: "クエスターランクへアップグレード",
   AccountUpgradeConfirm: "アカウントをアップグレード",
   AccountUpgradeConfirmDescV2: "アカウントをアップグレードすると、<b>現在の進行状況はリセットされ、</b>許可されたレベルの範囲内の恒久偉人を持ち越すことが出来ます。<b>この操作はやり直すことは出来ません。</b>続けますか？",
   Acknowledge: "Acknowledge",
   Acropolis: "アクロポリス",
   ActorsGuild: "役者ギルド",
   AdaLovelace: "エイダ・ラブレス",
   AdamSmith: "アダム・スミス",
   AdjustBuildingCapacity: "生産能力",
   AdvisorElectricityContent:
      "発電所は2つの新しいシステムから成り立っており、1つ目は「電力」で、発電所に隣接する稲妻タイルで示されます。一部の建物（World War時代のラジオなど）には、右リストに「電力が必要」と表示されます。 <b>機能させるには電力タイルの上に建設する必要があります。</b>. 電力が必要でかつ、電力が供給されている建物は、その建物に隣接するタイルに電力を送信するため、少なくとも1つの建物が発電所に接している限り、建物同士で電力を供給できます。<br><br>もう一つのシステム「電化」は、 <b>全ての建物</b>（科学・生産者を生産しない）マップ上のどの建物にも適用できます。発電所で発電された電力が消費され、建物の消費と生産の両方が増加します。電化のレベルが上がると、必要な電力もより上昇します。電力を必要とする建物を電化する方が、電力を必要としない建物を電化するより効率的です。",
   AdvisorElectricityTitle: "電力と電化",
   AdvisorGreatPeopleContent:
      "新しい時代の技術をアンロックするたびに、その時代とそれ以前の時代の偉人を獲得することが出来ます。偉人たちは生産や技術、幸福度などに関する多数の偉人ボーナスをもたらします。<br><br>これらの偉人ボーナスは、転生するまでは永続します。転生すると、すべての偉人が恒久偉人として引き継がれます。<br><br>転生後に同じ偉人を獲得すると、偉人ボーナスと恒久偉人ボーナスは重複し、さらに獲得すると余剰分は恒久偉人のアップグレードに使用できます。これは、ホームの<b>偉人の管理</b>からアクセスできます。",
   AdvisorGreatPeopleTitle: "偉人",
   AdvisorHappinessContent:
      "幸福度は、CivIdleの繁栄を制限するコアメカニズムです。幸福度は、新しいテクノロジーをアンロックする、新しい時代へ進む、遺産を建設する、幸福度が増加する偉人を獲得するほか、ゲームを進めていく中でもいくつかの方法で得られます。<b>建物を建設すると幸福度が1減ります。</b> 幸福度が0以上・以下になると、合計生産者に2%のボーナスまたはペナルティが課せられます（幸福度は-50・+50上限）幸福度の詳細な内訳は、<b>ホームの幸福度セクション</b>で確認できます。",
   AdvisorHappinessTitle: "人々の幸福度を維持する",
   AdvisorOkay: "理解した！",
   AdvisorScienceContent:
      "労働生産者がサイエンスを生産することで、新しいテクノロジーをアンロックし、文明を発展させることができます。研究メニューには、さまざまな方法でアクセスできます。科学メーターをクリックするか、ホームでアンロック可能なテクノロジーにアクセスするか、[表示] メニューを使用します。これらはすべて、テクノロジーツリーに移動し、すべてのテクノロジーと、それぞれに必要なサイエンスの量を表します。新しいテクノロジーを習得するのに十分なサイエンスがある場合は、それをクリックして、サイドバー メニューで [アンロック] を押すだけです。 <b>時代を進むごとに、より多くのサイエンスが必要になりますが、サイエンスを獲得するための新しい、より良い方法も発見されるでしょう。</b>",
   AdvisorScienceTitle: "科学的発見!",
   AdvisorSkipAllTutorials: "全てのチュートリアルをスキップする",
   AdvisorStorageContent:
      "建物には十分な容量がありますが、長期間放置されると容量が満杯になることがあります。<b>容量が満杯になると、生産出来なくなります。</b>大量の備蓄があるので必ずしも問題ではありません。ですが、通常は生産を継続させるほうが良いでしょう。<br><br>容量が満杯になった場合の対処方法としては倉庫が有用です。倉庫を建設すると、発見した全てのプロダクトのメニューが表示され、全てのプロダクトの合計が倉庫のレベルと容量倍率に基づいて倉庫が引き出せる量の範囲内である限り、任意のプロダクトを任意の量で引き出すように倉庫を設定できます。<br><br>倉庫を設定する簡単な方法は、倉庫に輸入したい各プロダクトをチェックし、「選択したプロダクト間で再分配」ボタンを使用して輸入率と保管量を均等に分割することです。建物が倉庫から引き出せるようにしたい場合は、「最大量以下で輸出」オプションも必ずオンにしてください。",
   AdvisorStorageTitle: "備蓄と倉庫",
   AdvisorTraditionContent:
      "いくつかの遺産(チョガ・ザンビール、ルクソール神殿、ビッグベン)は、更にアップグレードすることで現在の文明に新たな道を示すことができます。それぞれ、伝統・宗教・イデオロギーについて4つの選択肢から1つを選択できます。<br><br>1つを選択すると、転生するまで固定されますが、転生後別の道を選択することができます。選択すると、必要なプロダクトを費やすことで更なるアップグレードが可能です。各ティアのボーナスは累積されるため、ティア1でXの生産量が+1になり、ティア2でXの生産量が+1になると、ティア2ではXの生産量が合計で+2となります。",
   AdvisorTraditionTitle: "Choosing Paths and Upgradeable Wonders",
   AdvisorWonderContent:
      "Wonders are special buildings that provide global effects which can have a significant impact on your gameplay. In addition to their listed functions, all Wonders give +1 Happiness as well. You need to be careful though, as <b>Wonders require a LOT of materials, and have a higher than normal Builder Capacity as well</b>. This means that they can easily clear out your stockpiles of needed inputs, leaving your other buildings starving. <b>You can turn each input on and off freely</b>, allowing you to build it in stages while you stockpile enough materials to keep everything running.",
   AdvisorWonderTitle: "Wonders Of The World",
   AdvisorWorkerContent:
      "Every time a building produces or transports goods, this requires workers. If you don't have enough workers available, some buildings will fail to run that cycle. The obvious fix for this is to increase your total available workers by building or upgrading structures that make workers (Hut/House/Apartment/Condo).<br><br><b>Be aware though, that buildings turn off while upgrading, and can't provide any of their resources, which includes workers, so you might want to only upgrade one housing building at a time.</b> A good goal for the early stages of the game is to keep about 70% of your workers busy. If more than 70% are busy, upgrade/build housing. If fewer than 70% are busy, expand production.",
   AdvisorWorkerTitle: "Worker Management",
   Aeschylus: "Aeschylus",
   Agamemnon: "Agamemnon",
   AgeWisdom: "Age Wisdom",
   AgeWisdomDescHTML: "Each level of Age Wisdom provides <b>an equivalent level</b> of eligible Permanent Great People of that age - it can be upgraded with eligible Permanent Great People shards",
   AgeWisdomGreatPeopleShardsNeeded: "You need %{amount} more great people shards for the next Age Wisdom upgrade",
   AgeWisdomGreatPeopleShardsSatisfied: "You have enough great people shards for the next Age Wisdom upgrade",
   AgeWisdomNeedMoreGreatPeopleShards: "Need More Great People Shards",
   AgeWisdomNotEligible: "This Great Person is not eligible for Age Wisdom",
   AgeWisdomSource: "%{age} Wisdom: %{person}",
   AgeWisdomUpgradeWarningHTMLV3: "Age Wisdom <b>does not carry over</b> when upgrading from Tribune to Quaestor",
   AGreatPersonIsBorn: "A Great Person Is Born",
   AircraftCarrier: "Aircraft Carrier",
   AircraftCarrierYard: "Aircraft Carrier Yard",
   Airplane: "Airplane",
   AirplaneFactory: "Airplane Factory",
   Akitu: "Akitu: Ziggurat Of Ur and Euphrates River apply to buildings unlocked in the current age",
   AlanTuring: "Alan Turing",
   AlanTuringDesc: "+%{value} Science From Idle Workers",
   AlbertEinstein: "Albert Einstein",
   Alcohol: "アルコール",
   AldersonDisk: "Alderson Disk",
   AldersonDiskDesc: "+25 Happiness. This wonder can be upgraded and each additional upgrade provides +5 Happiness",
   Alloy: "Alloy",
   Alps: "アルプス山脈",
   AlpsDesc: "Every 10th level of a building gets +1 Production Capacity (+1 Consumption Multiplier, +1 Production Multiplier)",
   Aluminum: "アルミニウム",
   AluminumSmelter: "Aluminum Smelter",
   AmeliaEarhart: "Amelia Earhart",
   American: "American",
   AndrewCarnegie: "Andrew Carnegie",
   AngkorWat: "Angkor Wat",
   AngkorWatDesc: "All adjacent buildings get +1 Worker Capacity Multiplier. Provide 1000 Workers",
   AntiCheatFailure: "Your account rank has been restricted due to <b>failing to pass anti-cheat</b> check. Contact the developer if you want to appeal this",
   AoiMatsuri: "Aoi Matsuri: Mount Fuji generates double the warp",
   Apartment: "Apartment",
   Aphrodite: "Aphrodite",
   AphroditeDescV2: "+1 Builder Capacity Multiplier for each level when upgrading buildings over Level 20. All unlocked Classical Age permanent great people get +1 level this run",
   ApolloProgram: "Apollo Program",
   ApolloProgramDesc: "All rocket factories get +2 Production, Worker Capacity and Storage Multiplier. Satellite factories, spaceship factories and nuclear missile silos get +1 Production Multiplier for each adjacent rocket factory",
   ApplyToAll: "全てに適用",
   ApplyToAllBuilding: "全ての %{building} に適用",
   ApplyToBuildingInTile: "%{tile} タイル内にある全ての %{building} に適用",
   ApplyToBuildingsToastHTML: "<b>%{count}</b> つの<b> %{building} </b>に適用しました",
   Aqueduct: "Aqueduct",
   ArcDeTriomphe: "Arc de Triomphe",
   ArcDeTriompheDescV2: "Every 1 happiness (capped) provides +1 builder capacity to all buildings",
   Archimedes: "Archimedes",
   Architecture: "Architecture",
   Aristophanes: "Aristophanes",
   AristophanesDesc: "+%{value} Happiness",
   Aristotle: "Aristotle",
   Arithmetic: "Arithmetic",
   Armor: "Armor",
   Armory: "Armory",
   ArtificialIntelligence: "Artificial Intelligence",
   Artillery: "Artillery",
   ArtilleryFactory: "Artillery Factory",
   AshokaTheGreat: "Ashoka the Great",
   Ashurbanipal: "Ashurbanipal",
   Assembly: "Assembly",
   Astronomy: "Astronomy",
   AtomicBomb: "Atomic Bomb",
   AtomicFacility: "Atomic Facility",
   AtomicTheory: "Atomic Theory",
   Atomium: "Atomium",
   AtomiumDescV2: "All buildings that produce science (except Clone Labs) within 2 tile range get +5 Production Multiplier. Generate science that is equal to the science production within 2 tile range. When completed, generate one-time science equivalent to the cost of the most expensive unlocked technology",
   Autocracy: "Autocracy",
   Aviation: "Aviation",
   Babylonian: "Babylonian",
   BackToCity: "Back To City",
   BackupRecovery: "Backup Recovery",
   Bakery: "Bakery",
   Ballistics: "Ballistics",
   Bank: "Bank",
   Banking: "Banking",
   BankingAdditionalUpgrade: "All buildings that are level 10 or higher get +1 Storage Multiplier",
   Banknote: "Banknote",
   BaseCapacity: "Base Capacity",
   BaseConsumption: "Base Consumption",
   BaseMultiplier: "Base Multiplier",
   BaseProduction: "Base Production",
   BastilleDay: "Bastille Day: Double the effect of Centre Pompidou and Arc de Triomphe. Double the Culture generation from Mont Saint-Michel",
   BatchModeTooltip: "%{count} buildings are currently selected. Upgrade will apply to all selected buildings",
   BatchSelectAllSameType: "All Same Type",
   BatchSelectAnyType1Tile: "Any Type in 1 Tile",
   BatchSelectAnyType2Tile: "Any Type in 2 Tile",
   BatchSelectAnyType3Tile: "Any Type in 3 Tile",
   BatchSelectSameType1Tile: "Same Type in 1 Tile",
   BatchSelectSameType2Tile: "Same Type in 2 Tile",
   BatchSelectSameType3Tile: "Same Type in 3 Tile",
   BatchSelectSameTypeSameLevel: "Same Type Same Level",
   BatchSelectThisBuilding: "This Building",
   BatchStateSelectActive: "Active",
   BatchStateSelectAll: "All",
   BatchStateSelectTurnedFullStorage: "Full Storage",
   BatchStateSelectTurnedOff: "Turned Off",
   BatchUpgrade: "Batch Upgrade",
   Battleship: "Battleship",
   BattleshipBuilder: "Battleship Builder",
   BigBen: "Big Ben",
   BigBenDesc: "+2 Science From Busy Workers. Choose an empire ideology, unlock more boost with each choice",
   Biplane: "Biplane",
   BiplaneFactory: "Biplane Factory",
   Bitcoin: "Bitcoin",
   BitcoinMiner: "Bitcoin Miner",
   BlackForest: "Black Forest",
   BlackForestDesc: "When discovered, reveals all wood tiles on the map. Spawn wood on adjacent tiles. All buildings that consume Wood or Lumber get +5 Production Multiplier",
   Blacksmith: "Blacksmith",
   Blockchain: "Blockchain",
   BlueMosque: "Blue Mosque",
   BlueMosqueDesc: "All wonders provide +1 Production, Worker Capacity and Storage Multiplier to adjacent buildings. When constructed next to Hagia Sophia, provide extra +1 Production, Worker Capacity and Storage Multiplier",
   BobHope: "Bob Hope",
   BobHopeDesc: "+%{value} Happiness",
   Bond: "Bond",
   BondMarket: "Bond Market",
   Book: "Book",
   BoostCyclesLeft: "Boost Cycles Left",
   BoostDescription: "+%{value} %{multipliers} for %{buildings}",
   Borobudur: "Borobudur",
   BorobudurDesc: "Borobudur",
   BranCastle: "Bran Castle",
   BranCastleDesc: "Bran Castle",
   BrandenburgGate: "Brandenburg Gate",
   BrandenburgGateDesc: "All coal mines and oil wells get +1 Production, Worker Capacity and Storage Multiplier. Oil refineries get +1 Production, Worker Capacity and Storage Multiplier for each adjacent oil tile",
   Bread: "Bread",
   Brewery: "Brewery",
   Brick: "Brick",
   Brickworks: "Brickworks",
   BritishMuseum: "British Museum",
   BritishMuseumChooseWonder: "Choose a Wonder",
   BritishMuseumDesc: "After constructed, can transform into to a unique wonder from other civilizations",
   BritishMuseumTransform: "Transform",
   Broadway: "Broadway",
   BroadwayCurrentlySelected: "Currently selected",
   BroadwayDesc: "A great person of the current age and a great person of the previous age are born. Select a great person and double his/her effect",
   BronzeAge: "Bronze Age",
   BronzeTech: "Bronze",
   BuddhismLevelX: "Buddhism %{level}",
   Build: "Build",
   BuilderCapacity: "Builder Capacity",
   BuildingColor: "Building Color",
   BuildingColorMatchBuilding: "Copy Color From Building",
   BuildingColorMatchBuildingTooltip: "Copies color of production buildings to their resources. If there is more than one option, selects randomly",
   BuildingDefaults: "Building Defaults",
   BuildingDefaultsCount: "%{count} properties are overridden in building default",
   BuildingDefaultsRemove: "Clear all property overrides",
   BuildingEmpireValue: "Building Empire Value / Resource Empire Value",
   BuildingMultipliers: "Boost",
   BuildingName: "Name",
   BuildingNoMultiplier: "%{building} is <b>not affected</b> by any multipliers (production, worker capacity, storage, etc)",
   BuildingSearchText: "Type a building or resource name to search",
   BuildingTier: "Tier",
   Cable: "Cable",
   CableFactory: "Cable Factory",
   Calendar: "Calendar",
   CambridgeUniversity: "Cambridge University",
   CambridgeUniversityDesc: "+1 Age Wisdom level for Renaissance and ages after",
   CambridgeUniversitySource: "Cambridge University (%{age})",
   Cancel: "Cancel",
   CancelAllUpgradeDesc: "Cancel all %{building} upgrades",
   CancelUpgrade: "Cancel Upgrade",
   CancelUpgradeDesc: "All the resources that have already been transported will remain in the storage",
   Cannon: "Cannon",
   CannonWorkshop: "Cannon Workshop",
   CannotEarnPermanentGreatPeopleDesc: "Because this is a trial run, permanent great people cannot be earned",
   Capitalism: "Capitalism",
   Cappadocia: "Cappadocia",
   CappadociaDesc: "All buildings within 3 tile range get +1 Production, Worker Capacity and Storage Multiplier for every level above Level 30",
   Car: "Car",
   Caravansary: "Caravansary",
   CaravansaryDesc: "Trade resources with other players and provide extra storage",
   Caravel: "Caravel",
   CaravelBuilder: "Caravel Builder",
   CarFactory: "Car Factory",
   CarlFriedrichGauss: "Carl Friedrich Gauss",
   CarlFriedrichGaussDesc: "+%{idle} Science from Idle Workers. +%{busy} Science from Busy Workers",
   CarlSagan: "Carl Sagan",
   Census: "Census",
   CentrePompidou: "Centre Pompidou",
   CentrePompidouDesc:
      "Once constructed, all buildings get +1 Production and +2 Storage Multiplier. The wonder will persist if the current run reaches Information Age and the next run is a different civilization. The wonder gets +1 level at rebirth for each run that reaches Information Age with a unique civilization. Each level provides +1 Production and +2 Storage Multiplier. The value of this wonder is excluded from total empire value and British Museum cannot transform into this wonder",
   CentrePompidouWarningHTML: "Centre Pompidou will disappear if you rebirth as <b>%{civ}</b>",
   CerneAbbasGiant: "Cerne Abbas Giant",
   CerneAbbasGiantDesc: "A great person of the current age is born when a wonder is constructed",
   ChangePlayerHandle: "Change",
   ChangePlayerHandleCancel: "Cancel",
   ChangePlayerHandledDesc: "Choose a unique player handle 5 ~ 16 characters long. Your player handle may only contain letters and numbers",
   Chariot: "Chariot",
   ChariotWorkshop: "Chariot Workshop",
   Charlemagne: "Charlemagne",
   CharlesDarwin: "Charles Darwin",
   CharlesDarwinDesc: "+%{value} Science From Busy Workers",
   CharlesMartinHall: "Charles Martin Hall",
   CharlesParsons: "Charles Parsons",
   CharlieChaplin: "Charlie Chaplin",
   CharlieChaplinDesc: "+%{value} Happiness",
   Chat: "Chat",
   ChatChannel: "Chat Channel",
   ChatChannelLanguage: "Language",
   ChatHideLatestMessage: "Hide Latest Message Content",
   ChatNoMessage: "No Chat Messages",
   ChatReconnect: "Disconnected, reconnecting...",
   ChatSend: "Send",
   CheckInAndExit: "Check In And Exit",
   CheckInCloudSave: "Check In Save",
   CheckOutCloudSave: "Check Out Save",
   Cheese: "Cheese",
   CheeseMaker: "Cheese Maker",
   Chemistry: "Chemistry",
   ChesterWNimitz: "Chester W. Nimitz",
   ChichenItza: "Chichen Itza",
   ChichenItzaDesc: "All adjacent buildings get +1 Production, Worker Capacity and Storage Multiplier",
   Chinese: "Chinese",
   ChoghaZanbil: "Chogha Zanbil",
   ChoghaZanbilDescV2: "Choose an empire tradition, unlock more boost with each choice",
   ChooseGreatPersonChoicesLeft: "You have %{count} choices left",
   ChristianityLevelX: "Christianity %{level}",
   Church: "Church",
   CircusMaximus: "Circus Maximus",
   CircusMaximusDescV2: "+5 Happiness. All Musician's Guilds, Writer's Guilds and Painter's Guilds get +1 Production and Storage Multiplier",
   CityState: "City State",
   CityViewMap: "City",
   CivGPT: "CivGPT",
   CivIdle: "CivIdle",
   CivIdleInfo: "Proudly presented by Fish Pond Studio",
   Civilization: "Civilization",
   CivilService: "Civil Service",
   CivOasis: "CivOasis",
   CivTok: "CivTok",
   ClaimedGreatPeople: "Claimed Great People",
   ClaimedGreatPeopleTooltip: "You have %{total} great people at rebirth, %{claimed} of them are already claimed",
   ClassicalAge: "Classical Age",
   ClearAfterUpdate: "Clear All Trades After Market Update",
   ClearSelected: "Clear Selected",
   ClearSelection: "Clear",
   ClearTransportPlanCache: "Clear Transport Plan Cache",
   Cleopatra: "Cleopatra",
   CloneFactory: "Clone Factory",
   CloneFactoryDesc: "Clone any resources",
   CloneFactoryInputDescHTML: "Clone Factory can only clone <b>%{res}</b> directly transported from <b>%{buildings}</b>",
   CloneLab: "Clone Lab",
   CloneLabDesc: "Convert any resources into Science",
   CloneLabScienceMultiplierHTML: "Production multipliers that <b>only apply to science production buildings</b> (e.g. production multipliers from Atomium) <b>do not apply</b> to Clone Lab",
   Cloth: "Cloth",
   CloudComputing: "Cloud Computing",
   CloudSaveRefresh: "Refresh",
   CloudSaveReturnToGame: "Return To Game",
   CNTower: "CN Tower",
   CNTowerDesc: "All movie studios, radio stations and TV stations are exempt from -1 Happiness. All buildings unlocked in World Wars and Cold War get +N Production, Worker Capacity and Storage Multiplier. N = Difference between the tier and the age of the building",
   Coal: "Coal",
   CoalMine: "Coal Mine",
   CoalPowerPlant: "Coal Power Plant",
   Coin: "Coin",
   CoinMint: "Coin Mint",
   ColdWarAge: "Cold War",
   CologneCathedral: "Cologne Cathedral",
   CologneCathedralDesc:
      "When constructed, generate one-time science equivalent to the cost of the most expensive technology in the current age. All buildings that produce science (excluding Clone Lab) get +1 Production Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Production Multiplier to all buildings that produce science (excluding Clone Lab)",
   Colonialism: "Colonialism",
   Colosseum: "Colosseum",
   ColosseumDescV2: "Chariot Workshops are exempt from -1 happiness. Consumes 10 chariots and produce 10 happiness. Each unlocked age gives 2 extra happiness",
   ColossusOfRhodes: "Colossus of Rhodes",
   ColossusOfRhodesDesc: "All adjacent buildings that do not produce workers get +1 Happiness",
   Combustion: "Combustion",
   Commerce4UpgradeHTMLV2: "When unlocked, all <b>adjacent banks</b> get free upgrade to <b>level 30</b>",
   CommerceLevelX: "Commerce %{level}",
   Communism: "Communism",
   CommunismLevel4DescHTML: "A great person of <b>Industrial Age</b> and a great person of <b>World Wars Age</b> are born",
   CommunismLevel5DescHTML: "A great person of <b>Cold War Age</b> is born. When entering a new age, get <b>2 additional</b> great people of that age",
   CommunismLevelX: "Communism Level %{level}",
   Computer: "Computer",
   ComputerFactory: "Computer Factory",
   ComputerLab: "Computer Lab",
   Concrete: "Concrete",
   ConcretePlant: "Concrete Plant",
   Condo: "Condo",
   ConfirmDestroyResourceContent: "You are about to destroy %{amount} %{resource}. This cannot be undone",
   ConfirmNo: "No",
   ConfirmYes: "Yes",
   Confucius: "Confucius",
   ConfuciusDescV2: "+%{value} Science from All Workers if more than 50% of workers are busy and less than 50% of busy workers work in transportation",
   ConnectToADevice: "Connect To A Device",
   Conservatism: "Conservatism",
   ConservatismLevelX: "Conservatism Level %{level}",
   Constitution: "Constitution",
   Construction: "Construction",
   ConstructionBuilderBaseCapacity: "Base Capacity",
   ConstructionBuilderCapacity: "Builder Capacity",
   ConstructionBuilderMultiplier: "Capacity Multiplier",
   ConstructionBuilderMultiplierFull: "Builder Capacity Multiplier",
   ConstructionCost: "Construction Cost: %{cost}",
   ConstructionDelivered: "Delivered",
   ConstructionPriority: "Construction Priority",
   ConstructionProgress: "Progress",
   ConstructionResource: "Resource",
   Consume: "Consume",
   ConsumeResource: "Consume: %{resource}",
   ConsumptionMultiplier: "Consumption Multiplier",
   ContentInDevelopment: "Content In Development",
   ContentInDevelopmentDesc: "This game content is still in development and will be available in a future game update, stay tuned!",
   Copper: "Copper",
   CopperMiningCamp: "Copper Mining Camp",
   CosimoDeMedici: "Cosimo de' Medici",
   Cotton: "Cotton",
   CottonMill: "Cotton Mill",
   CottonPlantation: "Cotton Plantation",
   Counting: "Counting",
   Courthouse: "Courthouse",
   CristoRedentor: "Cristo Redentor",
   CristoRedentorDesc: "All buildings within 2 tile range are exempt from -1 Happiness",
   CrossPlatformAccount: "Platform Account",
   CrossPlatformConnect: "Connect",
   CrossPlatformSave: "Cross Platform Save",
   CrossPlatformSaveLastCheckIn: "Last Check In",
   CrossPlatformSaveStatus: "Current Status",
   CrossPlatformSaveStatusCheckedIn: "Checked In",
   CrossPlatformSaveStatusCheckedOut: "Checked Out on %{platform}",
   CrossPlatformSaveStatusCheckedOutTooltip: "Your cross platform save has been checked out on another platform, you have to check in on that platform before you can check out on this platform",
   Cultivation4UpgradeHTML: "A great person of <b>Renaissance Age</b> is born",
   CultivationLevelX: "Cultivation %{level}",
   Culture: "Culture",
   Culus: "Cülus: Double the effect of Cappadocia. Mount Ararat's effect becomes based on square root of Effective Great People Level, instead of cubic root",
   CurrentLanguage: "日本語",
   CurrentPlatform: "Current Platform",
   CursorBigOldFashioned: "3D (Big)",
   CursorOldFashioned: "3D",
   CursorStyle: "Cursor Style",
   CursorStyleDescHTML: "Change the style of the cursor. <b>Require restarting your game to take effect</b>",
   CursorSystem: "System",
   Cycle: "Cycle",
   CyrusII: "Cyrus II",
   DairyFarm: "Dairy Farm",
   DefaultBuildingLevel: "Default Building Level",
   DefaultConstructionPriority: "Default Construction Priority",
   DefaultProductionPriority: "Default Production Priority",
   DefaultStockpileMax: "Default Max Stockpile",
   DefaultStockpileSettings: "Default Stockpile Input Capacity",
   DeficitResources: "Deficit Resources",
   Democracy: "Democracy",
   DemolishAllBuilding: "Demolish All %{building} Within %{tile} Tile",
   DemolishAllBuildingConfirmContent: "Are you sure about demolishing %{count} %{name}?",
   DemolishAllBuildingConfirmTitle: "Demolish %{count} Building(s)?",
   DemolishBuilding: "Demolish Building",
   DennisRitchie: "Dennis Ritchie",
   Deposit: "Deposit",
   DepositTileCountDesc: "%{count} tile(s) of %{deposit} can be found when playing as %{city}",
   Dido: "Dido",
   Diplomacy: "Diplomacy",
   DistanceInfinity: "Unlimited",
   DistanceInTiles: "Distance (In Tiles)",
   DolmabahcePalace: "Dolmabahçe Palace",
   Drilling: "Drilling",
   DukeOfZhou: "Duke of Zhou",
   DuneOfPilat: "Dune of Pilat",
   DuneOfPilatDesc: "In each age, double the age wisdom for the previous age",
   DynamicMultiplierTooltip: "This multiplier is dynamic - it will not affect workers and storage",
   Dynamite: "Dynamite",
   DynamiteWorkshop: "Dynamite Workshop",
   DysonSphere: "Dyson Sphere",
   DysonSphereDesc: "All buildings get +5 Production Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Production Multiplier to all buildings",
   EasterBunny: "Easter Bunny",
   EasterBunnyDesc: "Once constructed, 10% of the extra Great People at Rebirth from this run will carry forward to the next run and are born after building the Easter Bunny in the new run. This wonder can only be constructed during April",
   EastIndiaCompany: "East India Company",
   EastIndiaCompanyDescV2:
      "This wonder accumulates the total value of your completed player trade transactions. For every 2,000 trade value, all buildings adjacent to caravansaries get a +0.5 Production Multiplier for 1 cycle. This wonder can be upgraded and each upgrade provides an additional +0.5 Production Multiplier. A trade transaction counts when you either fulfill another player's trade request or when your own trade request is fulfilled. Multiple boosts stack by extending the duration",
   Education: "Education",
   EffectiveGreatPeopleLevel: "Effective Great People Level",
   EffectiveGreatPeopleLevelDesc: "Effective great people level is the sum of all permanent great people level and age wisdom level. It measures the effect boost provided by great people and age wisdom",
   Egyptian: "Egyptian",
   EiffelTower: "Eiffel Tower",
   EiffelTowerDesc: "All adjacent steel mills get +N Production, Worker Capacity and Storage Multiplier. N = Number of steel mills adjacent to the Eiffel Tower",
   Elbphilharmonie: "Elbphilharmonie",
   ElbphilharmonieDesc: "All buildings within 3 tile range get +1 Production Multiplier for each adjacent working building that has different tier",
   Electricity: "Electricity",
   Electrification: "Electrification",
   ElectrificationPowerRequired: "Power Required",
   ElectrificationStatusActive: "Active",
   ElectrificationStatusDesc: "Both buildings that require power and buildings that do not require power can be electrified. However, buildings that require power provides higher electrification efficiency",
   ElectrificationStatusNoPowerV2: "Not Enough Power",
   ElectrificationStatusNotActive: "Not Active",
   ElectrificationStatusV2: "Electrification Status",
   ElectrificationUpgrade: "Unlock electrification. Allow buildings to consume power to boost production",
   Electrolysis: "Electrolysis",
   ElvisPresley: "Elvis Presley",
   ElyseePalace: "Élysée Palace",
   EmailDeveloper: "開発者にメールを送る",
   Embassy: "Embassy",
   EmperorWuOfHan: "Emperor Wu of Han",
   EmpireValue: "Empire Value",
   EmpireValueByHour: "Empire Value By Hour",
   EmpireValueFromBuilding: "Empire Value from Building",
   EmpireValueFromBuildingsStat: "From Buildings",
   EmpireValueFromResources: "Empire Value from Resources",
   EmpireValueFromResourcesStat: "From Resources",
   EmpireValueIncrease: "Empire Value Increase",
   EmptyTilePageBuildLastBuilding: "Build Last Building",
   EndConstruction: "建設をやめる",
   EndConstructionDescHTML: "建設をやめた場合、既に使用されている資源は<b>戻ってきません。</b>",
   Engine: "Engine",
   Engineering: "Engineering",
   English: "English",
   Enlightenment: "Enlightenment",
   Enrichment: "Enrichment",
   EnricoFermi: "Enrico Fermi",
   EstimatedTimeLeft: "Estimated Time Left",
   EuphratesRiver: "Euphrates River",
   EuphratesRiverDesc:
      "Every 10% of busy workers that in production (not transporting) provides +1 Production Multiplier to all buildings that do not produce workers (max = number of unlocked ages / 2). When the Hanging Garden is built next to it, the Hanging Garden gets +1 effect for each age after the Hanging Garden is unlocked. When discovered, spawn water on all adjacent tiles that do not have deposits",
   ExpansionLevelX: "Expansion %{level}",
   Exploration: "Exploration",
   Explorer: "Explorer",
   ExplorerRangeUpgradeDesc: "Increase the explorer's range to %{range}",
   ExploreThisTile: "Send An Explorer",
   ExploreThisTileHTML: "An explorer will explore <b>this tile and its adjacent tiles</b>. Explorers are generated in %{name}. You have %{count} explorers left",
   ExtraGreatPeople: "%{count} Extra Great People",
   ExtraGreatPeopleAtReborn: "Extra Great People At Rebirth",
   ExtraTileInfoType: "Extra Tile Info",
   ExtraTileInfoTypeDesc: "Choose what information is shown below each tile",
   ExtraTileInfoTypeEmpireValue: "Empire Value",
   ExtraTileInfoTypeNone: "None",
   ExtraTileInfoTypeStoragePercentage: "Storage Percentage",
   Faith: "Faith",
   Farming: "Farming",
   FavoriteBuildingAdd: "Add To Favorite",
   FavoriteBuildingEmptyToast: "You don't have any favorite buildings",
   FavoriteBuildingRemove: "Remove From Favorite",
   FeatureRequireQuaestorOrAbove: "This feature requires Quaestor rank or above",
   Festival: "Festival",
   FestivalCycle: "Festival Cycle",
   FestivalTechTooltipV2: "Positive Happiness (max. 50) is converted into festival points. For every %{point} festival points, your empire enters a festival cycle, granting a significant map-specific boost. The festival on this map is %{desc}",
   FestivalTechV2: "Unlock festival - positive Happiness (max. 50) is converted into festival points. For every %{point} festival points, your empire enters a festival cycle, granting a significant map-specific boost",
   Feudalism: "Feudalism",
   Fibonacci: "Fibonacci",
   FibonacciDescV2: "+%{idle} Science from Idle Workers. +%{busy} Science from Busy Workers. Fibonacci's permanent upgrade cost follows Fibonacci sequence",
   FighterJet: "Fighter Jet",
   FighterJetPlant: "Fighter Jet Plant",
   FilterByAge: "Filter by Age",
   FinancialArbitrage: "Financial Arbitrage",
   FinancialLeverage: "Financial Leverage",
   Fire: "Fire",
   Firearm: "Firearm",
   FirstTimeGuideNext: "次へ",
   FirstTimeTutorialWelcome: "CivIdleへようこそ",
   FirstTimeTutorialWelcome1HTML:
      "CivIdleへようこそ。このゲームでは、<b>生産の管理、技術の解放、他のプレイヤーとの資源の取引、偉人の輩出、そして世界遺産の建設...</b>あなただけの帝国を築くことができます。<br><br>マウスをドラッグして周囲を見回せます。マウスホイールでズームイン・アウトが可能です。空のタイルをクリックして新しい建物を建設してください。建物をクリックすると詳細を確認できます。<br><br>Stone QuarryやLogging Campのような建物は資源タイルの上に建設する必要があります。最初は労働者を生産するHutを霧の横に置くことをお勧めします。建設には少し時間がかかります。完成したら、近くの霧が晴れるはずです。",
   FirstTimeTutorialWelcome2HTML:
      "建物は、資源と時間を費やすことでアップグレードできます。アップグレードしている間は<b>何も生産されません。</b>労働者を生産する建物も対象です。<b>絶対に同時にアップグレードしないでください！</b><br><br>帝国が育ってくると、大量の研究ポイントを手に入れ、新しい技術を解放することができるでしょう。そのような状況になった時、改めてチュートリアルとして説明されますが、「表示」->「研究」で覗いてみることが出来ます。<br><br>",
   FirstTimeTutorialWelcome3HTML:
      "帝国を築く準備が出来ました。既にこのゲームの基本は理解できたはずです！帝国を築く前に、<b>あなたのプレイヤー名</b>を決める必要があります。その後、ゲーム内チャットであいさつしてみましょう。とても協力的なコミュニティが、迷ったときに助けてくれます。遠慮せずに尋ねてみましょう！<br>(訳注:このチュートリアルを見返したい時は、「ヘルプ」->「CivIdleへようこそ」を選んでください。また、日本語への翻訳はプレイヤーの一人がボランティアでやっています。まだまだ未完ですが、ゲームのアップデートとともに徐々に反映予定です。)",
   Fish: "Fish",
   FishPond: "Fish Pond",
   FlorenceNightingale: "Florence Nightingale",
   FlorenceNightingaleDesc: "+%{value} Happiness",
   Flour: "Flour",
   FlourMill: "Flour Mill",
   FontSizeScale: "Font Size Scale",
   FontSizeScaleDescHTML: "Change the font size scale of the game's UI. <b>Setting the scale greater than 1x might break some UI layouts</b>",
   ForbiddenCity: "Forbidden City",
   ForbiddenCityDesc: "All Paper Makers, Writer's Guilds and Printing Houses get +1 Production, Worker Capacity and Storage Multiplier",
   Forex: "Forex",
   ForexMarket: "Forex Market",
   FrankLloydWright: "Frank Lloyd Wright",
   FrankLloydWrightDesc: "+%{value} Builder Capacity Multiplier",
   FrankWhittle: "Frank Whittle",
   FreeThisWeek: "Free This Week",
   FreeThisWeekDescHTMLV2: "<b>Every week</b>, one of the premium civilizations is free to play. This week's free civilization is <b>%{city}</b>",
   French: "French",
   Frigate: "Frigate",
   FrigateBuilder: "Frigate Builder",
   Furniture: "Furniture",
   FurnitureWorkshop: "Furniture Workshop",
   Future: "Future",
   GabrielGarciaMarquez: "Gabriel García Márquez",
   GabrielGarciaMarquezDesc: "+%{value} Happiness",
   GalileoGalilei: "Galileo Galilei",
   GalileoGalileiDesc: "+%{value} Science From Idle Workers",
   Galleon: "Galleon",
   GalleonBuilder: "Galleon Builder",
   Gameplay: "ゲームプレイ",
   Garment: "Garment",
   GarmentWorkshop: "Garment Workshop",
   GasPipeline: "Gas Pipeline",
   GasPowerPlant: "Gas Power Plant",
   GatlingGun: "Gatling Gun",
   GatlingGunFactory: "Gatling Gun Factory",
   Genetics: "Genetics",
   Geography: "Geography",
   GeorgeCMarshall: "George C. Marshall",
   GeorgeWashington: "George Washington",
   GeorgiusAgricola: "Georgius Agricola",
   German: "German",
   Glass: "Glass",
   Glassworks: "Glassworks",
   GlobalBuildingDefault: "Global Building Default",
   Globalization: "Globalization",
   GoBack: "戻る",
   Gold: "Gold",
   GoldenGateBridge: "Golden Gate Bridge",
   GoldenGateBridgeDesc: "All power plants get +1 Production Multiplier. Provide power to all tiles within 2 tile range",
   GoldenPavilion: "Golden Pavilion",
   GoldenPavilionDesc: "All buildings within 3 tile range get +1 Production Multiplier for each adjacent building that produces any of its consumed resources (excluding Clone Lab and Clone Factory and the building cannot be turned off)",
   GoldMiningCamp: "Gold Mining Camp",
   GordonMoore: "Gordon Moore",
   GrandBazaar: "Grand Bazaar",
   GrandBazaarDesc: "Manage all your markets. Adjacent caravansaries get +5 Production and Storage Multiplier. Adjacent markets get different trades",
   GrandBazaarFilters: "Filters",
   GrandBazaarFilterWarningHTML: "You must select a filter before any market trades are shown",
   GrandBazaarFilterYouGet: "得るもの",
   GrandBazaarFilterYouPay: "渡すもの",
   GrandBazaarSeach: "Search",
   GrandBazaarSearchGet: "Get",
   GrandBazaarSearchPay: "Pay",
   GrandBazaarTabActive: "Active",
   GrandBazaarTabTrades: "Trades",
   GrandCanyon: "Grand Canyon",
   GrandCanyonDesc: "Buildings unlocked in the current age get +2 Production Multiplier. Double the effect of J.P. Morgan",
   GraphicsDriver: "グラフィックドライバ: %{driver}",
   GreatDagonPagoda: "Great Dagon Pagoda",
   GreatDagonPagodaDescV2: "All pagodas are exempt from -1 happiness. Generate science based on faith production of all pagodas",
   GreatMosqueOfSamarra: "Great Mosque of Samarra",
   GreatMosqueOfSamarraDescV2: "+1 building vision range. Reveal 5 random unexplored deposit tiles and build a level 10 resource extraction building on each",
   GreatPeople: "偉人",
   GreatPeopleEffect: "効果",
   GreatPeopleFilter: "偉人の名前や時代で絞りこむ...",
   GreatPeopleName: "名前",
   GreatPeoplePermanentColumn: "Permanent",
   GreatPeoplePermanentShort: "Permanent",
   GreatPeoplePickPerRoll: "Great People Pick Per Roll",
   GreatPeopleThisRun: "Great People From This Run",
   GreatPeopleThisRunColumn: "This Run",
   GreatPeopleThisRunShort: "This Run",
   GreatPersonLevelRequired: "Permanent Great People Level Required",
   GreatPersonLevelRequiredDescV2: "%{city} civilization requires %{required} permanent great people levels. You currently have %{current}",
   GreatPersonPromotionPromote: "Promote",
   GreatPersonThisRunEffectiveLevel: "You currently have %{count} %{person} from this run. An additional %{person} will have 1/%{effect} of the effect",
   GreatPersonWildCardBirth: "Birth",
   GreatSphinx: "Great Sphinx",
   GreatSphinxDesc: "All Tier II or above buildings within 2 tiles get +N Consumption, Production Multiplier. N = Number of its adjacent buildings of the same type",
   GreatWall: "Great Wall",
   GreatWallDesc: "All buildings within 1 tile range get +N Production, Worker Capacity and Storage Multiplier. N = the number of the different ages between the current age and the age where the building is first unlocked. When constructed next to Forbidden City, the range increases to 2 tile",
   GreedyTransport: "Construction/Upgrade Greedy Transport",
   GreedyTransportDescHTML: "This will make buildings keep transporting resources even if it has enough resources for the current upgrade, which can make upgrading multiple levels <b>faster</b> but end up transport <b>more resources than needed</b>",
   Greek: "Greek",
   GrottaAzzurra: "Grotta Azzurra",
   GrottaAzzurraDescV2: "When discovered, all your Tier I buildings get +5 Level and +1 Production, Worker Capacity and Storage Multiplier",
   Gunpowder: "Gunpowder",
   GunpowderMill: "Gunpowder Mill",
   GuyFawkesNightV2: "Guy Fawkes Night: East India Company provides double the Production Multiplier to buildings adjacent to caravansaries. Tower Bridge generates great people 20% faster",
   HagiaSophia: "Hagia Sophia",
   HagiaSophiaDescV2: "+5 Happiness. Buildings with 0% Production Capacity are exempt from -1 happiness. During the game bootstrap, provide extra happiness to avoid production halt",
   HallOfFame: "名誉の殿堂",
   HallOfSupremeHarmony: "Hall of Supreme Harmony",
   Hammurabi: "Hammurabi",
   HangingGarden: "Hanging Garden",
   HangingGardenDesc: "+1 Builder Capacity Multiplier. Adjacent aqueducts get +1 Production, Worker Capacity and Storage Multiplier",
   Happiness: "幸福度",
   HappinessFromBuilding: "建物によって(遺産を除く)",
   HappinessFromBuildingTypes: "From Well-Stocked Building Types",
   HappinessFromHighestTierBuilding: "From Highest Tier Working Building",
   HappinessFromUnlockedAge: "到達した時代によって",
   HappinessFromUnlockedTech: "解放した技術によって",
   HappinessFromWonders: "From Wonders (incl. Natural)",
   HappinessUncapped: "Happiness (Uncapped)",
   HarryMarkowitz: "Harry Markowitz",
   HarunAlRashid: "Harun al-Rashid",
   Hatshepsut: "Hatshepsut",
   HatshepsutTemple: "Hatshepsut Temple",
   HatshepsutTempleDesc: "Reveals all water tiles on the map. Wheat Farms get +1 Production Multiplier for each water tile adjacent to the farm",
   Headquarter: "Headquarter",
   HedgeFund: "Hedge Fund",
   HelpMenu: "ヘルプ",
   HenryFord: "Henry Ford",
   Herding: "Herding",
   Herodotus: "Herodotus",
   HighlightBuilding: "Highlight %{building}",
   HimejiCastle: "Himeji Castle",
   HimejiCastleDesc: "All Caravel Builders, Galleon Builders, and Frigate Builders get +1 Production, Worker Capacity and Storage Multiplier",
   Hollywood: "Hollywood",
   HollywoodDesc: "+5 Happiness. +1 Happiness for each well-stocked building that consumes or produces culture within 2 tile range",
   HolyEmpire: "Holy Empire",
   Homer: "Homer",
   Honor4UpgradeHTML: "Double the effect of <b>Zheng He</b> (Great Person)",
   HonorLevelX: "Honor %{level}",
   Horse: "馬",
   HorsebackRiding: "Horseback Riding",
   House: "家屋",
   Housing: "Housing",
   Hut: "小屋",
   HydroDam: "Hydro Dam",
   Hydroelectricity: "Hydroelectricity",
   HymanGRickover: "Hyman G. Rickover",
   IdeologyDescHTML: "Choose from <b>Liberalism, Conservatism, Socialism or Communism</b> as your empire ideology. You <b>cannot switch ideology</b> after it is chosen. You can unlock more boost within each ideology",
   IMPei: "I. M. Pei",
   IMPeiDesc: "+%{value} Builder Capacity Multiplier",
   Imperialism: "Imperialism",
   ImperialPalace: "Imperial Palace",
   IndustrialAge: "Industrial",
   InformationAge: "Information Age",
   InputResourceForCloning: "Input Resource For Cloning",
   InternationalSpaceStation: "International Space Station",
   InternationalSpaceStationDesc: "All buildings get +5 Storage Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Storage Multiplier to all buildings",
   Internet: "Internet",
   InternetServiceProvider: "Internet Service Provider",
   InverseSelection: "Inverse",
   Iron: "鉄",
   IronAge: "Iron Age",
   Ironclad: "Ironclad",
   IroncladBuilder: "Ironclad Builder",
   IronForge: "Iron Forge",
   IronMiningCamp: "Iron Mining Camp",
   IronTech: "Iron",
   IsaacNewton: "Isaac Newton",
   IsaacNewtonDescV2: "+%{value} Science from All Workers if more than 50% of workers are busy and less than 50% of busy workers work in transportation",
   IsambardKingdomBrunel: "Isambard Kingdom Brunel",
   IsidoreOfMiletus: "Isidore of Miletus",
   IsidoreOfMiletusDesc: "+%{value} Builder Capacity Multiplier",
   Islam5UpgradeHTML: "When unlocked, generate one-time science equivalent to the cost of the most expensive <b>Industrial</b> technology",
   IslamLevelX: "Islam %{level}",
   ItsukushimaShrine: "Itsukushima Shrine",
   ItsukushimaShrineDescV2: "When all technologies within an age are unlocked, generate one-time science equivalent to the cost of the cheapest technology in the next age",
   JamesWatson: "James Watson",
   JamesWatsonDesc: "+%{value} Science From Busy Workers",
   JamesWatt: "James Watt",
   Japanese: "Japanese",
   JetPropulsion: "Jet Propulsion",
   JohannesGutenberg: "Johannes Gutenberg",
   JohannesKepler: "Johannes Kepler",
   JohnCarmack: "John Carmack",
   JohnDRockefeller: "John D. Rockefeller",
   JohnMcCarthy: "John McCarthy",
   JohnVonNeumann: "John von Neumann",
   JohnVonNeumannDesc: "+%{value} Science From Busy Workers",
   JoinDiscord: "Discordに参加",
   JosephPulitzer: "Joseph Pulitzer",
   Journalism: "Journalism",
   JPMorgan: "J.P. Morgan",
   JRobertOppenheimer: "J. Robert Oppenheimer",
   JuliusCaesar: "Julius Caesar",
   Justinian: "Justinian",
   Kanagawa: "Kanagawa",
   KanagawaDesc: "All great people of the current age get an additional level for this run (excluding Zenobia)",
   KarlMarx: "Karl Marx",
   Knight: "Knight",
   KnightCamp: "Knight Camp",
   Koti: "Koti",
   KotiInStorage: "Koti In Storage",
   KotiProduction: "Koti Production",
   LandTrade: "Land Trade",
   Language: "言語",
   Lapland: "Lapland",
   LaplandDesc: "When discovered, reveal the whole map. All buildings within 2-tile range get +N Production Multiplier, N = number of unlocked ages. This natural wonder can only be discovered in December",
   LargeHadronCollider: "Large Hadron Collider",
   LargeHadronColliderDescV2: "All Information Age great people get +2 level for this run. This wonder can be upgraded and each additional upgrade provides +1 level to all Information Age great people for this run",
   Law: "Law",
   Lens: "Lens",
   LensWorkshop: "Lens Workshop",
   LeonardoDaVinci: "Leonardo da Vinci",
   Level: "レベル",
   LevelX: "レベル %{level}",
   Liberalism: "Liberalism",
   LiberalismLevel3DescHTML: "Free transport <b>from</b> and <b>to</b> warehouses",
   LiberalismLevel5DescHTML: "<b>Double</b> the electrification effect",
   LiberalismLevelX: "Liberalism Level %{level}",
   Library: "Library",
   LighthouseOfAlexandria: "Lighthouse of Alexandria",
   LighthouseOfAlexandriaDesc: "All adjacent buildings get +5 Storage Multiplier",
   LinusPauling: "Linus Pauling",
   LinusPaulingDesc: "+%{value} Science From Idle Workers",
   Literature: "Literature",
   LiveData: "Live Value",
   LocomotiveFactory: "Locomotive Factory",
   Logging: "Logging",
   LoggingCamp: "Logging Camp",
   LouisSullivan: "Louis Sullivan",
   LouisSullivanDesc: "+%{value} Builder Capacity Multiplier",
   Louvre: "Louvre",
   LouvreDesc: "For every 10 Extra Great People at Rebirth, one great person from all unlocked ages is born",
   Lumber: "Lumber",
   LumberMill: "Lumber Mill",
   LunarNewYear: "Lunar New Year: Great Wall provides double the boost to buildings. Porcelain Tower provides +1 level to all great people from this run",
   LuxorTemple: "Luxor Temple",
   LuxorTempleDescV2: "+1 Science From Busy Workers. Choose an empire religion, unlock more boost with each choice",
   Machinery: "Machinery",
   Magazine: "Magazine",
   MagazinePublisher: "Magazine Publisher",
   Maglev: "Maglev",
   MaglevFactory: "Maglev Factory",
   MahatmaGandhi: "Mahatma Gandhi",
   ManageAgeWisdom: "Manage Age Wisdom",
   ManagedImport: "Managed Import",
   ManagedImportDescV2: "This building will automatically import resources produced within %{range} tile range. Resource transports for this building cannot be manually changed. Max transport distance will be ignored",
   ManageGreatPeople: "Manage Great People",
   ManagePermanentGreatPeople: "Manage Permanent Great People",
   ManageSave: "Manage Save",
   ManageWonders: "Manage Wonders",
   Manhattan: "Manhattan",
   ManhattanProject: "Manhattan Project",
   ManhattanProjectDesc: "All uranium mines get +2 Production, Worker Capacity and Storage Multiplier. Uranium enrichment plants and atomic facilities get +1 Production Multiplier for each adjacent uranium mine that is built on top of a uranium deposit",
   Marble: "Marble",
   Marbleworks: "Marbleworks",
   MarcoPolo: "Marco Polo",
   MarieCurie: "Marie Curie",
   MarinaBaySands: "Marina Bay Sands",
   MarinaBaySandsDesc: "All buildings get +5 Worker Capacity Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Worker Capacity Multiplier to all buildings",
   Market: "Market",
   MarketDesc: "Exchange a resource to another, available resources update every hour",
   MarketRefreshMessage: "Trades in %{count} markets has been refreshed",
   MarketSell: "売却",
   MarketSettings: "マーケットの設定",
   MarketValueDesc: "%{value} compared to average price",
   MarketYouGet: "得るもの",
   MarketYouPay: "渡すもの",
   MartinLuther: "Martin Luther",
   MaryamMirzakhani: "Maryam Mirzakhani",
   MaryamMirzakhaniDesc: "+%{value} Science From Idle Workers",
   Masonry: "Masonry",
   MatrioshkaBrain: "Matrioshka Brain",
   MatrioshkaBrainDescV2: "Allow Science to be counted when calculating empire value (5 Science = 1 Empire Value). +5 Science Per Busy and Idle Worker. This wonder can be upgraded and each additional upgrade provides +1 Science Per Busy and Idle Worker and +1 Production Multiplier for buildings that produce Science",
   MausoleumAtHalicarnassus: "Mausoleum at Halicarnassus",
   MausoleumAtHalicarnassusDescV2: "Transports from or to buildings within 2 tile range do not cost workers",
   MaxExplorers: "Max Explorers",
   MaxTransportDistance: "Max Transport Distance",
   Meat: "Meat",
   Metallurgy: "Metallurgy",
   Michelangelo: "Michelangelo",
   MiddleAge: "Middle Age",
   MilitaryTactics: "Military Tactics",
   Milk: "Milk",
   Moai: "Moai",
   MoaiDesc: "Moai",
   MobileOverride: "Mobile Override",
   MogaoCaves: "Mogao Caves",
   MogaoCavesDescV3: "+1 happiness for every 10% of busy workers. All adjacent buildings that produce faith are exempt from -1 happiness",
   MonetarySystem: "Monetary System",
   MontSaintMichel: "Mont Saint-Michel",
   MontSaintMichelDesc: "Generate Culture from Idle Workers. Provide +1 Storage Multiplier to all buildings within 2-tile range. This wonder can be upgraded using the generated Culture and each level provides addtional +1 Storage Multiplier",
   Mosque: "Mosque",
   MotionPicture: "Motion Picture",
   MountArarat: "Mount Ararat",
   MountAraratDesc: "All buildings within 2 tile range get +X Production, Worker Capacity and Storage Multiplier. X = cubic root of Effective Great People Level",
   MountFuji: "Mount Fuji",
   MountFujiDescV2: "When Petra is built next to it, Petra gets +8h Warp storage. When the game is running, generate 20 warp every minute in Petra (not accelerated by Petra itself, not generating when the game is offline)",
   MountSinai: "Mount Sinai",
   MountSinaiDesc: "When discovered, a great person of the current age is born. All buildings that produce faith get +5 Storage Multiplier",
   MountTai: "Mount Tai",
   MountTaiDesc: "All buildings that produce science get +1 Production Multiplier. Double the effect of Confucius (Great Person). When discovered, generate one-time science equivalent to the cost of the most expensive unlocked technology",
   MoveBuilding: "Move Building",
   MoveBuildingFail: "Selected tile is not valid",
   MoveBuildingNoTeleport: "You don't have enough teleport",
   MoveBuildingSelectTile: "Select An Tile...",
   MoveBuildingSelectTileToastHTML: "Select <b>an empty explored tile</b> on the map as the target",
   Movie: "Movie",
   MovieStudio: "Movie Studio",
   Museum: "Museum",
   Music: "Music",
   MusiciansGuild: "Musician's Guild",
   MutualAssuredDestruction: "Mutual Assured Destruction",
   MutualFund: "Mutual Fund",
   Name: "名前",
   Nanotechnology: "Nanotechnology",
   NapoleonBonaparte: "Napoleon Bonaparte",
   NaturalGas: "Natural Gas",
   NaturalGasWell: "Natural Gas Well",
   NaturalWonderName: "Natural Wonder: %{name}",
   NaturalWonders: "Natural Wonders",
   Navigation: "Navigation",
   NebuchadnezzarII: "Nebuchadnezzar II",
   Neuschwanstein: "Neuschwanstein",
   NeuschwansteinDesc: "+10 Builder Capacity Multiplier when constructing wonders",
   Newspaper: "Newspaper",
   NextExplorersIn: "Next Explorers In",
   NextMarketUpdateIn: "Next Market Update In",
   NiagaraFalls: "Niagara Falls",
   NiagaraFallsDescV2: "All warehouses, markets and caravansaries get +N storage multiplier. N = number of unlocked ages. Albert Einstein provides +1 Production Multiplier to Research Fund (not affected by other boosts like Broadway)",
   NielsBohr: "Niels Bohr",
   NielsBohrDescV2: "+%{value} Science from All Workers if more than 50% of workers are busy and less than 50% of busy workers work in transportation",
   NileRiver: "Nile River",
   NileRiverDesc: "Double the effect of Hatshepsut (Great Person). All wheat farms get +1 Production and Storage Multiplier. All adjacent wheat farms get +5 Production and Storage Multiplier",
   NoPowerRequired: "This building does not require power",
   NothingHere: "Nothing here",
   NotProducingBuildings: "Buildings That Are Not Producing",
   NuclearFission: "Nuclear Fission",
   NuclearFuelRod: "Nuclear Fuel Rod",
   NuclearMissile: "Nuclear Missile",
   NuclearMissileSilo: "Nuclear Missile Silo",
   NuclearPowerPlant: "Nuclear Power Plant",
   NuclearReactor: "Nuclear Reactor",
   NuclearSubmarine: "Nuclear Submarine",
   NuclearSubmarineYard: "Nuclear Submarine Yard",
   OdaNobunaga: "Oda Nobunaga",
   OfflineErrorMessage: "You are currently offline, this operation requires an Internet connection",
   OfflineProduction: "オフライン中の生産",
   OfflineProductionTime: "オフライン生産の時間",
   OfflineProductionTimeDescHTML: "オフライン時間のうち<b>最初の %{time} 分は</b>、オフライン生産にするかワープにするかを選べます。どこで切り替わるかは設定可能です。<b>超えている分</b>はワープに変換されます。",
   OfflineTime: "オフライン時間",
   Oil: "Oil",
   OilPress: "Oil Press",
   OilRefinery: "Oil Refinery",
   OilWell: "Oil Well",
   Ok: "OK",
   Oktoberfest: "Oktoberfest: Double the effect of Zugspitze",
   Olive: "Olive",
   OlivePlantation: "Olive Plantation",
   Olympics: "Olympics",
   OnlyAvailableWhenPlaying: "Only available when playing %{city}",
   OpenLogFolder: "Open Log Folder",
   OpenSaveBackupFolder: "Open Backup Folder",
   OpenSaveFolder: "Open Save Folder",
   Opera: "Opera",
   OperationNotAllowedError: "This operation is not allowed",
   Opet: "Opet: Great Sphinx no longer increases Consumption Multiplier",
   OpticalFiber: "Optical Fiber",
   OpticalFiberPlant: "Optical Fiber Factory",
   Optics: "Optics",
   OptionsMenu: "Options",
   OptionsUseModernUIV2: "Use Anti-Aliased Font",
   OsakaCastle: "Osaka Castle",
   OsakaCastleDesc: "Provide power to all tiles within 2 tile range. Allow electrification of science producing buildings (including Clone Lab)",
   OtherPlatform: "Other Platform",
   Ottoman: "Ottoman",
   OttoVonBismarck: "Otto von Bismarck",
   OxfordUniversity: "Oxford University",
   OxfordUniversityDescV3: "+10% science output for buildings that produce science. When completed, generate one-time science equivalent to the cost of the most expensive unlocked technology",
   PabloPicasso: "Pablo Picasso",
   Pagoda: "Pagoda",
   PaintersGuild: "Painter's Guild",
   Painting: "Painting",
   PalmJumeirah: "Palm Jumeirah",
   PalmJumeirahDesc: "+10 Builder Capacity. This wonder can be upgraded and each additional upgrade provides +2 Builder Capacity",
   Pamukkale: "Pamukkale",
   PamukkaleDesc: "When discovered, convert each one of the Permanent Great People Shards (except for Promotion and Wildcard) to the same Great Person From This Run",
   Panathenaea: "Panathenaea: Poseidon provides +1 Production Multiplier to all buildings",
   Pantheon: "Pantheon",
   PantheonDescV2: "All buildings within 2 tile range get +1 Worker Capacity and Storage Multiplier. Generate science based on faith production of all shrines",
   Paper: "Paper",
   PaperMaker: "Paper Maker",
   Parliament: "Parliament",
   Parthenon: "Parthenon",
   ParthenonDescV2: "Two great people of Classical Age are born and you get 4 choices for each. Musician's Guilds and Painter's Guilds get +1 Production, Worker Capacity and Storage Multiplier and are exempt from -1 Happiness",
   Passcode: "Passcode",
   PasscodeToastHTML: "<b>%{code}</b> is your passcode and it's valid for 30 minutes",
   PatchNotes: "更新履歴",
   Peace: "Peace",
   Peacekeeper: "Peacekeeper",
   Penthouse: "Penthouse",
   PercentageOfProductionWorkers: "Percentage of Production Workers",
   Performance: "Performance",
   PermanentGreatPeople: "永久偉人",
   PermanentGreatPeopleAcquired: "取得した永久偉人",
   PermanentGreatPeopleUpgradeUndo: "Undo permanent great people upgrade: this will convert upgraded level back to shards - you will get %{amount} shards",
   Persepolis: "Persepolis",
   PersepolisDesc: "All Copper Mining Camps, Logging Camps and Stone Quarries get +1 Production, Worker Capacity and Storage Multiplier",
   PeterHiggs: "Peter Higgs",
   PeterHiggsDesc: "+%{value} Science From Busy Workers",
   Petra: "Petra",
   PetraDesc: "Generate time warp when you are offline, which you can use to accelerate your empire",
   PetraOfflineTimeReconciliation: "You have been credited %{count} warp after server offline time reconciliation",
   Petrol: "Petrol",
   PhiloFarnsworth: "Philo Farnsworth",
   Philosophy: "Philosophy",
   Physics: "Physics",
   PierreDeCoubertin: "Pierre de Coubertin",
   Pizza: "ピザ",
   Pizzeria: "Pizzeria",
   PlanetaryRover: "Planetary Rover",
   Plastics: "Plastics",
   PlasticsFactory: "Plastics Factory",
   PlatformAndroid: "Android",
   PlatformiOS: "iOS",
   PlatformSteam: "Steam",
   PlatformSyncInstructionHTML: "If you want to sync your progress on this device to a new device, click <b>Sync To A New Device</b> and get a one-time passcode. On your new device, click <b>Connect To A Device</b> and type in the one-time passcode",
   Plato: "Plato",
   PlayerHandle: "プレイヤー名",
   PlayerHandleOffline: "現在オフラインです",
   PlayerMapClaimThisTile: "このタイルを取得する",
   PlayerMapClaimTileCondition2: "アンチチート行為でbanされていない",
   PlayerMapClaimTileCondition3: "%{tech} がアンロックされている",
   PlayerMapClaimTileCondition4: "タイルを取得していないか、タイル移動のクールダウンが明けている",
   PlayerMapClaimTileCooldownLeft: "クールダウンが明けるまで: %{time}",
   PlayerMapClaimTileNoLongerReserved: "このタイルは予約されていません。<b>%{name} </b>を立ち退かせ、タイルを取得することが可能です。",
   PlayerMapEstablishedSince: "取得した日時",
   PlayerMapLastSeenAt: "最終ログイン日時",
   PlayerMapMapTileBonus: "Trade Tile Bonus",
   PlayerMapMenu: "取引",
   PlayerMapOccupyThisTile: "Occupy This Tile",
   PlayerMapOccupyTileCondition1: "This tile is adjacent to your home or occupied tiles",
   PlayerMapPageGoBackToCity: "街に戻る",
   PlayerMapSetYourTariff: "関税の設定",
   PlayerMapTariff: "関税",
   PlayerMapTariffApply: "関税率を設定",
   PlayerMapTariffDesc: "あなたのタイルを通る全ての取引に対して、設定した関税を受け取ることができます。税率を高くするとより多くの関税を受け取ることができますが、あなたのタイルを通る取引は少なくなるでしょう。バランスが大事です。",
   PlayerMapTileAvailableTilePoint: "Available Tile Point",
   PlayerMapTileFromOccupying: "From Owned/Occupied Tiles",
   PlayerMapTileFromOccupyingTooltipHTML: "An owned/occupied tile generates <b>%{point}</b> tile point per hour (up to %{max} days from the first claimed tile)",
   PlayerMapTileFromRank: "From Account Rank",
   PlayerMapTileTilePoint: "Tile Point",
   PlayerMapTileUsedTilePoint: "Used Tile Point",
   PlayerMapTileUsedTilePointTooltipHTML: "You need <b>1 tile point</b> to own/occupy a tile",
   PlayerMapTradesFrom: "%{name} の取引",
   PlayerMapUnclaimedTile: "取得されていないタイル",
   PlayerMapYourTile: "あなたのタイル",
   PlayerTrade: "プレイヤーとの取引",
   PlayerTradeAddSuccess: "取引の追加に成功しました",
   PlayerTradeAddTradeCancel: "キャンセル",
   PlayerTradeAmount: "取引量",
   PlayerTradeCancelDescHTML: "この取引を取り下げると、手数料の<b>%{percent}</b>を除いた<b> %{res} </b>が戻ってきます。また、ストレージの容量を超えている<b> %{discard} </b> が破棄されます。<br><b>本当に取引を取り下げますか？</b>",
   PlayerTradeCancelTrade: "取引を取り下げる",
   PlayerTradeClaim: "受取",
   PlayerTradeClaimAll: "全て受け取る",
   PlayerTradeClaimAllFailedMessageV2: "全ての取引に失敗しました。ストレージがいっぱいかもしれません。",
   PlayerTradeClaimAllMessageV2: "<b>%{resources}</b> を受け取りました",
   PlayerTradeClaimAvailable: "%{count} 個の取引が受け取り可能です",
   PlayerTradeClaimTileFirst: "取引マップでタイルを取得する",
   PlayerTradeClaimTileFirstWarning: "取引マップでタイルを取得した後に、他のプレイヤーと取引することが出来るようになります。",
   PlayerTradeClearAll: "全て消す",
   PlayerTradeClearFilter: "Clear Filters",
   PlayerTradeDisabledBeta: "You can only create player trades once the beta version is released",
   PlayerTradeFill: "埋める",
   PlayerTradeFill50: "Fill 50%",
   PlayerTradeFill95: "Fill 95%",
   PlayerTradeFillAmount: "取引量",
   PlayerTradeFillAmountMaxV2: "最大まで埋める",
   PlayerTradeFillBy: "取引相手",
   PlayerTradeFillPercentage: "取引量の割合",
   PlayerTradeFillSuccessV2: "<b>%{success}/%{total}</b>件の取引に成功しました。<b>%{fillAmount} %{fillResource}</b>を渡し、<b>%{receivedAmount} %{receivedResource}</b>を受け取りました",
   PlayerTradeFillTradeButton: "取引をする",
   PlayerTradeFillTradeTitle: "指定した量取引する",
   PlayerTradeFilters: "絞り込み",
   PlayerTradeFiltersApply: "適用",
   PlayerTradeFiltersClear: "解除",
   PlayerTradeFilterWhatIHave: "Filter By What I Have",
   PlayerTradeFrom: "取引者",
   PlayerTradeIOffer: "提示するもの",
   PlayerTradeIWant: "要求するもの",
   PlayerTradeMaxAll: "全て最大",
   PlayerTradeMaxTradeAmountFilter: "最大数量",
   PlayerTradeMaxTradeExceeded: "現在のアカウントランクで許容される最大取引数を超過しました",
   PlayerTradeNewTrade: "新しい取引",
   PlayerTradeNoFillBecauseOfResources: "資源が不足しているため、取引は成立しませんでした",
   PlayerTradeNoValidRoute: "あなたと%{name}との間に有効な取引ルートがありません。",
   PlayerTradeOffer: "提示物",
   PlayerTradePlaceTrade: "取引を追加する",
   PlayerTradePlayerNameFilter: "プレイヤー名",
   PlayerTradeResource: "資源",
   PlayerTradeStorageRequired: "必要なストレージの空き",
   PlayerTradeTabImport: "取り込み",
   PlayerTradeTabPendingTrades: "保留中の取引",
   PlayerTradeTabTrades: "取引",
   PlayerTradeTariffTooltip: "取引の関税として徴収",
   PlayerTradeWant: "要求物",
   PlayerTradeYouGetGross: "受け取るもの(関税適用前): %{res}",
   PlayerTradeYouGetNet: "受け取るもの(関税適用後): %{res}",
   PlayerTradeYouPay: "渡すもの: %{res}",
   Poem: "Poem",
   PoetrySchool: "Poetry School",
   Politics: "Politics",
   PolytheismLevelX: "Polytheism %{level}",
   PorcelainTower: "Porcelain Tower",
   PorcelainTowerDesc: "+5 Happiness. When constructed, all your extra great people at rebirth will become available for this run (they are rolled following the same rule as permanent great people)",
   PorcelainTowerMaxPickPerRoll: "Prefer Max Pick Per Roll",
   PorcelainTowerMaxPickPerRollDescHTML: "When choosing great people after Porcelain Tower completed, prefer max pick per roll for the available amount",
   Poseidon: "Poseidon",
   PoseidonDescV2: "All adjacent buildings get free upgrades to Level 25 and +N Production, Worker Capacity and Storage Multiplier. N = Tier of the building",
   PoultryFarm: "Poultry Farm",
   Power: "Power",
   PowerAvailable: "Power Available",
   PowerUsed: "Power Used",
   PreciousMetal: "Precious Metal",
   Printing: "Printing",
   PrintingHouse: "Printing House",
   PrintingPress: "Printing Press",
   PrivateOwnership: "Private Ownership",
   Produce: "Produce",
   ProduceResource: "Produce: %{resource}",
   ProductionMultiplier: "Production Multiplier",
   ProductionPriority: "Production Priority",
   ProductionPriorityDescV4: "Priority determins the order that buildings transport and produce - a bigger number means a building transports and produces before other buildings",
   ProductionWorkers: "Production Workers",
   Progress: "Progress",
   ProgressTowardsNextGreatPerson: "Progress Towards Next Great Person at Rebirth",
   ProgressTowardsTheNextGreatPerson: "Progress Towards the Next Great Person",
   PromotionGreatPersonDescV2: "When consumed, promote any permanent great people of the same age to the next age",
   ProphetsMosque: "Prophet's Mosque",
   ProphetsMosqueDesc: "Double the effect of Harun al-Rashid. Generate science based on faith production of all mosques",
   Province: "Province",
   ProvinceAegyptus: "Aegyptus",
   ProvinceAfrica: "Africa",
   ProvinceAsia: "Asia",
   ProvinceBithynia: "Bithynia",
   ProvinceCantabri: "Cantabri",
   ProvinceCappadocia: "Cappadocia",
   ProvinceCilicia: "Cilicia",
   ProvinceCommagene: "Commagene",
   ProvinceCreta: "Creta",
   ProvinceCyprus: "Cyprus",
   ProvinceCyrene: "Cyrene",
   ProvinceGalatia: "Galatia",
   ProvinceGallia: "Gallia",
   ProvinceGalliaCisalpina: "Gallia Cisalpina",
   ProvinceGalliaTransalpina: "Gallia Transalpina",
   ProvinceHispania: "Hispania",
   ProvinceIllyricum: "Illyricum",
   ProvinceItalia: "Italia",
   ProvinceJudia: "Judia",
   ProvinceLycia: "Lycia",
   ProvinceMacedonia: "Macedonia",
   ProvinceMauretania: "Mauretania",
   ProvinceNumidia: "Numidia",
   ProvincePontus: "Pontus",
   ProvinceSardiniaAndCorsica: "Sardinia And Corsica",
   ProvinceSicillia: "Sicillia",
   ProvinceSophene: "Sophene",
   ProvinceSyria: "Syria",
   PublishingHouse: "Publishing House",
   PyramidOfGiza: "ギザのピラミッド",
   PyramidOfGizaDesc: "All buildings that produce workers get +1 Production Multiplier",
   QinShiHuang: "Qin Shi Huang",
   Radio: "Radio",
   RadioStation: "Radio Station",
   Railway: "Railway",
   RamessesII: "Ramesses II",
   RamessesIIDesc: "+%{value} Builder Capacity Multiplier",
   RandomColorScheme: "Random Color Scheme",
   RapidFire: "Rapid Fire",
   ReadFullPatchNotes: "パッチノートを見る",
   RebirthHistory: "Rebirth History",
   RebirthTime: "Rebirth Time",
   Reborn: "Rebirth",
   RebornModalDescV3: "You will start a new empire but all your great people <b>from this run</b> becomes permanent shards, which can be used to upgrade your <b>permanent great people level</b>. You will also get extra great people shards based on your <b>total empire value</b>",
   RebornOfflineWarning: "You are currently offline. You can only rebirth when you are connected to the server",
   RebornTradeWarning: "You have trades that are active or can be claimed. <b>Rebirth will erase them</b> - you should consider cancelling or claiming first",
   RedistributeAmongSelected: "Redistribute Among Selected",
   RedistributeAmongSelectedCap: "Cap",
   RedistributeAmongSelectedImport: "Import",
   Refinery: "Refinery",
   Reichstag: "Reichstag",
   Religion: "Religion",
   ReligionBuddhism: "Buddhism",
   ReligionChristianity: "Christianity",
   ReligionDescHTML: "Choose from <b>Christianity, Islam, Buddhism or Polytheism</b> as your empire religion. You <b>cannot switch religion</b> after it is chosen. You can unlock more boost within each religion",
   ReligionIslam: "Islam",
   ReligionPolytheism: "Polytheism",
   Renaissance: "Renaissance",
   RenaissanceAge: "Renaissance",
   ReneDescartes: "René Descartes",
   RequiredDeposit: "Required Deposit",
   RequiredWorkersTooltipV2: "Required number of workers for production is equal to the sum of all resources consumed and produced after multipliers (excluding dynamic multipliers)",
   RequirePower: "Require Power",
   RequirePowerDesc: "This building needs to be built on a tile with power and can extend the power to its adjacent tiles",
   Research: "研究",
   ResearchFund: "Research Fund",
   ResearchLab: "Research Lab",
   ResearchMenu: "研究",
   ResourceAmount: "数量",
   ResourceBar: "Resource Bar",
   ResourceBarExcludeStorageFullHTML: "Exclude buildings that have <b>full storage</b> from Not Producing Buildings",
   ResourceBarExcludeTurnedOffOrNoActiveTransportHTML: "Exclude buildings that are <b>turned off</b> from Not Producing Buildings",
   ResourceBarShowUncappedHappiness: "Show Uncapped Happiness",
   ResourceCloneTooltip: "The production multiplier only applies to the cloned resource (i.e. the extra copy)",
   ResourceColor: "Resource Color",
   ResourceExportBelowCap: "Export Below Max Amount",
   ResourceExportBelowCapTooltip: "Allow other buildings to transport a resource from this building even when its amount is below the max amount",
   ResourceExportToSameType: "Export to the Same Type",
   ResourceExportToSameTypeTooltip: "Allow other buildings of the same type to transport a resource from this building",
   ResourceFromBuilding: "%{resource} from %{building}",
   ResourceImport: "Resource Transport",
   ResourceImportCapacity: "Resource Transport Capacity",
   ResourceImportImportCapV2: "Max Amount",
   ResourceImportImportCapV2Tooltip: "This building will stop transporting this resource when the max amount is reached",
   ResourceImportImportPerCycleV2: "Per Cycle",
   ResourceImportImportPerCycleV2ToolTip: "The amount of this resource that is transported per cycle",
   ResourceImportPartialWarningHTML: "The total resource transport capacity has exceeds the maximum capacity: <b>each resource transport will only transport partially per cycle</b>",
   ResourceImportResource: "Resource",
   ResourceImportSettings: "Resource Transport: %{res}",
   ResourceImportStorage: "Storage",
   ResourceNeeded: "Extra %{resource} x%{amount} Needed",
   ResourceTransportPreference: "Transport Preference",
   RevealDeposit: "Reveal",
   Revolution: "Revolution",
   RhineGorge: "Rhine Gorge",
   RhineGorgeDesc: "+2 Happiness for each wonder within 2 tile range",
   RichardFeynman: "Richard Feynman",
   RichardFeynmanDesc: "+%{value} Science from All Workers if more than 50% of workers are busy and less than 50% of busy workers work in transportation",
   RichardJordanGatling: "Richard Jordan Gatling",
   Rifle: "Rifle",
   RifleFactory: "Rifle Factory",
   Rifling: "Rifling",
   Rijksmuseum: "Rijksmuseum",
   RijksmuseumDesc: "+5 Happiness. All buildings that consume or produce Culture get +1 Production, Worker Capacity and Storage Multiplier",
   RoadAndWheel: "Road & Wheel",
   RobertNoyce: "Robert Noyce",
   Robocar: "Robocar",
   RobocarFactory: "Robocar Factory",
   Robotics: "Robotics",
   RockefellerCenterChristmasTree: "Rockefeller Center Christmas Tree",
   RockefellerCenterChristmasTreeDesc: "+3 Happiness for each unlocked age. This natural wonder can only be discovered in December",
   Rocket: "Rocket",
   RocketFactory: "Rocket Factory",
   Rocketry: "Rocketry",
   Roman: "Roman",
   RomanForum: "Roman Forum",
   RudolfDiesel: "Rudolf Diesel",
   Rurik: "Rurik",
   RurikDesc: "+%{value} Happiness",
   SagradaFamilia: "Sagrada Familia",
   SagradaFamiliaDesc: "All buildings within 2 tile range get +N Production, Worker Capacity and Storage Multipliers. N = max tier difference among buildings adjacent (1 tile range) to the Sagrada Familia",
   SaintBasilsCathedral: "Saint Basil's Cathedral",
   SaintBasilsCathedralDescV2: "Allow resource extraction buildings to work adjacent to a deposit. All adjacent Tier I buildings get +1 Production, Worker Capacity and Storage Multiplier",
   Saladin: "Saladin",
   Samsuiluna: "Samsu-iluna",
   Sand: "砂",
   Sandpit: "Sandpit",
   SantaClausVillage: "Santa Claus Village",
   SantaClausVillageDesc: "When completed, a great person of the current age is born. This wonder can be upgraded and each additional upgrade provides an extra great person. When choosing great people from this wonder, 4 choices are provided. This wonder can only be constructed in December",
   SargonOfAkkad: "Sargon of Akkad",
   Satellite: "Satellite",
   SatelliteFactory: "Satellite Factory",
   SatoshiNakamoto: "Satoshi Nakamoto",
   Saturnalia: "Saturnalia: Alps no longer increases Consumption Multiplier",
   SaveAndExit: "セーブしてやめる",
   School: "School",
   Science: "研究ポイント",
   ScienceFromBusyWorkers: "Science From Busy Workers",
   ScienceFromIdleWorkers: "Science From Idle Workers",
   SciencePerBusyWorker: "Per Busy Worker",
   SciencePerIdleWorker: "Per Idle Worker",
   ScrollSensitivity: "スクロール感度",
   ScrollSensitivityDescHTML: "Adjust sensitivity when scrolling mousewheel. <b>Must be between 0.01 to 100. Default is 1</b>",
   ScrollWheelAdjustLevelTooltip: "You can use scroll wheel to adjust the level when your cursor is over this",
   SeaTradeCost: "Sea Trade Cost",
   SeaTradeUpgrade: "Trading with players across the sea. Tariff for each sea tile: %{tariff}",
   SelectCivilization: "Select Civilization",
   SelectedAll: "すべて選択",
   SelectedCount: "%{count} 個選択中",
   Semiconductor: "Semiconductor",
   SemiconductorFab: "Semiconductor Fab",
   SendExplorer: "探索者を送る",
   SergeiKorolev: "Sergei Korolev",
   SetAsDefault: "デフォルトにする",
   SetAsDefaultBuilding: "全ての %{building} でデフォルトにする",
   Shamanism: "Shamanism",
   Shelter: "Shelter",
   Shortcut: "ショートカット",
   ShortcutBuildingPageSellBuildingV2: "建物の解体",
   ShortcutBuildingPageToggleBuilding: "生産の切り替え",
   ShortcutBuildingPageToggleBuildingSetAllSimilar: "同じ建物の生産の切り替え",
   ShortcutBuildingPageUpgrade1: "アップグレードボタン 1 (+1)",
   ShortcutBuildingPageUpgrade2: "アップグレードボタン 2 (+5)",
   ShortcutBuildingPageUpgrade3: "アップグレードボタン 3 (+10)",
   ShortcutBuildingPageUpgrade4: "アップグレードボタン 4 (+15)",
   ShortcutBuildingPageUpgrade5: "アップグレードボタン 5 (+20)",
   ShortcutClear: "削除",
   ShortcutConflict: "このキーは%{name}と競合しています",
   ShortcutNone: "なし",
   ShortcutPressShortcut: "設定したいキーを押してください...",
   ShortcutSave: "保存",
   ShortcutScopeBuildingPage: "建物画面",
   ShortcutScopeConstructionPage: "建設/アップグレード画面",
   ShortcutScopeEmptyTilePage: "空タイル画面",
   ShortcutScopePlayerMapPage: "取引マップ画面",
   ShortcutScopeTechPage: "研究画面",
   ShortcutScopeUnexploredPage: "未探索画面",
   ShortcutTechPageGoBackToCity: "街に戻る",
   ShortcutTechPageUnlockTech: "選択している技術を解放",
   ShortcutUpgradePageCancelAllUpgrades: "Cancel All Upgrades",
   ShortcutUpgradePageCancelUpgrade: "アップグレードをやめる",
   ShortcutUpgradePageDecreaseLevel: "アップグレードレベルを下げる",
   ShortcutUpgradePageEndConstruction: "建設を取りやめる",
   ShortcutUpgradePageIncreaseLevel: "アップグレードレベルを上げる",
   ShowTransportArrow: "Show Transport Arrow",
   ShowTransportArrowDescHTML: "Turning this off will hide transport arrows. It might <i>slightly</i> improve performance on low end devices. Performance improvement takes effect <b>after restarting your game</b>",
   ShowUnbuiltOnly: "Only show buildings that haven't been built yet",
   Shrine: "Shrine",
   SidePanelWidth: "Side Panel Width",
   SidePanelWidthDescHTML: "Change the width of the side panel. <b>Require restarting your game to take effect</b>",
   SiegeRam: "Siege Ram",
   SiegeWorkshop: "Siege Workshop",
   Silicon: "Silicon",
   SiliconSmelter: "Silicon Smelter",
   Skyscraper: "Skyscraper",
   Socialism: "Socialism",
   SocialismLevel4DescHTMLV2: "Generate one-time science equivalent to the cost of the cheapest <b>World Wars Age</b> technology",
   SocialismLevel5DescHTMLV2: "Generate one-time science equivalent to the cost of the cheapest <b>Cold War Age</b> technology",
   SocialismLevelX: "Socialism Level %{level}",
   SocialNetwork: "Social Network",
   Socrates: "Socrates",
   SocratesDesc: "+%{value} Science from Busy Workers",
   Software: "Software",
   SoftwareCompany: "Software Company",
   Sound: "Sound",
   SoundEffect: "Sound Effect",
   SourceGreatPerson: "Great Person: %{person}",
   SourceGreatPersonPermanent: "Permanent Great Person: %{person}",
   SourceIdeology: "Ideology: %{ideology}",
   SourceReligion: "Religion: %{religion}",
   SourceResearch: "Research: %{tech}",
   SourceTradition: "Tradition: %{tradition}",
   SpaceCenter: "Space Center",
   Spacecraft: "Spacecraft",
   SpacecraftFactory: "Spacecraft Factory",
   SpaceNeedle: "Space Needle",
   SpaceNeedleDesc: "+1 Happiness for each wonder constructed",
   SpaceProgram: "Space Program",
   Sports: "Sports",
   Stable: "Stable",
   Stadium: "Stadium",
   StartFestival: "Let the Festival Begin!",
   Stateship: "Stateship",
   StatisticsBuildings: "Buildings",
   StatisticsBuildingsSearchText: "Type a building name to search",
   StatisticsEmpire: "Empire",
   StatisticsExploration: "Exploration",
   StatisticsOffice: "Statistics Office",
   StatisticsOfficeDesc: "Provide statistics of your empire. Generate explorers for exploring the map",
   StatisticsResources: "Resources",
   StatisticsResourcesDeficit: "Deficit",
   StatisticsResourcesDeficitDesc: "Production: %{output} - Consumption: %{input}",
   StatisticsResourcesRunOut: "Run Out",
   StatisticsResourcesSearchText: "Type a resource name to search",
   StatisticsScience: "研究ポイント",
   StatisticsScienceFromBuildings: "Science From Buildings",
   StatisticsScienceFromWorkers: "Science From Workers",
   StatisticsScienceProduction: "Science Production",
   StatisticsStalledTransportation: "Stalled Transportation",
   StatisticsTotalTransportation: "Total Transportation",
   StatisticsTransportation: "Transportation",
   StatisticsTransportationPercentage: "Percentage of Transportation Workers",
   StatueOfLiberty: "Statue of Liberty",
   StatueOfLibertyDesc: "All buildings adjacent to the Statue of Liberty get +N Production, Worker Capacity and Storage Multiplier. N = Number of its adjacent buildings of the same type",
   StatueOfZeus: "Statue of Zeus",
   StatueOfZeusDesc: "Spawn random deposits that have been revealed on adjacent empty tiles. All adjacent Tier I buildings get +5 Production and Storage Multiplier",
   SteamAchievement: "Steam Achievement",
   SteamAchievementDetails: "View Steam Achievement",
   SteamEngine: "Steam Engine",
   Steamworks: "Steamworks",
   Steel: "Steel",
   SteelMill: "Steel Mill",
   StephenHawking: "Stephen Hawking",
   Stock: "Stock",
   StockExchange: "Stock Exchange",
   StockMarket: "Stock Market",
   StockpileDesc: "This building will transport %{capacity}x input resources per production cycle until the max is reached",
   StockpileMax: "Max Stockpile",
   StockpileMaxDesc: "This building will stop transporting a resource once there are enough for %{cycle} production cycles",
   StockpileMaxUnlimited: "Unlimited",
   StockpileMaxUnlimitedDesc: "This building will never stop transporting resources, only until the storage is full",
   StockpileSettings: "Stockpile Input Capacity",
   Stone: "Stone",
   StoneAge: "Stone Age",
   Stonehenge: "Stonehenge",
   StonehengeDesc: "All buildings that consume or produce stone get +1 Production Multiplier",
   StoneQuarry: "Stone Quarry",
   StoneTool: "Stone Tool",
   StoneTools: "Stone Tools",
   Storage: "Storage",
   StorageBaseCapacity: "Base Capacity",
   StorageMultiplier: "Storage Multiplier",
   StorageUsed: "Storage Used",
   StPetersBasilica: "St. Peter's Basilica",
   StPetersBasilicaDescV2: "All churches get +5 Storage Multiplier. Generate science based on faith production of all churches",
   Submarine: "Submarine",
   SubmarineYard: "Submarine Yard",
   SuleimanI: "Suleiman I",
   SummerPalace: "Summer Palace",
   SummerPalaceDesc: "All adjacent buildings that consume or produce Gunpowder are exempt from -1 Happiness. All buildings that consume or produce Gunpowder get +1 Production, Worker Capacity and Storage Multiplier",
   Supercomputer: "Supercomputer",
   SupercomputerLab: "Supercomputer Lab",
   SupporterPackRequired: "Supporter Pack Required",
   SupporterThankYou: "CivIdle is kept afloat thanks to the generousity of the following supporter pack owners",
   SwissBank: "Swiss Bank",
   SwissBankDescV2: "Convert a chosen resource from adjacent warehouses to Koti (10 million in Sanskrit) - a tradeable resource that is worth 10M value. Each level of Swiss Bank add 1 Koti conversion per cycle, which is affected by Production Multiplier. Swiss Bank can store unlimited amount of Koti",
   Sword: "Sword",
   SwordForge: "Sword Forge",
   SydneyOperaHouse: "Sydney Opera House",
   SydneyOperaHouseDescV2: "Sydney Opera House",
   SyncToANewDevice: "Sync To A New Device",
   Synthetics: "Synthetics",
   TajMahal: "Taj Mahal",
   TajMahalDescV2: "A great person of Classical Age and a great person of Middle Age are born. +5 Builder Capacity Multiplier when upgrading buildings over Level 20",
   TangOfShang: "Tang of Shang",
   TangOfShangDesc: "+%{value} Science From Idle Workers",
   Tank: "Tank",
   TankFactory: "Tank Factory",
   TechAge: "Age",
   TechGlobalMultiplier: "Boost",
   TechHasBeenUnlocked: "%{tech} has been unlocked",
   TechProductionPriority: "Unlock building priority - allow setting production priority for each building",
   TechResourceTransportPreference: "Unlock building transport preference - allow setting how a building transports resources needed for its production",
   TechResourceTransportPreferenceAmount: "Amount",
   TechResourceTransportPreferenceAmountTooltip: "This building will prefer transporting resources from buildings that have larger amount in storage",
   TechResourceTransportPreferenceDefault: "Default",
   TechResourceTransportPreferenceDefaultTooltip: "Do not override transport preference for this resource, will use the building's transport preference instead",
   TechResourceTransportPreferenceDistance: "Distance",
   TechResourceTransportPreferenceDistanceTooltip: "This building will prefer transporting resources from buildings that are closer in distance",
   TechResourceTransportPreferenceOverrideTooltip: "This resource has transport preference override: %{mode}",
   TechResourceTransportPreferenceStorage: "Storage",
   TechResourceTransportPreferenceStorageTooltip: "This building will prefer transporting resources from buildings that have higher percentage of used storage",
   TechStockpileMode: "Unlock stockpile mode - allow adjusting stockpile for each building",
   Teleport: "Teleport",
   TeleportDescHTML: "A teleport is generated <b>every %{time} seconds</b>. A teleport can be used to <b>move a building (wonders excluded)</b> once",
   Television: "Television",
   TempleOfArtemis: "Temple Of Artemis",
   TempleOfArtemisDesc: "All Sword Forges and Armories get +5 Level when Temple Of Artemis is completed. All Sword Forges and Armories get +1 Production, Worker Capacity and Storage Multiplier",
   TempleOfHeaven: "Temple of Heaven",
   TempleOfHeavenDesc: "All buildings that are level 10 or higher get +1 Worker Capacity Multiplier",
   TempleOfPtah: "Temple of Ptah",
   TerracottaArmy: "Terracotta Army",
   TerracottaArmyDesc: "All Iron Mining Camps get +1 Production, Worker Capacity and Storage Multiplier. Iron Forges get +1 Production Multiplier for each adjacent Iron Mining Camp that is built on top of an Iron deposit",
   Thanksgiving: "Thanksgiving: Wall Street provides double the boost to buildings and applies to Mutual Fund, Hedge Fund and Bitcoin Miner, and +5 Production Multiplier to Research Funds",
   Theater: "Theater",
   Theme: "テーマ",
   ThemeColor: "Theme Color",
   ThemeColorResearchBackground: "Research Background",
   ThemeColorReset: "Reset to Default",
   ThemeColorResetBuildingColors: "Reset Building Colors",
   ThemeColorResetResourceColors: "Reset Resource Colors",
   ThemeInactiveBuildingAlpha: "Inactive Building Alpha",
   ThemePremiumTile: "This tile is only available for Supporter Pack owners",
   ThemeResearchHighlightColor: "Research Highlight Color",
   ThemeResearchLockedColor: "Research Locked Color",
   ThemeResearchUnlockedColor: "Research Unlocked Color",
   ThemeTransportIndicatorAlpha: "Transport Indicator Alpha",
   Theocracy: "Theocracy",
   TheoreticalData: "Theoretical Data",
   ThePentagon: "The Pentagon",
   ThePentagonDesc: "After constructed, generate teleports that can be used to move buildings. All buildings within 2 tile range get +1 Production, Worker Capacity and Storage Multiplier",
   TheWhiteHouse: "The White House",
   ThomasEdison: "Thomas Edison",
   ThomasGresham: "Thomas Gresham",
   Tile: "タイル",
   TileBonusRefreshIn: "Tile bonus will refresh in <b>%{time}</b>",
   TimBernersLee: "Tim Berners-Lee",
   TimeWarp: "Time Warp",
   TimeWarpWarning: "Accelerate at a higher speed than your computer can handle might result in data loss: USE AT YOUR OWN RISK",
   ToggleWonderEffect: "Toggle Wonder Effect",
   Tool: "Tool",
   TopkapiPalace: "Topkapı Palace",
   TopkapiPalaceDesc: "All buildings within 2 tile range get +X Storage Multiplier. X = 50% of its Production Multiplier (excluding Dynamic)",
   TotalEmpireValue: "Total Empire Value",
   TotalEmpireValuePerCycle: "Total Empire Value Per Cycle",
   TotalEmpireValuePerCyclePerGreatPeopleLevel: "Total Empire Value Per Cycle Per Great People Level",
   TotalEmpireValuePerWallSecond: "Total Empire Value Per Real Second",
   TotalEmpireValuePerWallSecondPerGreatPeopleLevel: "Total Empire Value Per Real Second Per Great People Level",
   TotalGameTimeThisRun: "Total Game Time This Run",
   TotalScienceRequired: "Total Science Required",
   TotalStorage: "Total Storage",
   TotalWallTimeThisRun: "Total Real Time This Run",
   TotalWallTimeThisRunTooltip: "Real time (aka. wall time) measures the actual time taken for this run. The differs from the game time in that Time Warp in Petra and Offline Production does not affect real time but it does affect game time",
   TotalWorkers: "Total Workers",
   TowerBridge: "Tower Bridge",
   TowerBridgeDesc: "After constructed, a great person from unlocked ages is born every 3600 cycles (1h game time)",
   TowerOfBabel: "Tower of Babel",
   TowerOfBabelDesc: "Provides +2 Production Multiplier to all buildings that have at least one working building located adjacent to the Tower of Babel",
   TradeFillSound: "'Trade Filled' Sound",
   TradeValue: "Trade Value",
   TraditionCommerce: "Commerce",
   TraditionCultivation: "Cultivation",
   TraditionDescHTML: "Choose from <b>Cultivation, Commerce, Expansion or Honor</b> as your empire tradition. You <b>cannot switch tradition</b> after it is chosen. You can unlock more boost within each tradition",
   TraditionExpansion: "Expansion",
   TraditionHonor: "Honor",
   Train: "Train",
   TranslationPercentage: "注:大部分で英語のままです。現在%{language}は %{percentage} 翻訳されています。GitHubでの、翻訳改善へのご協力お願い致します。",
   TranslatorCredit: "taku1417",
   Translators: "翻訳者",
   TransportAllocatedCapacityTooltip: "Builder Capacity allocated to transporting this resource",
   TransportationWorkers: "Transportation Workers",
   TransportCapacity: "Transport Capacity",
   TransportCapacityMultiplier: "Transport Capacity Multiplier",
   TransportManualControlTooltip: "Transport this resource for construction/upgrade",
   TransportPlanCache: "Transport Plan Cache",
   TransportPlanCacheDescHTML:
      "Every cycle, each building calculates the best transport plan based on its settings - this process requires high CPU power. Enabling this will attempt to cache the result of the transport plan if it is still valid and therefore reduce CPU usage and frame rate drop. <b>Experimental Feature</b>",
   TribuneUpgradeDescGreatPeopleWarning: "Your current run has great people. You should <b>rebirth first</b>. Upgrading to Quaestor rank will reset your current run",
   TribuneUpgradeDescGreatPeopleWarningTitle: "Please Rebirth First",
   TribuneUpgradeDescV4:
      "You can play the full game as Tribune if you do not plan to participate in the <b>optional</b> online features. To acquire unrestricted access to the online features, you will need to upgrade to Quaestor. <b>This is an anti-bot measure to keep the game free for everyone.</b> However, <b>when upgrading to Quaestor</b> you can carry over great people: <ul><li>Up to Level <b>3</b> for Bronze, Iron and Classical Age</li><li>Up to Level <b>2</b> for Middle Age, Renaissance and Industrial Age</li><li>Up to Level <b>1</b> for World Wars, Cold War and Information Age</li></ul>Great People Shards above the level and <b>Age Wisdom</b> levels <b>cannot</b> be carried over",
   TurnOffFullBuildings: "Turn Off All %{building} With Full Storage",
   TurnOnTimeWarpDesc: "Cost %{speed} warps for every second and accelerate your empire to run at %{speed}x speed.",
   Tutorial: "Tutorial",
   TutorialPlayerFlag: "Choose your player flag",
   TutorialPlayerHandle: "Choose your player handle",
   TV: "TV",
   TVStation: "TV Station",
   UnclaimedGreatPersonPermanent: "You have unclaimed <b>Permanent Great People</b>, click here to claim",
   UnclaimedGreatPersonThisRun: "You have unclaimed <b>Great People this run</b>, click here to claim",
   UnexploredTile: "Unexplored Tile",
   UNGeneralAssemblyCurrent: "Current UN General Assembly #%{id}",
   UNGeneralAssemblyMultipliers: "<b>+%{count}</b> Production, Worker Capacity and Storage Multipliers for <b>%{buildings}</b>",
   UNGeneralAssemblyNext: "Upcoming UN General Assembly #%{id}",
   UNGeneralAssemblyVoteEndIn: "You can change your vote any time before the voting ends in <b>%{time}</b>",
   UniqueBuildings: "Unique Buildings",
   UniqueTechMultipliers: "Unique Tech Multipliers",
   UnitedNations: "United Nations",
   UnitedNationsDesc: "All Tier IV and V and VI buildings get +1 Production, Worker Capacity and Storage Multiplier. Participate in UN General Assembly and vote for an additional boost each week",
   University: "University",
   UnlockableResearch: "Unlockable Research",
   UnlockBuilding: "Unlock",
   UnlockTechProgress: "Progress",
   UnlockXHTML: "Unlock <b>%{name}</b>",
   Upgrade: "Upgrade",
   UpgradeBuilding: "Upgrade",
   UpgradeBuildingNotProducingDescV2: "This building is being upgraded - <b>production will halt until upgrade is complete</b>",
   UpgradeTo: "Upgrade To Level %{level}",
   Uranium: "Uranium",
   UraniumEnrichmentPlant: "Uranium Enrichment Plant",
   UraniumMine: "Uranium Mine",
   Urbanization: "Urbanization",
   UserAgent: "ユーザーエージェント: %{driver}",
   View: "画面",
   ViewMenu: "画面",
   ViewTechnology: "View",
   Vineyard: "Vineyard",
   VirtualReality: "Virtual Reality",
   Voltaire: "Voltaire",
   WallOfBabylon: "Wall of Babylon",
   WallOfBabylonDesc: "All buildings get +N Storage Multiplier. N = number of unlocked ages / 2",
   WallStreet: "Wall Street",
   WallStreetDesc: "All buildings that produce coin, banknote, bond, stock and forex within 2 tile range get +N production multiplier. N = Random value between 1 to 5 which is different per building and changes with every market refresh. Double the effect of John D. Rockefeller",
   WaltDisney: "Walt Disney",
   Warehouse: "Warehouse",
   WarehouseAutopilotSettings: "Autopilot Settings",
   WarehouseAutopilotSettingsEnable: "Enable Autopilot",
   WarehouseAutopilotSettingsRespectCapSetting: "Require Storage < Cap",
   WarehouseAutopilotSettingsRespectCapSettingTooltip: "Autopilot will only transport resources whose amount in storage is below the cap",
   WarehouseDesc: "Transport specific resources and provide extra storage",
   WarehouseExtension: "Unlock warehouse caravansary extension mode. Allow warehouses adjacent to caravansaries to be included in player trading",
   WarehouseSettingsAutopilotDesc: "This warehouse will use its idle capacity to transport resources from buildings that have full storage. Current idle capacity: %{capacity}",
   WarehouseUpgrade: "Unlock warehouse autopilot mode. Free transportation between a warehouse and its adjacent buildings",
   WarehouseUpgradeDesc: "Free transportation between this warehouse and its adjacent tiles",
   Warp: "ワープ",
   WarpSpeed: "ワープ速度",
   Water: "水",
   WellStockedTooltip: "Well-stocked buildings are buildings that have enough resources for its production, which include buildings that are producing, that have full storage or not producing due to lack of workers",
   WernherVonBraun: "Wernher von Braun",
   Westminster: "Westminster",
   Wheat: "小麦",
   WheatFarm: "小麦農場",
   WildCardGreatPersonDescV2: "When consumed, become any great person of the same age",
   WilliamShakespeare: "William Shakespeare",
   Wine: "Wine",
   Winery: "Winery",
   WishlistSpaceshipIdle: "Wishlist Spaceship Idle",
   Wonder: "遺産",
   WonderBuilderCapacityDescHTML: "<b>Builder Capacity</b> when constructing wonders are affected by the <b>age</b> and <b>technology</b> that unlocks the wonder",
   WondersBuilt: "World Wonders Built",
   WondersUnlocked: "World Wonders Unlocked",
   WonderUpgradeLevel: "Wonder Level",
   Wood: "木",
   Worker: "労働者",
   WorkerCapacityMultiplier: "Worker Capacity Multiplier",
   WorkerHappinessPercentage: "Happiness Multiplier",
   WorkerMultiplier: "Worker Capacity",
   WorkerPercentagePerHappiness: "%{value}% Multiplier for Each Happiness",
   Workers: "労働者数",
   WorkersAvailableAfterHappinessMultiplier: "幸福度を考慮した労働者数",
   WorkersAvailableBeforeHappinessMultiplier: "幸福度を考慮しない労働者数",
   WorkersBusy: "Busy Workers",
   WorkerScienceProduction: "Worker Science Production",
   WorkersRequiredAfterMultiplier: "Required Workers",
   WorkersRequiredBeforeMultiplier: "Required Worker Capacity",
   WorkersRequiredForProductionMultiplier: "Production Capacity Per Worker",
   WorkersRequiredForTransportationMultiplier: "Transportation Capacity Per Worker",
   WorkersRequiredInput: "Transportation",
   WorkersRequiredOutput: "Production",
   WorldWarAge: "World Wars",
   WorldWideWeb: "World Wide Web",
   WritersGuild: "Writer's Guild",
   Writing: "Writing",
   WuZetian: "Empress Wu Zetian",
   WuZetianDesc: "+%{value} Transport Capacity Multiplier",
   Xuanzang: "Xuanzang",
   YangtzeRiver: "Yangtze River",
   YangtzeRiverDesc: "All buildings that consume water get +1 Production, Worker Capacity and Storage Multiplier. Double the effect of Zheng He (Great Person). Each level of Permanent Empress Wu Zetian (Great Person) provides +1 Storage Multiplier to all buildings",
   YearOfTheSnake: "Year of the Snake",
   YearOfTheSnakeDesc:
      "After completed, when entering a new age, instead of getting one great person of each unlocked age, get the same amount of great people in the current age. All buildings within 2-tile range get +1 Production Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Production Multiplier to buildings within 2-tile range. This wonder can only be constructed during the lunar new year period (1.20 ~ 2.10)",
   YellowCraneTower: "Yellow Crane Tower",
   YellowCraneTowerDesc: "+1 choice when choosing great people. All buildings within 1 tile range get +1 Production, Worker Capacity and Storage Multiplier. When constructed next to Yangtze River, the range increases to 2 tile",
   YuriGagarin: "Yuri Gagarin",
   ZagrosMountains: "Zagros Mountains",
   ZagrosMountainsDesc: "All adjacent buildings that have less than 5 Production Multiplier get +2 Production Multiplier. Double the effect of Nebuchadnezzar II (Great Person)",
   ZahaHadid: "Zaha Hadid",
   ZahaHadidDesc: "+%{value} Builder Capacity Multiplier",
   Zenobia: "Zenobia",
   ZenobiaDesc: "+%{value}h Petra Warp Storage",
   ZhengHe: "Zheng He",
   ZigguratOfUr: "Ziggurat of Ur",
   ZigguratOfUrDescV2: "Every 10 happiness (capped) provides +1 Production Multiplier to all buildings that do not produce workers and are unlocked in previous ages (max = number of unlocked ages / 2). Wonders (incl. Natural) no longer provide +1 Happiness. The effect can be turned off",
   Zoroaster: "Zoroaster",
   Zugspitze: "Zugspitze",
   ZugspitzeDesc: "For each unlocked age, get one point that can be used to provide one extra level to any Great Person that is born from this run",
};
