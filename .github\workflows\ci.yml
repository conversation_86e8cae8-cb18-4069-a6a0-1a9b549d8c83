name: CI Run

on:
  push:
  pull_request:

jobs:
  quality:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup pnmp
        uses: pnpm/action-setup@v3
        with:
          run_install: |
            - recursive: false
      - name: Run Biome
        run: pnpm run ci
      - name: Setup Test
        uses: pnpm/action-setup@v3
        with:
          run_install: |
            - recursive: false
            - cwd: "./test"
      - name: Run Test
        working-directory: ./test
        run:
          pnpm run ci
