export const KR = {
   About: "CivIdle에 대하여",
   AbuSimbel: "아부심벨",
   AbuSimbelDesc: "람세스 2세의 효과가 두배로 증가합니다. 인접한 모든 불가사의가 +1의 행복도를 얻습니다.",
   AccountActiveTrade: "활성화된 거래",
   AccountChatBadge: "채팅 뱃지",
   AccountCustomColor: "커스텀 색상",
   AccountCustomColorDefault: "기본값",
   AccountGreatPeopleLevelRequirement: "필요 영구 위인 레벨",
   AccountLevel: "계정 랭크",
   AccountLevelAedile: "조영관",
   AccountLevelConsul: "영사",
   AccountLevelMod: "Moderator",
   AccountLevelPlayTime: "활성화된 온라인 플레이시간 > %{requiredTime} (현재 플레이시간 %{actualTime})",
   AccountLevelPraetor: "집정관",
   AccountLevelQuaestor: "재무관",
   AccountLevelSupporterPack: "Supporter Pack 구매",
   AccountLevelTribune: "호민관",
   AccountLevelUpgradeConditionAnyHTML: "계정을 업그레이드하려면 <b>다음 중 하나</b>의 기준만 충족하면 됩니다.",
   AccountPlayTimeRequirement: "필요 누적 게임 플레이 시간",
   AccountRankUp: "계정 랭크 업글",
   AccountRankUpDesc: "모든 진행 상황이 새로운 랭크로 이월 됩니다.",
   AccountRankUpTip: "축하합니다, 귀하의 계정은 더 높은 랭크로 업그레이드 할 수 있는 자격이 생겼습니다. 업그레이드 할려면 클릭하세요!",
   AccountSupporter: "Supporter Pack 구매자",
   AccountTradePriceRange: "거래 가격 범위",
   AccountTradeTileReservationTime: "거래 타일 보존",
   AccountTradeTileReservationTimeDesc: "마지막 온라인 시간으로부터 거래 타일이 보존되는 시간입니다. 보존기간이 끝나면 다른 플레이어가 타일을 사용할수 있습니다.",
   AccountTradeValuePerMinute: "분당 거래 가치",
   AccountTypeShowDetails: "계정 세부 정보 표시",
   AccountUpgradeButton: "재무관으로 업그레이드",
   AccountUpgradeConfirm: "계정 업그레이드",
   AccountUpgradeConfirmDescV2: "계정을 업그레이드하면 <b>현재 회차가 재설정</b>되고 허용된 수준 내에서 영구적인 위인이 유지됩니다. 이 작업은 취소할 수 <b>없습니다</b>. 계속하시겠습니까?",
   Acknowledge: "Acknowledge",
   Acropolis: "아크로폴리스",
   ActorsGuild: "배우 길드",
   AdaLovelace: "에이다 러브레이스",
   AdamSmith: "아담 스미스",
   AdjustBuildingCapacity: "생산 능력",
   AdvisorElectricityContent:
      "발전소는 두 가지 새로운 시스템을 제공합니다. 첫 번째, '전력'은 발전소 인접 타일에 번개 모양으로 표시됩니다. 일부 건물(세계대전 시대의 라디오부터)은 입력 목록에 '전력이 필요함' 표시가 있습니다. <b>이는 해당 건물이 번개 타일에 건설되어야 작동함을 의미합니다</b>. 전력을 필요로 하고 전력 타일에 건설된 건물은 인접 타일에도 전력을 전달하므로, 최소한 하나가 발전소에 연결되어 있다면 서로 전력을 전달할 수 있습니다.<br><br>다른 시스템인 '전력화'는 <b>과학이나 노동자를 생산하지 않는 한</b> 지도 어디에서든 모든 건물에 적용할 수 있습니다. 이는 발전소에서 생성된 전력을 사용하여 건물의 소비량과 생산량을 모두 증가시킵니다. 더 높은 수준의 전력화는 점점 더 많은 전력을 요구합니다. '전력이 필요함' 표시가 있는 건물을 전력화하는 것이 표시가 없는 건물을 전력화하는 것보다 효율적입니다.",
   AdvisorElectricityTitle: "전력 및 전기화",
   AdvisorGreatPeopleContent:
      "새로운 기술 시대에 진입할 때마다 해당 시대와 이전 시대에서 위인을 선택할 수 있습니다. 이 위인들은 생산, 과학, 행복도 등 다양한 글로벌 보너스를 제공합니다.<br><br>이 보너스는 이번 환생 동안 영구적입니다. 환생할 때 모든 위대한 인물들은 영구적으로 남으며, 그들의 보너스는 영원히 유지됩니다.<br>나중에 동일한 인물을 선택하면 영구 보너스와 현재 플레이의 보너스가 중첩됩니다. 환생 시 중복된 인물은 저장되어 영구 보너스를 업그레이드하는 데 사용할 수 있습니다. 이는 홈 빌딩의 <b>영구 위인 관리</b> 메뉴에서 확인할 수 있습니다.",
   AdvisorGreatPeopleTitle: "위인",
   AdvisorHappinessContent:
      "행복도는 CivIdle에서 확장을 제한하는 핵심 메커니즘입니다. 행복도는 새로운 기술을 해제하거나 새로운 시대로 진입하고, 불가사의를 건설하고, 행복을 제공하는 위인 등 다양한 방법으로 얻을 수 있습니다. <b>각 건물 건설에는 1 행복도가 필요합니다</b>. 행복도가 0 이상/이하일 때마다 총 노동자 수에 2% 보너스 또는 페널티가 적용됩니다(행복도 -50에서 +50까지 제한). 자세한 행복도 정보는 <b>홈 타일의 행복도 섹션</b>에서 확인할 수 있습니다.",
   AdvisorHappinessTitle: "사람들을 행복하게 유지하세요.",
   AdvisorOkay: "알겠습니다. 감사합니다!",
   AdvisorScienceContent:
      "활동 중인 노동자들이 과학을 생성하여 새로운 기술을 해제하고 문명을 발전시킬 수 있습니다. 연구 메뉴에는 여러 방법으로 접근할 수 있습니다. 과학 수치를 클릭하거나, 홈 타일에서 해제 가능한 기술에 접근하거나, '보기' 메뉴를 통해 들어갈 수 있습니다. 이를 통해 모든 기술과 각 기술에 필요한 과학량을 보여주는 기술 트리로 이동할 수 있습니다. 새로운 기술을 배울 만큼 과학이 충분하다면, 해당 기술을 클릭하고 사이드바 메뉴에서 '해제'를 누르세요. <b>각 기술 티어와 시대가 높아질수록 더 많은 과학이 필요하지만, 과학을 얻는 새로운 방법들도 해제됩니다.</b>",
   AdvisorScienceTitle: "과학적 발견!",
   AdvisorSkipAllTutorials: "모든 튜토리얼 건너뛰기",
   AdvisorStorageContent:
      "건물에는 적절한 양의 저장 공간이 있지만, 오랜 시간 동안 가동을 중단하면 저장 공간이 가득 찰 수 있습니다. <b>건물이 가득 차면 더 이상 생산할 수 없습니다</b>. 이는 이미 많은 재고가 쌓여 있다는 의미이기 때문에 큰 문제가 아닐 수도 있습니다. 그러나 일반적으로 생산을 유지하는 것이 더 좋습니다.<br><br>가득 찬 저장 공간을 해결하는 한 가지 방법은 창고를 활용하는 것입니다. 창고를 건설하면 발견한 모든 제품 목록이 표시되며, 창고 레벨과 저장 배율에 따라 설정한 수량 내에서 다양한 제품을 가져올 수 있습니다.<br><br>간단히 창고 설정을 하는 방법은 가져오고자 하는 각 제품에 체크 표시를 하고, '선택 항목 간 재분배' 버튼을 사용하여 가져오는 비율과 저장 공간을 균등하게 나누는 것입니다. 또한, 건물이 창고에서 제품을 가져갈 수 있도록 하려면 '최대 수량 이하로 내보내기' 옵션을 활성화해야 합니다.",
   AdvisorStorageTitle: "저장소와 창고",
   AdvisorTraditionContent:
      "Some wonders (Chogha Zanbil, Luxor Temple, Big Ben) provide access to a new set of options, allowing you to customize the path of your rebirth. Each one allows you to choose from 1 of 4 options for your civilization's tradtion, religion and ideology respectively.<br><br>Once you choose one, that choice is locked in for that rebirth, though you can pick others in future rebirths. Once chosen, each one can also be upgraded a number of times by providing the necessary resources. The rewards in each tier are cumulative, so Tier 1 giving +1 production to X and Tier 2 giving +1 production to X means at Tier 2 you will have +2 production to X in total.",
   AdvisorTraditionTitle: "Choosing Paths and Upgradeable Wonders",
   AdvisorWonderContent:
      "Wonders are special buildings that provide global effects which can have a significant impact on your gameplay. In addition to their listed functions, all Wonders give +1 Happiness as well. You need to be careful though, as <b>Wonders require a LOT of materials, and have a higher than normal Builder Capacity as well</b>. This means that they can easily clear out your stockpiles of needed inputs, leaving your other buildings starving. <b>You can turn each input on and off freely</b>, allowing you to build it in stages while you stockpile enough materials to keep everything running.",
   AdvisorWonderTitle: "Wonders Of The World",
   AdvisorWorkerContent:
      "Every time a building produces or transports goods, this requires workers. If you don't have enough workers available, some buildings will fail to run that cycle. The obvious fix for this is to increase your total available workers by building or upgrading structures that make workers (Hut/House/Apartment/Condo).<br><br><b>Be aware though, that buildings turn off while upgrading, and can't provide any of their resources, which includes workers, so you might want to only upgrade one housing building at a time.</b> A good goal for the early stages of the game is to keep aboput 70% of your workers busy. If more than 70% are busy, upgrade/build housing. If fewer than 70% are busy, expand production.",
   AdvisorWorkerTitle: "노동자 관리",
   Aeschylus: "아이스킬로스",
   Agamemnon: "아가멤논",
   AgeWisdom: "Age Wisdom",
   AgeWisdomDescHTML: "Each level of Age Wisdom provides <b>an equivalent level</b> of eligible Permanent Great People of that age - it can be upgraded with eligible Permanent Great People shards",
   AgeWisdomGreatPeopleShardsNeeded: "You need %{amount} more great people shards for the next Age Wisdom upgrade",
   AgeWisdomGreatPeopleShardsSatisfied: "You have enough great people shards for the next Age Wisdom upgrade",
   AgeWisdomNeedMoreGreatPeopleShards: "Need More Great People Shards",
   AgeWisdomNotEligible: "This Great Person is not eligible for Age Wisdom",
   AgeWisdomSource: "%{age} Wisdom: %{person}",
   AgeWisdomUpgradeWarningHTMLV3: "Age Wisdom <b>does not carry over</b> when upgrading from Tribune to Quaestor",
   AGreatPersonIsBorn: "위인이 태어났습니다.",
   AircraftCarrier: "항공 모함",
   AircraftCarrierYard: "항공 모함 조선소",
   Airplane: "비행기",
   AirplaneFactory: "비행기 공장",
   Akitu: "Akitu: Ziggurat Of Ur and Euphrates River apply to buildings unlocked in the current age",
   AlanTuring: "앨런 튜링",
   AlanTuringDesc: "쉬고 있는 노동자당 +%{value} 과학 생산",
   AlbertEinstein: "알베르트 아인슈타인",
   Alcohol: "알코올",
   AldersonDisk: "엘더슨 디스크",
   AldersonDiskDesc: "+25 행복도. 엘더슨 디스크는 업그레이드가 가능하며 업그레이드를 완료 할 때마다 +5 행복도를 추가로 제공됩니다.",
   Alloy: "합금",
   Alps: "알프스 산맥",
   AlpsDesc: "건물의 매 10레벨마다 +1의 생산용량을 얻습니다.(+1의 소비 배수, +1의 생산 배수)",
   Aluminum: "알루미늄",
   AluminumSmelter: "알루미늄 제련소",
   AmeliaEarhart: "아멜리아 에어하트",
   American: "미국",
   AndrewCarnegie: "앤드류 카네기",
   AngkorWat: "앙코르 와트",
   AngkorWatDesc: "모든 인접한 건물이 +1의 노동자 능력 배수를 얻습니다. 1000명의 노동자를 생산합니다.",
   AntiCheatFailure: "<b>치트 방지 검사를 통과하지 못하여</b> 귀하의 계정 순위가 제한되었습니다. 이의를 제기하려면 개발자에게 문의하세요.",
   AoiMatsuri: "아오리 마츠리: 후지산이 두배의 Warp를 생성합니다.",
   Apartment: "아파트",
   Aphrodite: "아프로디테",
   AphroditeDescV2: "+1 Builder Capacity Multiplier for each level when upgrading buildings over Level 20. All unlocked Classical Age permanent great people get +1 level this run",
   ApolloProgram: "아폴로 프로그램",
   ApolloProgramDesc: "모든 로켓 공장은 생산, 노동자 능력, 저장소 배수 +2를 얻습니다. 위성 공장, 우주선 공장, 핵 미사일 사일로는 인접한 로켓 공장마다 생산 배수 +1을 얻습니다.",
   ApplyToAll: "일괄 적용",
   ApplyToAllBuilding: "일괄 적용 %{building}",
   ApplyToBuildingInTile: "%{tile} 타일 내 모든 %{building}에 적용",
   ApplyToBuildingsToastHTML: "<b>%{count} 개의 %{building}</b>에 성공적으로 적용되었습니다.",
   Aqueduct: "수로",
   ArcDeTriomphe: "에투알 개선문",
   ArcDeTriompheDescV2: "Every 1 happiness (capped) provides +1 builder capacity to all buildings",
   Archimedes: "아르키메데스",
   Architecture: "건축학",
   Aristophanes: "아리스토파네스",
   AristophanesDesc: "+%{value} 행복도",
   Aristotle: "아리스토텔레스",
   Arithmetic: "산수",
   Armor: "갑옷",
   Armory: "무기고",
   ArtificialIntelligence: "인공지능",
   Artillery: "대포",
   ArtilleryFactory: "포 공장",
   AshokaTheGreat: "아쇼카 대왕",
   Ashurbanipal: "아슈르바니팔",
   Assembly: "조립",
   Astronomy: "천문학",
   AtomicBomb: "원자 폭탄",
   AtomicFacility: "핵 물질 재처리시설",
   AtomicTheory: "원자론",
   Atomium: "아토미움",
   AtomiumDescV2: "2타일 범위 내에서 과학을 생산하는 모든 건물은 +5 생산 배수를 얻습니다. 2타일 범위 내의 과학 생산량과 동일한 과학을 생성합니다. 완료되면 가장 비싼 잠금 해제 기술 비용에 해당하는 과학을 생성합니다.",
   Autocracy: "독재",
   Aviation: "비행",
   Babylonian: "바빌론",
   BackToCity: "도시로 돌아가기",
   BackupRecovery: "백업 복구",
   Bakery: "제빵소",
   Ballistics: "탄도학",
   Bank: "은행",
   Banking: "은행업",
   BankingAdditionalUpgrade: "레벨10 이상인 모든 건물은 +1의 저장소 배수를 획득합니다.",
   Banknote: "지폐",
   BaseCapacity: "기초 수송 용량",
   BaseConsumption: "기초 소비",
   BaseMultiplier: "기초 배수",
   BaseProduction: "기초 생산",
   BastilleDay: "Bastille Day: Double the effect of Centre Pompidou and Arc de Triomphe. Double the Culture generation from Mont Saint-Michel",
   BatchModeTooltip: "현재 %{count}개의 건물이 선택되어 있습니다. 업그레이드는 선택한 모든 건물에 적용됩니다.",
   BatchSelectAllSameType: "모든 동일한 건물",
   BatchSelectAnyType1Tile: "1타일 내의 모든 건물",
   BatchSelectAnyType2Tile: "2타일 내의 모든 건물",
   BatchSelectAnyType3Tile: "3타일 내의 모든 건물",
   BatchSelectSameType1Tile: "1타일 내의 동일 건물",
   BatchSelectSameType2Tile: "2타일 내의 동일 건물",
   BatchSelectSameType3Tile: "3타일 내의 동일 건물",
   BatchSelectSameTypeSameLevel: "동일 레벨과 동일 건물",
   BatchSelectThisBuilding: "이 건물만",
   BatchStateSelectActive: "Active",
   BatchStateSelectAll: "All",
   BatchStateSelectTurnedFullStorage: "Full Storage",
   BatchStateSelectTurnedOff: "Turned Off",
   BatchUpgrade: "일괄 업그레이드",
   Battleship: "전함",
   BattleshipBuilder: "전함 건조대",
   BigBen: "빅 벤",
   BigBenDesc: "활동 중인 노동자로부터 과학 +2 획득. 제국의 이념을 선택하고, 선택할 때마다 더 많은 부스트를 해제하세요.",
   Biplane: "복엽기",
   BiplaneFactory: "복엽기 공장",
   Bitcoin: "비트코인",
   BitcoinMiner: "비트코인 채굴기",
   BlackForest: "검은 숲",
   BlackForestDesc: "발견되면 지도에 있는 모든 나무 타일이 발견됩니다. 그리고 인접한 타일에 나무가 생성됩니다. 나무나 목제를 소비하는 건물은 생산 배수 +5를 얻습니다.",
   Blacksmith: "대장장이",
   Blockchain: "블록체인",
   BlueMosque: "Blue Mosque",
   BlueMosqueDesc: "All wonders provide +1 Production, Worker Capacity and Storage Multiplier to adjacent buildings. When constructed next to Hagia Sophia, provide extra +1 Production, Worker Capacity and Storage Multiplier",
   BobHope: "밥 호프",
   BobHopeDesc: "+%{value} 행복도",
   Bond: "채권",
   BondMarket: "채권 시장",
   Book: "책",
   BoostCyclesLeft: "Boost Cycles Left",
   BoostDescription: "+%{value} %{multipliers} 가 %{buildings} 에 적용됨",
   Borobudur: "보로부드르",
   BorobudurDesc: "보로부드르",
   BranCastle: "브란 성",
   BranCastleDesc: "브란 성",
   BrandenburgGate: "브란덴부르크 문",
   BrandenburgGateDesc: "모든 석탄광산과 유정이 +1의 생산, 저장소, 노동자 능력 배수를 얻습니다. 정유 공장이 인접한 석유 타일 하나당 +1의 생산, 저장소, 노동자 당 생산량 배수를 얻습니다.",
   Bread: "빵",
   Brewery: "양조장",
   Brick: "벽돌",
   Brickworks: "벽돌 공장",
   BritishMuseum: "British Museum",
   BritishMuseumChooseWonder: "Choose a Wonder",
   BritishMuseumDesc: "After constructed, can transform into to a unique wonder from other civilizations",
   BritishMuseumTransform: "Transform",
   Broadway: "브로드웨이",
   BroadwayCurrentlySelected: "Currently selected",
   BroadwayDesc: "현 시대의 위인과 이전 시대의 위인이 탄생합니다. 위인을 선택하면 해당 위인의 효과가 두배가 됩니다.",
   BronzeAge: "청동기시대",
   BronzeTech: "구리",
   BuddhismLevelX: "불교 %{level}",
   Build: "건설",
   BuilderCapacity: "건축가 수송 용량",
   BuildingColor: "건물 색상",
   BuildingColorMatchBuilding: "건물에서 색상 복사",
   BuildingColorMatchBuildingTooltip: "이 자원을 생산하는 건물에서 자원 색상을 복사합니다. 여러개의 건물에서 자원을 생산하면 임의의 건물이 선택됩니다.",
   BuildingDefaults: "건물 기본값",
   BuildingDefaultsCount: "건물 기본값에서 %{count}개의 속성이 재정의되었습니다.",
   BuildingDefaultsRemove: "모든 속성을 기본값으로 복원",
   BuildingEmpireValue: "건물 제국 가치 / 자원 제국 가치",
   BuildingMultipliers: "가속",
   BuildingName: "이름",
   BuildingNoMultiplier: "%{building}은 생산, 노동자 능력, 저장 등의 배수에 <b>영향을 받지 않습니다</b>.",
   BuildingSearchText: "검색할 자원이나 건물을 입력하세요",
   BuildingTier: "티어",
   Cable: "전선",
   CableFactory: "전선 공장",
   Calendar: "달력",
   CambridgeUniversity: "Cambridge University",
   CambridgeUniversityDesc: "+1 Age Wisdom level for Renaissance and ages after",
   CambridgeUniversitySource: "Cambridge University (%{age})",
   Cancel: "취소",
   CancelAllUpgradeDesc: "Cancel all %{building} upgrades",
   CancelUpgrade: "업그레이드 취소",
   CancelUpgradeDesc: "이미 수송된 자원은 저장소에 저장됩니다.",
   Cannon: "대포",
   CannonWorkshop: "대포 공장",
   CannotEarnPermanentGreatPeopleDesc: "실험 회차에서는 영구 위인들을 얻을 수 없습니다",
   Capitalism: "자본주의",
   Cappadocia: "Cappadocia",
   CappadociaDesc: "All buildings within 3 tile range get +1 Production, Worker Capacity and Storage Multiplier for every level above Level 30",
   Car: "자동차",
   Caravansary: "상인 숙소",
   CaravansaryDesc: "다른 플레이어와 자원을 거래할수 있으며, 추가 저장공간을 제공합니다.",
   Caravel: "캐러벨",
   CaravelBuilder: "캐러벨 조선소",
   CarFactory: "자동차 공장",
   CarlFriedrichGauss: "칼 프리드리히 가우스",
   CarlFriedrichGaussDesc: "쉬고 있는 노동자당 과학 생산 +%{idle}. 노동 중인 노동자당 과학 생산 +%{busy}",
   CarlSagan: "칼 세이건",
   Census: "인구조사",
   CentrePompidou: "Centre Pompidou",
   CentrePompidouDesc:
      "Once constructed, all buildings get +1 Production and +2 Storage Multiplier. The wonder will persist if the current run reaches Information Age and the next run is a different civilization. The wonder gets +1 level at rebirth for each run that reaches Information Age with a unique civilization. Each level provides +1 Production and +2 Storage Multiplier. The value of this wonder is excluded from total empire value and British Museum cannot transform into this wonder",
   CentrePompidouWarningHTML: "Centre Pompidou will disappear if you rebirth as <b>%{civ}</b>",
   CerneAbbasGiant: "Cerne Abbas Giant",
   CerneAbbasGiantDesc: "A great person of the current age is born when a wonder is constructed",
   ChangePlayerHandle: "변경",
   ChangePlayerHandleCancel: "닫기",
   ChangePlayerHandledDesc: "플레이어 핸들값은 5~16개의 알파벳과 숫자만 포함할수 있으며, 중복될 수 없습니다.",
   Chariot: "전차",
   ChariotWorkshop: "전차 공장",
   Charlemagne: "카롤루스 대제",
   CharlesDarwin: "찰스 다윈",
   CharlesDarwinDesc: "+%{value} 노동 중인 노동자당 과학 생산",
   CharlesMartinHall: "찰스 마틴 홀",
   CharlesParsons: "찰스 파슨스",
   CharlieChaplin: "찰리 채플린",
   CharlieChaplinDesc: "+%{value} 행복도",
   Chat: "채팅",
   ChatChannel: "채팅 채널",
   ChatChannelLanguage: "언어",
   ChatHideLatestMessage: "최신 메시지 내용 숨기기",
   ChatNoMessage: "채팅 메시지가 없습니다",
   ChatReconnect: "연결해제되었습니다, 재연결중...",
   ChatSend: "보내기",
   CheckInAndExit: "Check In And Exit",
   CheckInCloudSave: "Check In Save",
   CheckOutCloudSave: "Check Out Save",
   Cheese: "치즈",
   CheeseMaker: "치즈공장",
   Chemistry: "화학",
   ChesterWNimitz: "체스터 니미츠",
   ChichenItza: "치첸 이트사",
   ChichenItzaDesc: "모든 인접한 건물이 +1의 생산, 저장소, 노동자 능력 배수를 얻습니다.",
   Chinese: "중국",
   ChoghaZanbil: "초가잔빌 지구라트",
   ChoghaZanbilDescV2: "제국 전통을 선택하고 각 선택마다 더 많은 부스트를 잠금 해제하십시오.",
   ChooseGreatPersonChoicesLeft: "%{count}개의 선택사항이 남았습니다.",
   ChristianityLevelX: "기독교 %{level}",
   Church: "교회",
   CircusMaximus: "키르쿠스 막시무스",
   CircusMaximusDescV2: "+5 행복도. 모든 음악가 길드, 작가 길드, 화가 길드가 생산 및 저장소 배수를 +1 얻습니다.",
   CityState: "도시 국가",
   CityViewMap: "도시",
   CivGPT: "CivGPT",
   CivIdle: "CivIdle",
   CivIdleInfo: "Fish Pond Studio에서 자랑스럽게 선보입니다.",
   Civilization: "Civilization",
   CivilService: "행정 업무",
   CivOasis: "CivOasis",
   CivTok: "CivTok",
   ClaimedGreatPeople: "위인 획득",
   ClaimedGreatPeopleTooltip: "환생 시 %{total}명의 위인이 있으며, 그 중 이미 %{claimed}명의 소유권을 주장했습니다.",
   ClassicalAge: "고전시대",
   ClearAfterUpdate: "시장 갱신후 모든 거래 초기화",
   ClearSelected: "선택한 항목 초기화",
   ClearSelection: "초기화",
   ClearTransportPlanCache: "운송 계획 캐시 지우기",
   Cleopatra: "클레오파트라",
   CloneFactory: "클론 공장",
   CloneFactoryDesc: "모든 자원을 클론합니다.",
   CloneFactoryInputDescHTML: "클론 공장은 <b>%{buildings}</b>에서 직접 운송된 <b>%{res}</b>만 클론할 수 있습니다.",
   CloneLab: "클론 연구소",
   CloneLabDesc: "모든 자원을 과학으로 변환합니다.",
   CloneLabScienceMultiplierHTML: "<b>과학 생산 건물에만 적용되는</b> 생산 배율(예: 아토미움의 생산 배율)은 <b>클론 연구소에는 적용되지 않습니다.</b>",
   Cloth: "옷감",
   CloudComputing: "클라우드 컴퓨팅",
   CloudSaveRefresh: "새로고침",
   CloudSaveReturnToGame: "겜으로 돌아가기",
   CNTower: "CN 타워",
   CNTowerDesc: "모든 영화 스튜디오, 라디오 방송국 및 TV 방송국은 행복도 -1이 면제됩니다. 세계대전 시대과 냉전 시대에서 잠금 해제된 모든 건물은 +N 생산, 노동자 능력 및 저장소 배수를 얻습니다. N = 건물의 티어와 시대의 차이",
   Coal: "석탄",
   CoalMine: "석탄 광산",
   CoalPowerPlant: "석탄 발전소",
   Coin: "동전",
   CoinMint: "동전 주조소",
   ColdWarAge: "냉전 시대",
   CologneCathedral: "쾰른 대성당",
   CologneCathedralDesc: "건설 시, 현시대에서 가장 비싼 기술의 해당하는 과학을 즉시 획득합니다. 모든 과학 생성 건물(클론 연구소 제외)은 생산 배수 +1을 얻습니다. 쾰른 대성당은 업그레이드 가능하며 추가 업그레이드를 완료 할때 마다 과학 생산 건물(클론 연구소 제외)은 생산 배수 +1을 추가로 얻습니다.",
   Colonialism: "식민주의",
   Colosseum: "콜로세움",
   ColosseumDescV2: "전차 공장은 -1 행복도에서 면제됩니다. 전차 10대를 소비하여 행복도 10을 생산합니다. 해제된 각 시대마다 추가로 행복도 2를 제공합니다.",
   ColossusOfRhodes: "로도스의 거상",
   ColossusOfRhodesDesc: "노동자를 생산하지 않는 모든 인접한 건물이 +1의 행복도를 얻습니다.",
   Combustion: "연소",
   Commerce4UpgradeHTMLV2: "잠금 해제되면 모든 <b>인접 은행</b>이 <b>레벨 30</b>으로 무료 업그레이드됩니다.",
   CommerceLevelX: "상업 %{level}",
   Communism: "공산주의",
   CommunismLevel4DescHTML: "<b>산업혁명 시대</b>의 위인과 <b>세계대전 시대</b>의 위인이 탄생합니다.",
   CommunismLevel5DescHTML: "<b>냉전 시대</b>의 위인이 탄생합니다. 새로운 시대로 진입할 때마다 해당 시대의 위인 <b>2명을 추가로</b> 획득합니다.",
   CommunismLevelX: "공산주의 레벨 %{level}",
   Computer: "컴퓨터",
   ComputerFactory: "컴퓨터 공장",
   ComputerLab: "컴퓨터 연구소",
   Concrete: "콘크리트",
   ConcretePlant: "콘크리트 공장",
   Condo: "콘도",
   ConfirmDestroyResourceContent: "%{amount} %{resource} 를 파괴하려고 합니다. 취소 할수 없습니다",
   ConfirmNo: "아니오",
   ConfirmYes: "예",
   Confucius: "공자",
   ConfuciusDescV2: "50% 이상의 노동자가 노동 중이고 노동 중인 노동자의 50% 미만이 운송 분야에 노동하는 경우 모든 노동자의 과학 생산 +%{value}",
   ConnectToADevice: "Connect To A Device",
   Conservatism: "보수주의",
   ConservatismLevelX: "보수주의 레벨 %{level}",
   Constitution: "헌법",
   Construction: "건축",
   ConstructionBuilderBaseCapacity: "기초 수송 용량",
   ConstructionBuilderCapacity: "건축가 수송 용량",
   ConstructionBuilderMultiplier: "수송 용량 배수",
   ConstructionBuilderMultiplierFull: "건축가 용량 배수",
   ConstructionCost: "건설 비용: %{cost}",
   ConstructionDelivered: "운송됨",
   ConstructionPriority: "건축 우선순위",
   ConstructionProgress: "진척도",
   ConstructionResource: "자원",
   Consume: "소비",
   ConsumeResource: "소비: %{resource}",
   ConsumptionMultiplier: "소비 배수",
   ContentInDevelopment: "개발중인 컨텐츠",
   ContentInDevelopmentDesc: "이 게임 콘텐츠는 아직 개발 중이며 향후 게임 업데이트에서 사용할수 있습니다. 지켜봐주세요!",
   Copper: "구리",
   CopperMiningCamp: "구리 광산",
   CosimoDeMedici: "코시모 데 메디치",
   Cotton: "목화",
   CottonMill: "방직 공장",
   CottonPlantation: "목화 농장",
   Counting: "계산",
   Courthouse: "법원",
   CristoRedentor: "구세주 그리스도상",
   CristoRedentorDesc: "주변 2타일 범위 내 모든 건물은 -1 행복도에서 면제됩니다.",
   CrossPlatformAccount: "Platform Account",
   CrossPlatformConnect: "Connect",
   CrossPlatformSave: "Cross Platform Save",
   CrossPlatformSaveLastCheckIn: "Last Check In",
   CrossPlatformSaveStatus: "Current Status",
   CrossPlatformSaveStatusCheckedIn: "Checked In",
   CrossPlatformSaveStatusCheckedOut: "Checked Out on %{platform}",
   CrossPlatformSaveStatusCheckedOutTooltip: "Your cross platform save has been checked out on another platform, you have to check in on that platform before you can check out on this platform",
   Cultivation4UpgradeHTML: "<b>르네상스 시대</b>의 위인이 탄생합니다.",
   CultivationLevelX: "재배 %{level}",
   Culture: "문화",
   Culus: "Cülus: Double the effect of Cappadocia. Mount Ararat's effect becomes based on square root of Effective Great People Level, instead of cubic root",
   CurrentLanguage: "한국어",
   CurrentPlatform: "Current Platform",
   CursorBigOldFashioned: "3D (대형)",
   CursorOldFashioned: "3D",
   CursorStyle: "커서 스타일",
   CursorStyleDescHTML: "커서 스타일을 변경합니다. <b>적용하려면 게임을 다시 시작해야 합니다.</b>",
   CursorSystem: "시스템",
   Cycle: "Cycle",
   CyrusII: "키루스 2세",
   DairyFarm: "낙농장",
   DefaultBuildingLevel: "기본 건축 레벨",
   DefaultConstructionPriority: "기본 건축 우선순위",
   DefaultProductionPriority: "기본 생산 우선순위",
   DefaultStockpileMax: "기본 최대 비축량",
   DefaultStockpileSettings: "기본 비축 투입 용량",
   DeficitResources: "부족한 자원",
   Democracy: "민주주의",
   DemolishAllBuilding: "%{tile} 안에 있는 모든 %{building}(을)를 파괴 합니다.",
   DemolishAllBuildingConfirmContent: "Are you sure about demolishing %{count} %{name}?",
   DemolishAllBuildingConfirmTitle: "Demolish %{count} Building(s)?",
   DemolishBuilding: "건물 파괴",
   DennisRitchie: "데니스 리치",
   Deposit: "자원 매장층",
   DepositTileCountDesc: "%{count} 개의  %{deposit} 타일이 %{city} 에서 발견됩니다.",
   Dido: "디도",
   Diplomacy: "외교",
   DistanceInfinity: "무제한",
   DistanceInTiles: "거리 (타일 내)",
   DolmabahcePalace: "Dolmabahçe Palace",
   Drilling: "시추",
   DukeOfZhou: "주문공",
   DuneOfPilat: "Dune of Pilat",
   DuneOfPilatDesc: "In each age, double the age wisdom for the previous age",
   DynamicMultiplierTooltip: "이 배율은 변할 수 있습니다 - 노동자와 저장소 배율에는 영향을 주지 않습니다.",
   Dynamite: "다이너마이트",
   DynamiteWorkshop: "다이너마이트 공장",
   DysonSphere: "다이슨 구체",
   DysonSphereDesc: "모든 건물이 +5 생산 배율을 얻습니다. 이 불가사의는 업그레이드할 수 있으며, 각 추가 업그레이드는 모든 건물에 +1 생산 배율을 제공합니다.",
   EasterBunny: "Easter Bunny",
   EasterBunnyDesc: "Once constructed, 10% of the extra Great People at Rebirth from this run will carry forward to the next run and are born after building the Easter Bunny in the new run. This wonder can only be constructed during April",
   EastIndiaCompany: "East India Company",
   EastIndiaCompanyDescV2:
      "This wonder accumulates the total value of your completed player trade transactions. For every 2,000 trade value, all buildings adjacent to caravansaries get a +0.5 Production Multiplier for 1 cycle. This wonder can be upgraded and each upgrade provides an additional +0.5 Production Multiplier. A trade transaction counts when you either fulfill another player's trade request or when your own trade request is fulfilled. Multiple boosts stack by extending the duration",
   Education: "교육",
   EffectiveGreatPeopleLevel: "Effective Great People Level",
   EffectiveGreatPeopleLevelDesc: "Effective great people level is the sum of all permanent great people level and age wisdom level. It measures the effect boost provided by great people and age wisdom",
   Egyptian: "이집트",
   EiffelTower: "에펠탑",
   EiffelTowerDesc: "모든 인접한 제강공장이 +N 의 생산, 저장소, 노동자 능력 배수를 얻습니다. N = 인접한 제강공장의 수",
   Elbphilharmonie: "엘필하모니",
   ElbphilharmonieDesc: "All buildings within 3 tile range get +1 Production Multiplier for each adjacent working building that has different tier",
   Electricity: "전력",
   Electrification: "전기화",
   ElectrificationPowerRequired: "전력 필요",
   ElectrificationStatusActive: "활성화",
   ElectrificationStatusDesc: "전력이 필요한 건물과 전력이 필요하지 않은 건물 모두 전기화 할 수 있습니다. 그러나 전력이 필요한 건물은 더 높은 전기화 효율을 제공합니다.",
   ElectrificationStatusNoPowerV2: "전력 부족",
   ElectrificationStatusNotActive: "활성화되지 않음",
   ElectrificationStatusV2: "전기화 현황",
   ElectrificationUpgrade: "전기화 해금. 건물이 전력을 소모하여 생산을 늘릴 수 있습니다.",
   Electrolysis: "전기분해",
   ElvisPresley: "엘비스 프레슬리",
   ElyseePalace: "Élysée Palace",
   EmailDeveloper: "개발자 이메일",
   Embassy: "대사관",
   EmperorWuOfHan: "한무제",
   EmpireValue: "제국 가치",
   EmpireValueByHour: "시간 당 제국 가치",
   EmpireValueFromBuilding: "건물이 제공하는 제국 가치",
   EmpireValueFromBuildingsStat: "건물이 제공",
   EmpireValueFromResources: "자원이 제공",
   EmpireValueFromResourcesStat: "자원이 제공",
   EmpireValueIncrease: "제국 가치 증가",
   EmptyTilePageBuildLastBuilding: "마지막에 지었던 건물 건축",
   EndConstruction: "건축 종료",
   EndConstructionDescHTML: "건축을 종료해도, 이미 사용한 자원은 <b>돌려받을 수 없습니다</b>",
   Engine: "엔진",
   Engineering: "공학",
   English: "English",
   Enlightenment: "계몽",
   Enrichment: "농축",
   EnricoFermi: "엔리코 페르미미",
   EstimatedTimeLeft: "남은 예상 시간",
   EuphratesRiver: "유프라테스 강",
   EuphratesRiverDesc:
      "생산에 투입된 노동자(운송 중이 아닌)가 10%마다 노동자를 생산하지 않는 모든 건물에 +1 생산 배율을 제공합니다 (최대 = 해제된 시대 수 / 2). 주변에 바빌론의 공중정원이 건설되면, 바빌론의 공중정원은 해제된 이후의 각 시대마다 +1 효과를 얻습니다. 발견 시 인접한 타일에 자원이 없는 경우 모든 인접한 타일에 물을 생성합니다.",
   ExpansionLevelX: "확장 %{level}",
   Exploration: "탐험",
   Explorer: "탐험가",
   ExplorerRangeUpgradeDesc: "탐험가의 범위를 %{range}로 증가시킵니다.",
   ExploreThisTile: "탐험가 파견",
   ExploreThisTileHTML: "탐험가는 <b>선택한 타일과 인접한 타일</b>을 탐색합니다. 탐험가는 %{name}에서 생성됩니다. %{count}명의 탐험가가 남았습니다.",
   ExtraGreatPeople: "%{count}명의 추가 위인",
   ExtraGreatPeopleAtReborn: "환생시 추가 위인",
   ExtraTileInfoType: "추가 타일 정보",
   ExtraTileInfoTypeDesc: "각 타일 아래에 표시할 정보를 선택하세요.",
   ExtraTileInfoTypeEmpireValue: "제국 가치",
   ExtraTileInfoTypeNone: "없음",
   ExtraTileInfoTypeStoragePercentage: "저장소 용량 비율(%)",
   Faith: "신앙",
   Farming: "농업",
   FavoriteBuildingAdd: "즐겨찾기 추가",
   FavoriteBuildingEmptyToast: "즐겨찾기에 추가된 건물이 없습니다.",
   FavoriteBuildingRemove: "즐겨찾기 삭제",
   FeatureRequireQuaestorOrAbove: "This feature requires Quaestor rank or above",
   Festival: "축제",
   FestivalCycle: "축제 주기",
   FestivalTechTooltipV2: "Positive Happiness (max. 50) is converted into festival points. For every %{point} festival points, your empire enters a festival cycle, granting a significant map-specific boost. The festival on this map is %{desc}",
   FestivalTechV2: "Unlock festival - positive Happiness (max. 50) is converted into festival points. For every %{point} festival points, your empire enters a festival cycle, granting a significant map-specific boost",
   Feudalism: "봉건제",
   Fibonacci: "피보나치",
   FibonacciDescV2: "쉬고 있는 노동자 과학 생산 +%{idle}. 노동중인 노동자 과학 생산 +%{busy}. 피보나치의 영구 업그레이드 비용은 피보나치 수열을 따릅니다.",
   FighterJet: "전투기",
   FighterJetPlant: "전투기 공장",
   FilterByAge: "시대로 필터링",
   FinancialArbitrage: "금융 차익 거래",
   FinancialLeverage: "금융 레버리지",
   Fire: "불",
   Firearm: "화기",
   FirstTimeGuideNext: "다음",
   FirstTimeTutorialWelcome: "CivIdle에 오신 것을 환영 합니다.",
   FirstTimeTutorialWelcome1HTML:
      "CivIdle에 오신 것을 환영합니다. 이 게임에서는 자신만의 제국을 운영하게 됩니다:<b>제작 관리, 기술 잠금 해제, 다른 플레이어와 자원 교환, 위인을 생성하고 세계 불가사의를 건설하세요</b>.<br>마우스를 끌어서 이동하세요. 스크롤 휠을 사용하여 확대하거나 축소하세요. 빈 타일을 클릭하여 새 건물을 짓고 건물을 클릭하여 점검하세요.<br><br>벌목장이나 채석장 같은 특정 건물은 해당 자원 타일 위에 지어야 합니다. 안개 옆에 노동자를 제공하는 오두막을 두는 것이 좋습니다. 건물을 짓는 데 시간이 좀 걸립니다. 완료 후에는 근처 안개가 드러납니다.",
   FirstTimeTutorialWelcome2HTML:
      "건물을 업그레이드하려면 자원이 많이 들고 시간이 걸립니다. 건물을 업그레이드 할 때는 <b>생산이 중단 됩니다</b>. 여기에는 노동자를 제공하는 건물도 포함되므로 <b>모든 건물을 동시에 업그레이드 하지 마세요!</b><br><br>제국이 성장함에 따라 더 많은 과학 기술을 습득하고 새로운 기술을 개발 할 수 있습니다. 도착하면 자세한 내용을 알려드리겠지만 화면 > 연구를 통해 빠르게 확인 할 수 있습니다.",
   FirstTimeTutorialWelcome3HTML: "이제 겜의 모든 기본 사항을 알게 되었으니 제국을 건설 할 수 있습니다. 하지만 제가 당신을 보내기 전에, <b>플레이어 정보를 선택</b>하고 게임 내 채팅에서 인사해보세요. 우리는 놀라운 정도의 유용한 정보를 얻을 수 있는 채팅 커뮤니티가 있습니다: 길을 잃었을때 주저하지 말고 물어보세요!",
   Fish: "물고기",
   FishPond: "물고기 연못",
   FlorenceNightingale: "플로렌스 나이팅게일",
   FlorenceNightingaleDesc: "+%{value} 행복도",
   Flour: "밀가루",
   FlourMill: "제분소",
   FontSizeScale: "폰트 크기 배율",
   FontSizeScaleDescHTML: "게임 UI의 글꼴 크기 배율을 변경합니다. <b>배율을 1x보다 크게 설정하면 일부 UI 레이아웃이 손상될 수 있습니다.</b>",
   ForbiddenCity: "자금성",
   ForbiddenCityDesc: "모든 제지소, 인쇄소, 작가 길드가 +1의 생산, 저장소, 노동자 능력 배수를 얻습니다",
   Forex: "외환",
   ForexMarket: "외환 시장",
   FrankLloydWright: "프랭크 로이드 라이트",
   FrankLloydWrightDesc: "+%{value} 건축가 용량 배수",
   FrankWhittle: "프랭크 휘틀",
   FreeThisWeek: "이번 주 무료",
   FreeThisWeekDescHTMLV2: "<b>매주</b>, 프리미엄 문명 중 하나를 무료 플레이 가능합니다. 이번주 무료 문명은 <b>%{city}</b> 입니다.",
   French: "French",
   Frigate: "프리깃",
   FrigateBuilder: "프리깃 조선소",
   Furniture: "화로",
   FurnitureWorkshop: "화로 공장",
   Future: "Future",
   GabrielGarciaMarquez: "가브리엘 가르시아 마르케스",
   GabrielGarciaMarquezDesc: "+%{value} 행복도",
   GalileoGalilei: "갈릴레오 갈릴레이",
   GalileoGalileiDesc: "쉬고 있는 노동자 과학 생산 +%{value}",
   Galleon: "갤리온",
   GalleonBuilder: "갤리온 조선소",
   Gameplay: "일반",
   Garment: "의류",
   GarmentWorkshop: "의류 공장",
   GasPipeline: "가스관",
   GasPowerPlant: "가스 발전소",
   GatlingGun: "개틀링건",
   GatlingGunFactory: "개틀링건 공장",
   Genetics: "유전학",
   Geography: "지리학",
   GeorgeCMarshall: "조지 C. 마샬",
   GeorgeWashington: "조지 워싱턴",
   GeorgiusAgricola: "게오르기우스 아그리콜라",
   German: "독일",
   Glass: "유리공장",
   Glassworks: "유리공장",
   GlobalBuildingDefault: "전역 건물 기본값",
   Globalization: "세계화",
   GoBack: "뒤로가기",
   Gold: "금",
   GoldenGateBridge: "금문교",
   GoldenGateBridgeDesc: "모든 발전소는 +1 생산량 배수를 얻습니다. 2타일 범위 내의 모든 타일에 전원 공급",
   GoldenPavilion: "금각사",
   GoldenPavilionDesc: "3타일 범위 내 모든 건물은 인접한 건물이 소비하는 자원을 생산할 때마다 +1 생산 배율을 얻습니다 (클론 연구소와 클론 공장은 제외되며, 해당 건물은 비활성화할 수 없습니다).",
   GoldMiningCamp: "금 광산",
   GordonMoore: "고든 무어",
   GrandBazaar: "그랜드 바자르",
   GrandBazaarDesc: "모든 시장을 한곳에서 통제하세요! 모든 인접한 건물은 +5 저장소 배수를 얻습니다.",
   GrandBazaarFilters: "필터",
   GrandBazaarFilterWarningHTML: "시장 거래가 표시되기 전에 필터를 선택해야 합니다.",
   GrandBazaarFilterYouGet: "매입",
   GrandBazaarFilterYouPay: "매매",
   GrandBazaarSeach: "Search",
   GrandBazaarSearchGet: "Get",
   GrandBazaarSearchPay: "Pay",
   GrandBazaarTabActive: "활성",
   GrandBazaarTabTrades: "거래",
   GrandCanyon: "그랜드 캐니언",
   GrandCanyonDesc: "현 시대에 잠금 해제된 건물은 생산 배수 +2를 얻습니다. J.P. 모건의 효과가 2배로 증가됩니다.",
   GraphicsDriver: "그래픽 드라이버: %{driver}",
   GreatDagonPagoda: "슈웨다곤 파고다",
   GreatDagonPagodaDescV2: "모든 불탑은 -1 행복도에서 면제됩니다. 모든 불탑의 신앙 생산에 따라 과학을 생산합니다",
   GreatMosqueOfSamarra: "사마라 대모스크",
   GreatMosqueOfSamarraDescV2: "건물 시야 범위 +1. 탐험되지 않은 무작위 1티어 자원 타일 5개를 공개하고 각 타일에 레벨 10 자원 추출 건물이 바로 건설됩니다.",
   GreatPeople: "위인",
   GreatPeopleEffect: "효과",
   GreatPeopleFilter: "위인을 필터링하려면 이름이나 시대를 입력하세요.",
   GreatPeopleName: "이름",
   GreatPeoplePermanentColumn: "영구",
   GreatPeoplePermanentShort: "영구",
   GreatPeoplePickPerRoll: "굴림 당 위인 선택",
   GreatPeopleThisRun: "이번 회차에서의 위인",
   GreatPeopleThisRunColumn: "이번 회차",
   GreatPeopleThisRunShort: "이번 회차",
   GreatPersonLevelRequired: "영구 위인 레벨 필요",
   GreatPersonLevelRequiredDescV2: "%{city} 문명은 최소 %{required}의 영구적인 위인 레벨이 필요합니다. 현재 보유중인 영구적인 위인 레벨은 %{current} 입니다.",
   GreatPersonPromotionPromote: "승급",
   GreatPersonThisRunEffectiveLevel: "현재 이 회차에서 %{count} %{person}이(가) 있습니다. 추가 %{person}은 1/%{effect}의 효과를 갖습니다.",
   GreatPersonWildCardBirth: "탄생",
   GreatSphinx: "대 스핑크스",
   GreatSphinxDesc: "2개 타일 내의 모든 티어 II 이상의 건물은 +N 소비, 생산 배수를 얻습니다. N = 동일한 유형의 인접한 건물 수",
   GreatWall: "만리장성",
   GreatWallDesc: "1 타일 범위 내의 모든 건물은 +N 생산, 노동자 능력 및 저장소 배수를 얻습니다. N = 현재 시대와 건물이 처음 잠금 해제된 시대 사이의 시대 수입니다. 자금성 옆에 건설하면 범위가 2타일로 늘어납니다.",
   GreedyTransport: "Construction/Upgrade Greedy Transport",
   GreedyTransportDescHTML: "This will make buildings keep transporting resources even if it has enough resources for the current upgrade, which can make upgrading multiple levels <b>faster</b> but end up transport <b>more resources than needed</b>",
   Greek: "그리스",
   GrottaAzzurra: "카프리 푸른 동굴",
   GrottaAzzurraDescV2: "발견 즉시 모든 1티어 건물이 +5레벨은 얻고, +1의 생산, 저장소 노동자 능력 배수를 얻습니다.",
   Gunpowder: "화약",
   GunpowderMill: "화약 공장",
   GuyFawkesNightV2: "Guy Fawkes Night: East India Company provides double the Production Multiplier to buildings adjacent to caravansaries. Tower Bridge generates great people 20% faster",
   HagiaSophia: "하기아 소피아",
   HagiaSophiaDescV2: "+5 행복도. 생산 능력이 0% 인 건물은 -1 행복도에서 면제됩니다. 게임 진행 중에 생산 중단을 피하기 위해 여분의 행복을 제공하십시오.",
   HallOfFame: "Hall of Fame",
   HallOfSupremeHarmony: "태화전",
   Hammurabi: "함무라비",
   HangingGarden: "바빌론의 공중정원",
   HangingGardenDesc: "+1 건축가 용량 배수. 인접한 수로가 +1의 생산, 저장소, 노동자 능력 배수를 얻습니다.",
   Happiness: "행복도",
   HappinessFromBuilding: "건물로부터 (불가사의 제외)",
   HappinessFromBuildingTypes: "재고가 풍부한 건물로부터",
   HappinessFromHighestTierBuilding: "최고 티어의 건물로부터",
   HappinessFromUnlockedAge: "해금한 시대로 부터",
   HappinessFromUnlockedTech: "연구 해금으로 부터",
   HappinessFromWonders: "불가사의로부터 (자연불가사의 포함)",
   HappinessUncapped: "행복도 (무제한)",
   HarryMarkowitz: "해리 마코위츠",
   HarunAlRashid: "하룬 알 라시드",
   Hatshepsut: "하트셉수트",
   HatshepsutTemple: "하트셉수트의 장제전",
   HatshepsutTempleDesc: "건설완료시 지도상의 모든 물 타일을 밝힙니다. 밀 농장이 인접한 물 타일 하나 당 +1의 생산 배수를 얻습니다.",
   Headquarter: "본부",
   HedgeFund: "헤지 펀드",
   HelpMenu: "지원",
   HenryFord: "헨리 포드",
   Herding: "목축",
   Herodotus: "헤로도토스",
   HighlightBuilding: "Highlight %{building}",
   HimejiCastle: "히메지 성",
   HimejiCastleDesc: "모든 캐러벨 조선소, 갤리온 조선소, 프리깃 조선소가 +1의 생산, 저장소, 노동자 능력 배수를 얻습니다.",
   Hollywood: "할리우드",
   HollywoodDesc: "+5 행복도. 2타일 범위 내에서 문화를 소비하거나 생산하는 재고가 풍부한 건물마다 행복도 +1",
   HolyEmpire: "신성제국",
   Homer: "호머",
   Honor4UpgradeHTML: "<b>정화</b>(위인) 효과 2배",
   HonorLevelX: "명예 %{level}",
   Horse: "말",
   HorsebackRiding: "승마",
   House: "집",
   Housing: "주택",
   Hut: "오두막",
   HydroDam: "수력 댐",
   Hydroelectricity: "수력발전",
   HymanGRickover: "하이먼 G. 리코버",
   IdeologyDescHTML: "제국의 이념으로 <b>자유주의, 보수주의, 사회주의 또는 공산주의</b> 중 하나를 선택하세요. 이념을 선택한 후에는 <b>변경할 수 없습니다</b>. 각 이념 내에서 더 많은 부스트를 해제할 수 있습니다.",
   IMPei: "I. M. Pei",
   IMPeiDesc: "+%{value} 건축가 용량 배수",
   Imperialism: "제국주의",
   ImperialPalace: "황궁",
   IndustrialAge: "산업혁명 시대",
   InformationAge: "정보화 시대",
   InputResourceForCloning: "Input Resource For Cloning",
   InternationalSpaceStation: "International Space Station",
   InternationalSpaceStationDesc: "All buildings get +5 Storage Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Storage Multiplier to all buildings",
   Internet: "인터넷",
   InternetServiceProvider: "인터넷 서비스 제공자",
   InverseSelection: "반전",
   Iron: "철",
   IronAge: "철기시대",
   Ironclad: "철갑함",
   IroncladBuilder: "철갑함 조선소",
   IronForge: "철 대장간",
   IronMiningCamp: "철 광산",
   IronTech: "철",
   IsaacNewton: "아이작 뉴턴",
   IsaacNewtonDescV2: "50% 이상의 노동자의 노동 중이고 노동 중인 노동자의 50% 미만이 운송 분야에 노동하는 경우 모든 노동자의 과학 생산 +%{value}",
   IsambardKingdomBrunel: "이점바드 킹덤 브루넬",
   IsidoreOfMiletus: "밀레토스의 이시도르",
   IsidoreOfMiletusDesc: "+%{value} 건축가 용량 배수",
   Islam5UpgradeHTML: "잠금 해제되면 가장 비싼 <b>산업혁명 시대 기술</b> 비용에 해당하는 일회성 과학을 생성합니다.",
   IslamLevelX: "이슬람 %{level}",
   ItsukushimaShrine: "이쓰쿠시마 신사",
   ItsukushimaShrineDescV2: "한 시대의 과학 연구를 다 하면 다음 시대의 과학 연구 중 가장 저렴한 비용에 해당하는 일회성 과학을 생성합니다.",
   JamesWatson: "제임스 왓슨",
   JamesWatsonDesc: "노동 중인 노동자당 과학 생산 +%{value}",
   JamesWatt: "제임스 와트",
   Japanese: "일본",
   JetPropulsion: "제트 추진",
   JohannesGutenberg: "요하네스 구텐베르크",
   JohannesKepler: "요하네스 케플러",
   JohnCarmack: "존 카맥",
   JohnDRockefeller: "존 D. 록펠러",
   JohnMcCarthy: "존 매카시",
   JohnVonNeumann: "존 폰 노이만",
   JohnVonNeumannDesc: "노동 중인 노동자의 과학 생산 +%{value}",
   JoinDiscord: "Discord 참가",
   JosephPulitzer: "조셉 퓰리처",
   Journalism: "언론",
   JPMorgan: "J.P. 모건",
   JRobertOppenheimer: "J. 로버트 오펜하이머",
   JuliusCaesar: "율리우스 카이사르",
   Justinian: "유스티니아누스 대제",
   Kanagawa: "가나가와",
   KanagawaDesc: "현 시대의 모든 위인은 이번 제국에서 추가 레벨을 얻습니다.(제노비아 제외)",
   KarlMarx: "카를 마르크스",
   Knight: "기사",
   KnightCamp: "기사 야영지",
   Koti: "Koti",
   KotiInStorage: "Koti In Storage",
   KotiProduction: "Koti Production",
   LandTrade: "토지 매매",
   Language: "언어",
   Lapland: "라플란드",
   LaplandDesc: "발견 즉시 전체 지도를 공개합니다, 2타일 범위 내의 모든 건물이 +5의 생산 배수를을 획득합니다. 이 자연 불가사의는 12월에만 발견됩니다.",
   LargeHadronCollider: "Large Hadron Collider",
   LargeHadronColliderDescV2: "All Information Age great people get +2 level for this run. This wonder can be upgraded and each additional upgrade provides +1 level to all Information Age great people for this run",
   Law: "법",
   Lens: "안경",
   LensWorkshop: "안경 공장",
   LeonardoDaVinci: "레오나르도 다 빈치",
   Level: "레벨",
   LevelX: "레벨 %{level}",
   Liberalism: "자유주의",
   LiberalismLevel3DescHTML: "창고 <b>에서</b> 및 <b>로</b> 무료 운송",
   LiberalismLevel5DescHTML: "전력화 효과를 <b>두 배</b>로 증가",
   LiberalismLevelX: "자유주의 레벨 %{level}",
   Library: "도서관",
   LighthouseOfAlexandria: "알렉산드리아의 등대",
   LighthouseOfAlexandriaDesc: "모든 인접한 건물이 +5의 저장소 배수를 얻습니다.",
   LinusPauling: "라이너스 폴링",
   LinusPaulingDesc: "쉬고 있는 노동자당 과학 생산 +%{value}",
   Literature: "문학",
   LiveData: "Live Value",
   LocomotiveFactory: "기관차 공장",
   Logging: "벌목",
   LoggingCamp: "벌목장",
   LouisSullivan: "루이스 설리반",
   LouisSullivanDesc: "+%{value} 건축가 용량 배수",
   Louvre: "Louvre",
   LouvreDesc: "For every 10 Extra Great People at Rebirth, one great person from all unlocked ages is born",
   Lumber: "목재",
   LumberMill: "제재소",
   LunarNewYear: "춘절: 만리장성이 건물에 제공하는 부스트가 두 배가 됩니다. 도자기 탑은 이번 플레이에서 모든 위인에게 +1 레벨을 제공합니다.",
   LuxorTemple: "룩소르 신전",
   LuxorTempleDescV2: "+1 노동 중인 노동자당 과학 생산, 제국 종교를 선택하고 각 선택마다 더 많은 부스트를 해제하십시오.",
   Machinery: "기계",
   Magazine: "잡지",
   MagazinePublisher: "잡지사",
   Maglev: "자기 부상열차",
   MaglevFactory: "자기 부상열차 공장",
   MahatmaGandhi: "마하트마 간디",
   ManageAgeWisdom: "Manage Age Wisdom",
   ManagedImport: "자동 운송",
   ManagedImportDescV2: "이 건물은 %{range} 타일 범위 내에서 생산된 자원을 자동으로 가져옵니다. 이 건물의 자원 전송은 수동으로 변경할 수 없습니다. 최대 운송 거리는 무시됩니다.",
   ManageGreatPeople: "위인 관리",
   ManagePermanentGreatPeople: "영구적인 위인 관리",
   ManageSave: "Manage Save",
   ManageWonders: "불가사의 관리",
   Manhattan: "맨해튼",
   ManhattanProject: "맨해튼 프로젝트",
   ManhattanProjectDesc: "모든 우라늄 광산이 +2 생산, 노동자 능력, 저장소 배수를 얻습니다. 우라늄 농축 공장과 핵 물질 재처리시설은 우라늄 매장지 위에 건설된 인접한 우라늄 광산마다 +1 생산 배수를 얻습니다.",
   Marble: "대리석",
   Marbleworks: "대리석 공장",
   MarcoPolo: "마르코 폴로",
   MarieCurie: "마리 퀴리",
   MarinaBaySands: "마리나 베이 샌즈",
   MarinaBaySandsDesc: "모든 건물에 +5 노동자 용량 배율을 제공합니다. 이 불가사의는 업그레이드할 수 있으며, 각 추가 업그레이드는 모든 건물에 +1 노동자 용량 배율을 추가로 제공합니다.",
   Market: "시장",
   MarketDesc: "자원을 다른 자원으로 교환합니다, 교환 가능한 자원은 매 시간 업데이트됩니다.",
   MarketRefreshMessage: "%{count}개의 시장 거래가 새로고침 되었습니다.",
   MarketSell: "판매",
   MarketSettings: "시장 설정",
   MarketValueDesc: "평균 가격의 %{value}",
   MarketYouGet: "입수",
   MarketYouPay: "지불",
   MartinLuther: "마틴 루터",
   MaryamMirzakhani: "마리암 미르자카니",
   MaryamMirzakhaniDesc: "쉬고 있는 노동자로부터 과학 +%{value} 획득",
   Masonry: "벽돌",
   MatrioshkaBrain: "마트료시카 브레인",
   MatrioshkaBrainDescV2: "제국 가치 계산 시 과학을 포함할 수 있습니다 (과학 5 = 제국 가치 1). 활동 및 비활동 노동자당 과학 +5를 제공합니다. 이 불가사의는 업그레이드할 수 있으며, 각 추가 업그레이드는 노동 중인 및 쉬고 있는 노동자당 과학 +1, 그리고 과학을 생산하는 건물에 +1 생산 배율을 추가로 제공합니다.",
   MausoleumAtHalicarnassus: "마우솔로스 영묘",
   MausoleumAtHalicarnassusDescV2: "Transports from or to buildings within 2 tile range do not cost workers",
   MaxExplorers: "최대 탐험가",
   MaxTransportDistance: "최대 운송 거리",
   Meat: "고기",
   Metallurgy: "야금학",
   Michelangelo: "미켈란젤로",
   MiddleAge: "중세시대",
   MilitaryTactics: "군사 전술",
   Milk: "우유",
   Moai: "모아이 석상",
   MoaiDesc: "모아이 석상",
   MobileOverride: "Mobile Override",
   MogaoCaves: "막고굴",
   MogaoCavesDescV3: "노동 중인 노동자 비율 10%마다 +1 행복도. 신앙을 생산하는 모든 인접한 건물은 -1 행복도에서 면제됩니다.",
   MonetarySystem: "화폐 제도",
   MontSaintMichel: "Mont Saint-Michel",
   MontSaintMichelDesc: "Generate Culture from Idle Workers. Provide +1 Storage Multiplier to all buildings within 2-tile range. This wonder can be upgraded using the generated Culture and each level provides addtional +1 Storage Multiplier",
   Mosque: "모스크",
   MotionPicture: "영화",
   MountArarat: "Mount Ararat",
   MountAraratDesc: "All buildings within 2 tile range get +X Production, Worker Capacity and Storage Multiplier. X = cubic root of Effective Great People Level",
   MountFuji: "후지산",
   MountFujiDescV2: "페트라가 후지산 옆에 건설되면, 페트라는 +8시간 워프 저장소를 얻습니다. 게임이 실행 중일 때, 페트라에서 매분 20 워프를 생성합니다 (페트라 자체의 효과로 가속되지 않으며, 게임이 오프라인일 때는 생성되지 않습니다).",
   MountSinai: "시나이 산",
   MountSinaiDesc: "발견하면 이 시대의 위인이 탄생합니다. 신앙을 생산하는 모든 건물은 +5 저장소 배수를 얻습니다.",
   MountTai: "타이 산",
   MountTaiDesc: "과학을 생산하는 모든 건물은 +1 생산 배수를 얻습니다. 공자(위인)의 효과가 2배로 증가합니다. 발견 시 가장 비싼 잠금 해제 기술 비용에 해당하는 과학을 생성합니다.",
   MoveBuilding: "건물 이동",
   MoveBuildingFail: "선택한 타일이 유효하지 않습니다.",
   MoveBuildingNoTeleport: "텔레포트가 부족합니다.",
   MoveBuildingSelectTile: "타일을 선택하세요...",
   MoveBuildingSelectTileToastHTML: "지도에서 <b>탐험된 빈 타일</b>을 선택하세요",
   Movie: "영화",
   MovieStudio: "영화관",
   Museum: "박물관",
   Music: "음악",
   MusiciansGuild: "음악가 길드",
   MutualAssuredDestruction: "상호확증파괴",
   MutualFund: "뮤츄얼 펀드",
   Name: "이름",
   Nanotechnology: "나노기술",
   NapoleonBonaparte: "나폴레옹 보나파르트",
   NaturalGas: "천연 가스",
   NaturalGasWell: "천연 가스정",
   NaturalWonderName: "자연 불가사의: %{name}",
   NaturalWonders: "자연 불가사의",
   Navigation: "운항",
   NebuchadnezzarII: "네부카드네자르 2세",
   Neuschwanstein: "노이슈반슈타인 성",
   NeuschwansteinDesc: "불가사의를 건설할때 +10의 건축가 용량 배수를 얻습니다.",
   Newspaper: "신문",
   NextExplorersIn: "다음 탐험가까지",
   NextMarketUpdateIn: "시장 갱신까지",
   NiagaraFalls: "나이아가라 폭포",
   NiagaraFallsDescV2: "모든 창고, 시장 및 캐러밴은 +N 저장소 배수를 얻습니다. N = 잠금 해제된 시대의 수 알베르트 아인슈타인은 연구 기금에 +1 생산 승수를 제공합니다(브로드웨이와 같은 다른 부스트의 영향을 받지 않음).",
   NielsBohr: "닐스 보어",
   NielsBohrDescV2: "50% 이상의 노동자가 노동 중이고 노동 중인 노동자의 50% 미만이 운송 분야에 노동하는 경우 모든 노동자의 과학 생산 +%{value} ",
   NileRiver: "나일 강",
   NileRiverDesc: "하트셉수트의 효과가 두 배로 증가합니다. 모든 밀 농장은 생산 및 저장소 배수 +1을 얻습니다. 인접한 모든 밀 농장은 생산 및 저장소 배수 +5를 얻습니다.",
   NoPowerRequired: "이 건물은 전력이 필요하지 않습니다.",
   NothingHere: "아무것도 없다.",
   NotProducingBuildings: "생산 중이 아닌 건물",
   NuclearFission: "핵분열",
   NuclearFuelRod: "핵연료봉",
   NuclearMissile: "핵미사일",
   NuclearMissileSilo: "핵미사일 사일로",
   NuclearPowerPlant: "원자력 발전소",
   NuclearReactor: "원자로",
   NuclearSubmarine: "핵잠수함",
   NuclearSubmarineYard: "핵잠수함 조선소",
   OdaNobunaga: "Oda Nobunaga",
   OfflineErrorMessage: "현재 오프라인 상태입니다, 이 작업을 수행하려면 인터넷 연결이 필요합니다.",
   OfflineProduction: "오프라인 생산",
   OfflineProductionTime: "오프라인 생산 시간",
   OfflineProductionTimeDescHTML: "For the <b>first %{time} offline time</b>, you can choose either offline production or time warp - you can set the split here. The <b>rest of the offline time</b> can only be converted to time warp",
   OfflineTime: "오프라인 시간",
   Oil: "석유",
   OilPress: "착유기",
   OilRefinery: "정유 공장",
   OilWell: "유정",
   Ok: "확인",
   Oktoberfest: "Oktoberfest: Double the effect of Zugspitze",
   Olive: "올리브",
   OlivePlantation: "올리브 농장",
   Olympics: "올림픽",
   OnlyAvailableWhenPlaying: "Only available when playing %{city}",
   OpenLogFolder: "로그 폴더 열기",
   OpenSaveBackupFolder: "백업 폴더 열기",
   OpenSaveFolder: "세이브 폴더 열기",
   Opera: "오페라",
   OperationNotAllowedError: "이 작업은 허용되지 않습니다.",
   Opet: "오페트: 대 스핑크스가 더 이상 소비 배율을 증가시키지 않습니다",
   OpticalFiber: "광섬유",
   OpticalFiberPlant: "광섬유 공장",
   Optics: "광학",
   OptionsMenu: "설정",
   OptionsUseModernUIV2: "안티 앨리어싱 글꼴 사용",
   OsakaCastle: "오사카 성",
   OsakaCastleDesc: "2타일 범위 내의 모든 건물의 전력을 공급합니다. 과학 생산 건물(클론 연구소 포함)의 전력화를 허용 합니다.",
   OtherPlatform: "Other Platform",
   Ottoman: "Ottoman",
   OttoVonBismarck: "Otto von Bismarck",
   OxfordUniversity: "옥스포드 대학",
   OxfordUniversityDescV3: "과학을 생산하는 건물의 과학 생산량이 +10% 증가합니다. 건설되는 순간 가장 비싼 잠금 해제 기술 비용에 해당하는 과학을 생성합니다.",
   PabloPicasso: "파블로 피카소",
   Pagoda: "불탑",
   PaintersGuild: "화가 길드",
   Painting: "그림",
   PalmJumeirah: "팜 주메이라",
   PalmJumeirahDesc: "건설자 용량 +10을 제공합니다. 이 불가사의는 업그레이드할 수 있으며, 각 추가 업그레이드는 건설자 용량을 +2 추가로 제공합니다.",
   Pamukkale: "Pamukkale",
   PamukkaleDesc: "When discovered, convert each one of the Permanent Great People Shards (except for Promotion and Wildcard) to the same Great Person From This Run",
   Panathenaea: "파나테나이아: 포세이돈이 모든 건물에 +1 생산 배율을 제공합니다",
   Pantheon: "판테온",
   PantheonDescV2: "2 타일 범위 내의 모든 건물은 +1 작업자 능력 및 저장소 배수를 얻습니다. 모든 신당의 신앙 생산에 따라 과학을 생산합니다.",
   Paper: "종이",
   PaperMaker: "제지소",
   Parliament: "국회",
   Parthenon: "파르테논 신전",
   ParthenonDescV2: "고전 시대의 위인 둘이 태어나고 각각 4개의 선택권을 얻습니다. 음악가 길드와 화가 길드가 +1 생산력, 노동자 능력, 저장소 배수를 얻고 행복도 -1이 면제됩니다.",
   Passcode: "Passcode",
   PasscodeToastHTML: "<b>%{code}</b> is your passcode and it's valid for 30 minutes",
   PatchNotes: "패치노트",
   Peace: "Peace",
   Peacekeeper: "Peacekeeper",
   Penthouse: "Penthouse",
   PercentageOfProductionWorkers: "Percentage of Production Workers",
   Performance: "Performance",
   PermanentGreatPeople: "영구적인 위인",
   PermanentGreatPeopleAcquired: "영구적인 위인 획득",
   PermanentGreatPeopleUpgradeUndo: "Undo permanent great people upgrade: this will convert upgraded level back to shards - you will get %{amount} shards",
   Persepolis: "페르세폴리스",
   PersepolisDesc: "모든 벌목장, 채석장, 구리광산이 +1의 생산, 저장소, 노동자 능력 배수를 얻습니다.",
   PeterHiggs: "피터 힉스",
   PeterHiggsDesc: "노동 중인 노동자로부터 +%{value} 과학 획득",
   Petra: "페트라",
   PetraDesc: "오프라인 상태에서 타임워프를 생성하여 제국을 가속화하는 데 사용할 수 있습니다",
   PetraOfflineTimeReconciliation: "서버 오프라인 시간 조정 후 %{count} 워프가 적립되었습니다.",
   Petrol: "휘발유",
   PhiloFarnsworth: "필로 판스워스",
   Philosophy: "철학",
   Physics: "물리학",
   PierreDeCoubertin: "피에르 드 쿠베르탱",
   Pizza: "피자",
   Pizzeria: "피자가게",
   PlanetaryRover: "Planetary Rover",
   Plastics: "플라스틱",
   PlasticsFactory: "플라스틱 공장",
   PlatformAndroid: "Android",
   PlatformiOS: "iOS",
   PlatformSteam: "Steam",
   PlatformSyncInstructionHTML: "If you want to sync your progress on this device to a new device, click <b>Sync To A New Device</b> and get a one-time passcode. On your new device, click <b>Connect To A Device</b> and type in the one-time passcode",
   Plato: "플라톤",
   PlayerHandle: "플레이어 핸들값",
   PlayerHandleOffline: "현재 오프라인 상태입니다.",
   PlayerMapClaimThisTile: "이 타일을 획득합니다.",
   PlayerMapClaimTileCondition2: "안티치트로 정지당한적이 없습니다.",
   PlayerMapClaimTileCondition3: "필요한 기술을 해금했습니다: %{tech}",
   PlayerMapClaimTileCondition4: "타일을 가지지 않았거나 타일을 이동하기 위한 쿨타임이 지났습니다.",
   PlayerMapClaimTileCooldownLeft: "쿨타임: %{time}",
   PlayerMapClaimTileNoLongerReserved: "이 타일은 더이상 사용되지않습니다. <b>%{name}</b> 을 퇴거시키고 타일을 획득할수 있습니다.",
   PlayerMapEstablishedSince: "설립일",
   PlayerMapLastSeenAt: "최종 접속일",
   PlayerMapMapTileBonus: "Trade Tile Bonus",
   PlayerMapMenu: "거래",
   PlayerMapOccupyThisTile: "Occupy This Tile",
   PlayerMapOccupyTileCondition1: "This tile is adjacent to your home or occupied tiles",
   PlayerMapPageGoBackToCity: "도시로 돌아가기",
   PlayerMapSetYourTariff: "관세 설정",
   PlayerMapTariff: "관세",
   PlayerMapTariffApply: "관세 비율 설정",
   PlayerMapTariffDesc: "당신의 타일을 통과하는 모든 무역은 당신에게 관세를 지불합니다. 단, 당신이 관세를 늘리면 이익이 늘어나지만 당신의 타일을 통과하는 무역이 줄어듭니다.",
   PlayerMapTileAvailableTilePoint: "Available Tile Point",
   PlayerMapTileFromOccupying: "From Owned/Occupied Tiles",
   PlayerMapTileFromOccupyingTooltipHTML: "An owned/occupied tile generates <b>%{point}</b> tile point per hour (up to %{max} days from the first claimed tile)",
   PlayerMapTileFromRank: "From Account Rank",
   PlayerMapTileTilePoint: "Tile Point",
   PlayerMapTileUsedTilePoint: "Used Tile Point",
   PlayerMapTileUsedTilePointTooltipHTML: "You need <b>1 tile point</b> to own/occupy a tile",
   PlayerMapTradesFrom: "%{name} 로부터 거래",
   PlayerMapUnclaimedTile: "주인없는 타일",
   PlayerMapYourTile: "당신의 타일",
   PlayerTrade: "다른 플레이어와 거래",
   PlayerTradeAddSuccess: "거래가 성공적으로 추가되었습니다.",
   PlayerTradeAddTradeCancel: "취소",
   PlayerTradeAmount: "양",
   PlayerTradeCancelDescHTML: "이 거래를 취소하면 <b>%{res}</b>를 돌려받게 됩니다. 환불 비용은 <b>%{percent}</b>이 청구되고 저장소가 넘쳐 <b>%{discard}</b>는 폐기됩니다. <br><b>정말로 취소하시겠습니까?</b>",
   PlayerTradeCancelTrade: "거래 취소",
   PlayerTradeClaim: "받기",
   PlayerTradeClaimAll: "모두 받기",
   PlayerTradeClaimAllFailedMessageV2: "거래 요청에 실패했습니다. 저장 공간이 가득 찼습니까?",
   PlayerTradeClaimAllMessageV2: "You have claimed: <b>%{resources}</b>",
   PlayerTradeClaimAvailable: "%{count} 개의 거래가 가능해졌습니다.",
   PlayerTradeClaimTileFirst: "무역 지도에서 타일을 먼저 획득해주세요",
   PlayerTradeClaimTileFirstWarning: "무역 지도에서 타일을 획득한 후에만 다른 플레이어와 거래할수 있습니다.",
   PlayerTradeClearAll: "모두 지우기",
   PlayerTradeClearFilter: "Clear Filters",
   PlayerTradeDisabledBeta: "베타 버전이 출시된 후에만 플레이어 거래를 생성할 수 있습니다.",
   PlayerTradeFill: "수락",
   PlayerTradeFill50: "Fill 50%",
   PlayerTradeFill95: "Fill 95%",
   PlayerTradeFillAmount: "금액 채우기",
   PlayerTradeFillAmountMaxV2: "최대치 설정",
   PlayerTradeFillBy: "수락함",
   PlayerTradeFillPercentage: "비율 설정",
   PlayerTradeFillSuccessV2: "<b>%{success}/%{total}</b> 거래가 성사되었습니다. <b>%{fillAmount} %{fillResource}</b>을(를) 지불하고 <b>%{receivedAmount} %{receivedResource}</b>을(를) 받았습니다.",
   PlayerTradeFillTradeButton: "거래하기",
   PlayerTradeFillTradeTitle: "거래하기",
   PlayerTradeFilters: "필터",
   PlayerTradeFiltersApply: "적용",
   PlayerTradeFiltersClear: "지우기",
   PlayerTradeFilterWhatIHave: "Filter By What I Have",
   PlayerTradeFrom: "에서",
   PlayerTradeIOffer: "지불",
   PlayerTradeIWant: "희망",
   PlayerTradeMaxAll: "모든 필터 최대치",
   PlayerTradeMaxTradeAmountFilter: "최대량",
   PlayerTradeMaxTradeExceeded: "계정 랭크에 대한 최대 거래 횟수를 초과했습니다.",
   PlayerTradeNewTrade: "새 거래",
   PlayerTradeNoFillBecauseOfResources: "No trade has been filled due to insufficient resources",
   PlayerTradeNoValidRoute: "당신과 %{name} 사이에 유효한 거래 경로를 찾을수 없습니다.",
   PlayerTradeOffer: "지불",
   PlayerTradePlaceTrade: "거래 지역",
   PlayerTradePlayerNameFilter: "플레이어 이름",
   PlayerTradeResource: "자원",
   PlayerTradeStorageRequired: "요구 저장소",
   PlayerTradeTabImport: "수입",
   PlayerTradeTabPendingTrades: "보류 중인 거래",
   PlayerTradeTabTrades: "거래",
   PlayerTradeTariffTooltip: "무역 관세로 징수됨",
   PlayerTradeWant: "희망",
   PlayerTradeYouGetGross: "획득량 (관세 지불 전): %{res}",
   PlayerTradeYouGetNet: "획득량 (관세 지불 후): %{res}",
   PlayerTradeYouPay: "지불: %{res}",
   Poem: "시",
   PoetrySchool: "시 학교",
   Politics: "정치",
   PolytheismLevelX: "다신교 %{level}",
   PorcelainTower: "도자기 탑",
   PorcelainTowerDesc: "+5 행복도. 건설되면 환생 시 모든 추가 위인이 이번 회차에 사용할 수 있게 됩니다(영구적인 위인과 동일한 규칙에 따라 굴림).",
   PorcelainTowerMaxPickPerRoll: "Prefer Max Pick Per Roll",
   PorcelainTowerMaxPickPerRollDescHTML: "When choosing great people after Porcelain Tower completed, prefer max pick per roll for the available amount",
   Poseidon: "포세이돈",
   PoseidonDescV2: "모든 인접한 건물은 무료로 25레벨까지 업그레이드되며 +N 생산, 노동자 용량, 저장소 배율을 얻습니다. N = 건물의 티어",
   PoultryFarm: "가금 농장",
   Power: "전력",
   PowerAvailable: "사용 가능한 전력",
   PowerUsed: "사용한 전력",
   PreciousMetal: "귀금속",
   Printing: "인쇄",
   PrintingHouse: "인쇄소",
   PrintingPress: "인쇄기",
   PrivateOwnership: "사유재산권",
   Produce: "생산",
   ProduceResource: "생산: %{resource}",
   ProductionMultiplier: "생산 배수",
   ProductionPriority: "생산 우선순위",
   ProductionPriorityDescV4: "Priority determins the order that buildings transport and produce - a bigger number means a building transports and produces before other buildings",
   ProductionWorkers: "생산 중인 노동자",
   Progress: "진행도",
   ProgressTowardsNextGreatPerson: "재탄생시 다음 위인까지의 진척",
   ProgressTowardsTheNextGreatPerson: "Progress Towards the Next Great Person",
   PromotionGreatPersonDescV2: "소비될 때 동일한 시대의 영구적 위인을 다음 시대로 승급시킵니다.",
   ProphetsMosque: "예언자의 모스크",
   ProphetsMosqueDesc: "하룬 알 라시드의 효과를 두 배로 늘립니다. 모든 모스크의 신앙 생산에 따라 과학을 생산합니다",
   Province: "지방",
   ProvinceAegyptus: "아이깁투스",
   ProvinceAfrica: "아프리카",
   ProvinceAsia: "아시아",
   ProvinceBithynia: "비티니아",
   ProvinceCantabri: "칸타브리아",
   ProvinceCappadocia: "카파도키아",
   ProvinceCilicia: "킬리키아",
   ProvinceCommagene: "콤마게네",
   ProvinceCreta: "크레타",
   ProvinceCyprus: "키프로스",
   ProvinceCyrene: "키레나이",
   ProvinceGalatia: "갈라티아",
   ProvinceGallia: "갈리아",
   ProvinceGalliaCisalpina: "갈리아 키살피나",
   ProvinceGalliaTransalpina: "갈리아 트란살피나",
   ProvinceHispania: "히스파니아",
   ProvinceIllyricum: "일리리쿰",
   ProvinceItalia: "이탈리아",
   ProvinceJudia: "유대",
   ProvinceLycia: "리키아",
   ProvinceMacedonia: "마케도니아",
   ProvinceMauretania: "마우레타니아",
   ProvinceNumidia: "누미디아",
   ProvincePontus: "폰투스",
   ProvinceSardiniaAndCorsica: "사르디니아/코르시카",
   ProvinceSicillia: "시칠리아",
   ProvinceSophene: "소페네",
   ProvinceSyria: "시리아",
   PublishingHouse: "출판사",
   PyramidOfGiza: "기자의 피라미드",
   PyramidOfGizaDesc: "노동자를 생산하는 모든 건물이 +1의 생산배수를 얻습니다.",
   QinShiHuang: "진시황제",
   Radio: "라디오",
   RadioStation: "라디오 방송국",
   Railway: "선로",
   RamessesII: "람세스 2세",
   RamessesIIDesc: "+%{value} 건축가 용량 배수",
   RandomColorScheme: "Random Color Scheme",
   RapidFire: "속사",
   ReadFullPatchNotes: "패치 노트 확인(영문)",
   RebirthHistory: "Rebirth History",
   RebirthTime: "Rebirth Time",
   Reborn: "환생",
   RebornModalDescV3: "You will start a new empire but all your great people <b>from this run</b> becomes permanent shards, which can be used to upgrade your <b>permanent great people level</b>. You will also get extra great people shards based on your <b>total empire value</b>",
   RebornOfflineWarning: "현재 오프라인상태입니다, 환생은 서버에 연결 되었을때만 할수있습니다.",
   RebornTradeWarning: "활성상태이거나 수취할수있는 거래가 있습니다. <b>환생시 그것들은 사라집니다.</b> - 먼저 취소하거나 수취하는것을 고려해보십시오",
   RedistributeAmongSelected: "선택한 항목간 재설정",
   RedistributeAmongSelectedCap: "상한",
   RedistributeAmongSelectedImport: "수입량",
   Refinery: "정제소",
   Reichstag: "Reichstag",
   Religion: "종교",
   ReligionBuddhism: "불교",
   ReligionChristianity: "기독교",
   ReligionDescHTML: "<b>기독교, 이슬람, 불교 또는 다신교</b>를 당신의 제국 종교로 선택하십시오. 당신은 종교가 선택된 후에 종교를 바꿀 수 없습니다. 각 종교 내에서 더 많은 부스트를 잠금 해제 할 수 있습니다",
   ReligionIslam: "이슬람",
   ReligionPolytheism: "다신교",
   Renaissance: "르네상스",
   RenaissanceAge: "르네상스시대",
   ReneDescartes: "르네 데카르트",
   RequiredDeposit: "필요 자원",
   RequiredWorkersTooltipV2: "Required number of workers for production is equal to the sum of all resources consumed and produced after multipliers (excluding dynamic multipliers)",
   RequirePower: "전력 공급 필요",
   RequirePowerDesc: "이 건물은 전력이 공급되는 타일 위에 건설되어야 하며 전력을 인접한 타일로 확장할 수 있습니다.",
   Research: "연구",
   ResearchFund: "연구 기금",
   ResearchLab: "연구소",
   ResearchMenu: "연구",
   ResourceAmount: "양",
   ResourceBar: "리소스 바",
   ResourceBarExcludeStorageFullHTML: "생산하지 않는 건물에서 <b>저장공간이 가득 찬</b> 건물을 제외합니다.",
   ResourceBarExcludeTurnedOffOrNoActiveTransportHTML: "생산하지 않는 건물에서 <b>비활성화 된</b> 건물을 제외합니다.",
   ResourceBarShowUncappedHappiness: "상한을 무시하고 행복도 표시",
   ResourceCloneTooltip: "The production multiplier only applies to the cloned resource (i.e. the extra copy)",
   ResourceColor: "자원 색상",
   ResourceExportBelowCap: "소비 제한 해제",
   ResourceExportBelowCapTooltip: "자원의 양이 설정한 한도보다 적더라도 다른 건물이 이 건물에서 자원을 꺼내갈 수 있도록 허용합니다.",
   ResourceExportToSameType: "동일 유형간 수송",
   ResourceExportToSameTypeTooltip: "동일한 유형의 다른 건물이 이 건물에서 자원을 수송할 수 있도록 허용합니다.",
   ResourceFromBuilding: " %{building} 에서 %{resource}",
   ResourceImport: "자원 수송",
   ResourceImportCapacity: "자원 운송 상한",
   ResourceImportImportCapV2: "최대치",
   ResourceImportImportCapV2Tooltip: "이 건물은 최대치에 도달하면 이 자원 운송을 중단합니다.",
   ResourceImportImportPerCycleV2: "주기 당",
   ResourceImportImportPerCycleV2ToolTip: "주기당 운송되는 자원의 양",
   ResourceImportPartialWarningHTML: "The total resource transport capacity has exceeds the maximum capacity: <b>each resource transport will only transport partially per cycle</b>",
   ResourceImportResource: "자원",
   ResourceImportSettings: "자원수송: %{res}",
   ResourceImportStorage: "저장소",
   ResourceNeeded: "Extra %{resource} x%{amount} Needed",
   ResourceTransportPreference: "운송 설정",
   RevealDeposit: "새로운 자원 매장층 발견",
   Revolution: "혁명",
   RhineGorge: "Rhine Gorge",
   RhineGorgeDesc: "+2 Happiness for each wonder within 2 tile range",
   RichardFeynman: "리처드 파인만",
   RichardFeynmanDesc: "50% 이상의 노동자가 노수동 중이고 노동 중인 노동자의 50% 미만이 운송 분야에 노동하는 경우 모든 노동자의 과학 생산 +%{value}",
   RichardJordanGatling: "리처드 조던 개틀링",
   Rifle: "라이플",
   RifleFactory: "라이플 공장",
   Rifling: "강선",
   Rijksmuseum: "암스테르담 국립미술관",
   RijksmuseumDesc: "행복도 +5, 문화를 소비하거나 생산하는 모든 건물이 +1의 생산, 저장소, 노동자 능력 배수를 얻습니다.",
   RoadAndWheel: "도로와 바퀴",
   RobertNoyce: "로버트 노이스",
   Robocar: "로보카",
   RobocarFactory: "로보카 공장",
   Robotics: "로봇공학",
   RockefellerCenterChristmasTree: "Rockefeller Center Christmas Tree",
   RockefellerCenterChristmasTreeDesc: "+3 Happiness for each unlocked age. This natural wonder can only be discovered in December",
   Rocket: "로켓",
   RocketFactory: "로켓 공장",
   Rocketry: "로켓공학",
   Roman: "로마",
   RomanForum: "포로 로마노",
   RudolfDiesel: "루돌프 디젤젤",
   Rurik: "류리크",
   RurikDesc: "+%{value} 행복도",
   SagradaFamilia: "사그라다 파밀리아",
   SagradaFamiliaDesc: "2타일 범위 내의 모든 건물은 +N 생산, 작업자 능력 및 저장소 배수를 얻습니다. N = 사그라다 파밀리아에 인접한 건물 간의 최대 등급 차이",
   SaintBasilsCathedral: "성 바실리 대성당",
   SaintBasilsCathedralDescV2: "자원 추출 건물이 광산 근처에서 작동하도록 허용합니다. 모든 티어 1 건물은 생산량 배수, 노동자 능력, 저장소 배수 +1을 얻습니다.",
   Saladin: "살라딘",
   Samsuiluna: "삼수 일루나",
   Sand: "모래",
   Sandpit: "모래 채취장",
   SantaClausVillage: "Santa Claus Village",
   SantaClausVillageDesc: "When completed, a great person of the current age is born. This wonder can be upgraded and each additional upgrade provides an extra great person. When choosing great people from this wonder, 4 choices are provided. This wonder can only be constructed in December",
   SargonOfAkkad: "사르곤 대왕",
   Satellite: "위성",
   SatelliteFactory: "위성 공장",
   SatoshiNakamoto: "사토시 나카모토",
   Saturnalia: "새터낼리아: 알프스가 더 이상 소비 배율을 증가시키지 않습니다",
   SaveAndExit: "저장 후 종료",
   School: "학교",
   Science: "과학",
   ScienceFromBusyWorkers: "노동중인 노동자 과학 생산",
   ScienceFromIdleWorkers: "쉬고 있는 노동자 과학 생산",
   SciencePerBusyWorker: "노동중인 노동자 당",
   SciencePerIdleWorker: "쉬고 있는 노동자 당",
   ScrollSensitivity: "스크롤 민감도",
   ScrollSensitivityDescHTML: "마우스 휠을 스크롤 할 때 감도를 조정하십시오. <b>0.01에서 100 사이여야합니다. 기본값은 1입니다.</b>",
   ScrollWheelAdjustLevelTooltip: "커서가 이 위에 있을 때 스크롤 휠을 사용하여 레벨을 조정할 수 있습니다.",
   SeaTradeCost: "해상 무역 비용",
   SeaTradeUpgrade: "바다 건너 플레이어들과 거래를 해보세요. 각 바다 타일에 대한 관세: %{tariff}",
   SelectCivilization: "문명 선택",
   SelectedAll: "모두선택",
   SelectedCount: "%{count} 선택됨",
   Semiconductor: "반도체",
   SemiconductorFab: "반도체 공장",
   SendExplorer: "탐험가 파견",
   SergeiKorolev: "세르게이 코롤료프",
   SetAsDefault: "기본값으로 설정",
   SetAsDefaultBuilding: "기본값으로 설정 %{building}",
   Shamanism: "샤머니즘",
   Shelter: "주거",
   Shortcut: "단축키",
   ShortcutBuildingPageSellBuildingV2: "건물 파괴",
   ShortcutBuildingPageToggleBuilding: "생산 토글",
   ShortcutBuildingPageToggleBuildingSetAllSimilar: "동일한 모든 건물 생산 토글",
   ShortcutBuildingPageUpgrade1: "업그레이드 버튼 1 (+1)",
   ShortcutBuildingPageUpgrade2: "업그레이드 버튼 2 (+5)",
   ShortcutBuildingPageUpgrade3: "업그레이드 버튼 3 (+10)",
   ShortcutBuildingPageUpgrade4: "업그레이드 버튼 4 (+15)",
   ShortcutBuildingPageUpgrade5: "업그레이드 버튼 5 (+20)",
   ShortcutClear: "지우기",
   ShortcutConflict: "단축키가 다음과 충돌합니다. %{name}",
   ShortcutNone: "없음",
   ShortcutPressShortcut: "단축키를 눌러주세요...",
   ShortcutSave: "저장",
   ShortcutScopeBuildingPage: "건축 화면",
   ShortcutScopeConstructionPage: "건축/업그레이드 화면",
   ShortcutScopeEmptyTilePage: "빈 타일 화면",
   ShortcutScopePlayerMapPage: "무역 지도 화면",
   ShortcutScopeTechPage: "기술 연구 화면",
   ShortcutScopeUnexploredPage: "미탐색 타일 화면",
   ShortcutTechPageGoBackToCity: "도시로 돌아가기",
   ShortcutTechPageUnlockTech: "선택한 기술 해금",
   ShortcutUpgradePageCancelAllUpgrades: "Cancel All Upgrades",
   ShortcutUpgradePageCancelUpgrade: "업그레이드 취소",
   ShortcutUpgradePageDecreaseLevel: "업그레이드 레벨 하강",
   ShortcutUpgradePageEndConstruction: "건설 종료",
   ShortcutUpgradePageIncreaseLevel: "업그레이드 레벨 상승",
   ShowTransportArrow: "운송 화살표 표시",
   ShowTransportArrowDescHTML: "이 옵션을 끄면 운송 화살표가 숨겨집니다. 저사양 장치에서 <i>약간의</i> 성능 개선이 있을 수 있습니다. 성능 개선은 <b>게임을 재시작한 후</b> 적용됩니다.",
   ShowUnbuiltOnly: "아직 건설되지 않은 건물만 표시",
   Shrine: "신당",
   SidePanelWidth: "측면 패널 너비",
   SidePanelWidthDescHTML: "측면 패널의 너비를 변경합니다. <b>적용하려면 게임을 다시 시작해야 합니다.</b>",
   SiegeRam: "공성추",
   SiegeWorkshop: "공성추 공장",
   Silicon: "실리콘",
   SiliconSmelter: "실리콘 제련소",
   Skyscraper: "마천루",
   Socialism: "사회주의",
   SocialismLevel4DescHTMLV2: "가장 저렴한 <b>세계대전 시대</b> 기술 비용에 해당하는 과학을 한 번 생성합니다.",
   SocialismLevel5DescHTMLV2: "가장 저렴한 <b>냉전 시대</b> 기술 비용에 해당하는 과학을 한 번 생성합니다.",
   SocialismLevelX: "사회주의 레벨 %{level}",
   SocialNetwork: "소셜 네트워크",
   Socrates: "소크라테스",
   SocratesDesc: "+%{value} 노동 중인 노동자 당 과학 생산",
   Software: "소프트웨어",
   SoftwareCompany: "소프트웨어 회사",
   Sound: "소리",
   SoundEffect: "소리 설정",
   SourceGreatPerson: "위인: %{person}",
   SourceGreatPersonPermanent: "영구 위인: %{person}",
   SourceIdeology: "Ideology: %{ideology}",
   SourceReligion: "종교: %{religion}",
   SourceResearch: "연구: %{tech}",
   SourceTradition: "전통: %{tradition}",
   SpaceCenter: "우주 센터",
   Spacecraft: "우주선",
   SpacecraftFactory: "우주선 공장",
   SpaceNeedle: "스페이스 니들",
   SpaceNeedleDesc: "건설된 불가사의 하나 당 행복도 +1",
   SpaceProgram: "우주 계획",
   Sports: "스포츠",
   Stable: "마구간",
   Stadium: "경기장",
   StartFestival: "축제를 시작합시다!",
   Stateship: "국정",
   StatisticsBuildings: "건물",
   StatisticsBuildingsSearchText: "검색 할 건물 종류",
   StatisticsEmpire: "제국",
   StatisticsExploration: "탐험",
   StatisticsOffice: "통계청",
   StatisticsOfficeDesc: "제국의 통계를 제공합니다. 지도 탐색을 위한 탐험가 생성",
   StatisticsResources: "자원",
   StatisticsResourcesDeficit: "잔량",
   StatisticsResourcesDeficitDesc: "생산: %{output} - 소비: %{input}",
   StatisticsResourcesRunOut: "소진",
   StatisticsResourcesSearchText: "검색 할 자원 종류",
   StatisticsScience: "과학",
   StatisticsScienceFromBuildings: "건물이 제공하는 과학",
   StatisticsScienceFromWorkers: "노동자가 제공하는 과학",
   StatisticsScienceProduction: "과학 생산",
   StatisticsStalledTransportation: "정체된 운송",
   StatisticsTotalTransportation: "총 운송",
   StatisticsTransportation: "운송",
   StatisticsTransportationPercentage: "운송중인 노동자 비율",
   StatueOfLiberty: "자유의 여신상",
   StatueOfLibertyDesc: "모든 인접한 건물이 +N의 생산, 저장소, 노동자 능력 배수를 얻습니다. N = 동일한 유형의 인접한 건물 수",
   StatueOfZeus: "제우스 상",
   StatueOfZeusDesc: "인접한 빈 타일에 무작위 자원 매장층이 생성됩니다, 인접한 모든 티어1 건물이 +5의 생산, 저장소 배수를 얻습니다.",
   SteamAchievement: "Steam 도전과제",
   SteamAchievementDetails: "Steam 도전과제 보기",
   SteamEngine: "증기엔진",
   Steamworks: "증기엔진 공장",
   Steel: "강철",
   SteelMill: "제강 공장",
   StephenHawking: "스티븐 호킹",
   Stock: "주식",
   StockExchange: "주식 거래",
   StockMarket: "주식시장",
   StockpileDesc: "이 건물은 최대 비축량에 도달할때까지 %{capacity}x 배의 자원을 전송받습니다. ",
   StockpileMax: "최대 비축량",
   StockpileMaxDesc: "이 건물은 %{cycle} 번의 생산주기만큼 자원이 비축되면 자원을 전송받지 않습니다.",
   StockpileMaxUnlimited: "무제한",
   StockpileMaxUnlimitedDesc: "이 건물은 저장소가 가득 찰때까지 자원전송을 중단하지 않습니다.",
   StockpileSettings: "비축 투입 용량",
   Stone: "돌",
   StoneAge: "석기시대",
   Stonehenge: "스톤헨지",
   StonehengeDesc: "돌을 소비하거나 생산하는 모든 건물이 +1의 생산 배수를 얻습니다.",
   StoneQuarry: "채석장",
   StoneTool: "돌 도구",
   StoneTools: "돌 도구",
   Storage: "저장소",
   StorageBaseCapacity: "기초 저장량",
   StorageMultiplier: "저장소 배수",
   StorageUsed: "사용중인 저장량",
   StPetersBasilica: "성 베드로 대성당",
   StPetersBasilicaDescV2: "모든 교회는 +5 저장소 배수를 얻습니다. 모든 교회의 신앙 생산에 따라 과학을 생산합니다.",
   Submarine: "잠수함",
   SubmarineYard: "잠수함 조선소",
   SuleimanI: "Suleiman I",
   SummerPalace: "이화원",
   SummerPalaceDesc: "화약을 생산하거나 소비하는 모든 인접한 건물은 행복도 -1이 면제되며, 화약을 소비하거나 생산하는 모든 건물이 +1의 생산, 저장소, 노동자 능력 배수를 얻습니다.",
   Supercomputer: "슈퍼컴퓨터",
   SupercomputerLab: "슈퍼컴퓨터 연구소",
   SupporterPackRequired: "Supporter Pack 필요",
   SupporterThankYou: "CivIdle is kept afloat thanks to the generousity of the following supporter pack owners",
   SwissBank: "Swiss Bank",
   SwissBankDescV2: "Convert a chosen resource from adjacent warehouses to Koti (10 million in Sanskrit) - a tradeable resource that is worth 10M value. Each level of Swiss Bank add 1 Koti conversion per cycle, which is affected by Production Multiplier. Swiss Bank can store unlimited amount of Koti",
   Sword: "검",
   SwordForge: "검 대장간",
   SydneyOperaHouse: "시드니 오페라 하우스",
   SydneyOperaHouseDescV2: "시드니 오페라 하우스",
   SyncToANewDevice: "Sync To A New Device",
   Synthetics: "합성화학",
   TajMahal: "타지마할",
   TajMahalDescV2: "고전시대의 위인과 중세시대의 위인이 탄생합니다. 레벨 20 이상의 건물을 업그레이드할 때 건축가 용량 배수 +5",
   TangOfShang: "탕왕",
   TangOfShangDesc: "+%{value} 쉬고 있는 노동자 당 과학 생산",
   Tank: "탱크",
   TankFactory: "탱크 공장",
   TechAge: "Age",
   TechGlobalMultiplier: "보너스",
   TechHasBeenUnlocked: "%{tech} (이)가 해금되었습니다",
   TechProductionPriority: "건물 우선순위 잠금 해제 - 건물 별로 생산 우선순위 설정을 할 수 있습니다.",
   TechResourceTransportPreference: "건물 운송 기본 설정 잠금 해제 - 건물이 생산에 필요한 자원을 운송하는 방법을 설정할 수 있습니다.",
   TechResourceTransportPreferenceAmount: "수량",
   TechResourceTransportPreferenceAmountTooltip: "이 건물은 저장 공간에 더 많은 자원을 가진 건물에서 자원을 운반하는 것을 선호 합니다.",
   TechResourceTransportPreferenceDefault: "기본",
   TechResourceTransportPreferenceDefaultTooltip: "이 자원에 대한 교통 기본 설정을 재정의하지 말고 대신 건물의 교통 기본 설정을 사용 합니다.",
   TechResourceTransportPreferenceDistance: "거리",
   TechResourceTransportPreferenceDistanceTooltip: "이 건물은 거리가 더 가까운 건물에서 자원을 수송하는 것을 선호합니다.",
   TechResourceTransportPreferenceOverrideTooltip: "이 자원은 운송 설정이 재정의 되었습니다: %{mode}",
   TechResourceTransportPreferenceStorage: "저장소",
   TechResourceTransportPreferenceStorageTooltip: "이 건물은 사용된 저장 공간의 비율이 더 높은 건물에서 자원을 운반하는 것을 선호 합니다.",
   TechStockpileMode: "비축 모드 잠금 해제 - 건물 별로 비축량을 조정 할 수 있습니다.",
   Teleport: "텔레포트",
   TeleportDescHTML: "순간이동은 <b>%{time}초마다</b> 생성됩니다. 텔레포트를 사용하여 <b>건물(불가사의 제외)을 한 번 이동</b>할 수 있습니다.",
   Television: "텔레비전",
   TempleOfArtemis: "아르테미스 신전",
   TempleOfArtemisDesc: "완성 즉시 모든 검 대장간과 무기고가 +5레벨을 얻습니다. 모든 검 대장간과 무기고가 +1의 생산, 저장소, 노동자 능력 배수를 얻습니다.",
   TempleOfHeaven: "천단",
   TempleOfHeavenDesc: "레벨 10이상의 모든 건물이 +1의 노동자 능력 배수를 얻습니다.",
   TempleOfPtah: "프타 사원",
   TerracottaArmy: "병마용",
   TerracottaArmyDesc: "모든 철 광산이 +1의 생산, 저장소, 노동자 당 생산량 배수를 얻습니다. 철 대장간이 인접한 철 광산 하나당 +1의 생산 배수를 얻습니다.",
   Thanksgiving: "추수감사절: 월스트리트가 건물에 제공하는 부스트가 두 배가 되며, 뮤추얼 펀드, 헤지 펀드, 비트코인 채굴기에 적용됩니다. 연구 자금에 +5 생산 배율을 제공합니다.",
   Theater: "극장",
   Theme: "테마",
   ThemeColor: "테마 색상",
   ThemeColorResearchBackground: "연구 배경",
   ThemeColorReset: "기본값으로 되돌리기",
   ThemeColorResetBuildingColors: "건물 색상 초기화",
   ThemeColorResetResourceColors: "자원 색상 초기화",
   ThemeInactiveBuildingAlpha: "비활성화된 건물 투명도",
   ThemePremiumTile: "This tile is only available for Supporter Pack owners",
   ThemeResearchHighlightColor: "선택한 연구 색상",
   ThemeResearchLockedColor: "미해금된 연구 색상",
   ThemeResearchUnlockedColor: "해금된 연구 색상",
   ThemeTransportIndicatorAlpha: "운송 표시기 투명도",
   Theocracy: "신권정치",
   TheoreticalData: "Theoretical Data",
   ThePentagon: "The Pentagon",
   ThePentagonDesc: "After constructed, generate teleports that can be used to move buildings. All buildings within 2 tile range get +1 Production, Worker Capacity and Storage Multiplier",
   TheWhiteHouse: "The White House",
   ThomasEdison: "토마스 에디슨",
   ThomasGresham: "Thomas Gresham",
   Tile: "타일",
   TileBonusRefreshIn: "Tile bonus will refresh in <b>%{time}</b>",
   TimBernersLee: "팀 버너스-리",
   TimeWarp: "타임워프",
   TimeWarpWarning: "컴퓨터가 처리할 수 있는 속도보다 더 빠른 속도로 가속하면 데이터 손실이 발생할 수 있습니다. 책임은 사용자에게 있습니다",
   ToggleWonderEffect: "불가사의 효과 On/Off",
   Tool: "도구",
   TopkapiPalace: "Topkapı Palace",
   TopkapiPalaceDesc: "All buildings within 2 tile range get +X Storage Multiplier. X = 50% of its Production Multiplier (excluding Dynamic)",
   TotalEmpireValue: "총 제국 가치",
   TotalEmpireValuePerCycle: "주기 당 총 제국 가치",
   TotalEmpireValuePerCyclePerGreatPeopleLevel: "위인 레벨 당 주기 당 총 제국 가치",
   TotalEmpireValuePerWallSecond: "Total Empire Value Wall Second",
   TotalEmpireValuePerWallSecondPerGreatPeopleLevel: "Total Empire Value Per Wall Second Per Great People Level",
   TotalGameTimeThisRun: "Total Game Time This Run",
   TotalScienceRequired: "총 과학 요구량",
   TotalStorage: "총 저장량",
   TotalWallTimeThisRun: "Total Wall Time This Run",
   TotalWallTimeThisRunTooltip: "Wall time (aka. elapsed real time) measures the actual time taken for this run. The differs from the game time in that Time Warp in Petra and Offline Production does not affect wall time but it does affect game time",
   TotalWorkers: "총 노동자",
   TowerBridge: "Tower Bridge",
   TowerBridgeDesc: "After constructed, a great person from unlocked ages is born every 3600 cycles (1h game time)",
   TowerOfBabel: "바벨탑",
   TowerOfBabelDesc: "주변에 건설된 건물이 가동 중이라면 해당 건물과 같은 모든 건물에 +2 생산 배율을 제공합니다.(중복되지 않음)",
   TradeFillSound: "'Trade Filled' Sound",
   TradeValue: "Trade Value",
   TraditionCommerce: "상업",
   TraditionCultivation: "재배",
   TraditionDescHTML: "<b>재배, 상업, 확장 및 명예</b> 중 하나를 제국 전통으로 선택하세요. 전통을 선택한 후에는 <b>전통을 변경할 수 없습니다.</b> 각 전통 내에서 더 많은 부스트를 잠금 해제할 수 있습니다.",
   TraditionExpansion: "확장",
   TraditionHonor: "명예",
   Train: "기차",
   TranslationPercentage: "%{language} 는 %{percentage} 번역되었습니다. GitHub에서 번역에 도움을 주세요",
   TranslatorCredit: "ㅇㅇ, happy",
   Translators: "번역가",
   TransportAllocatedCapacityTooltip: "이 자원 전송에 할당된 건축가 용량+",
   TransportationWorkers: "운송 노동자",
   TransportCapacity: "수송 용량",
   TransportCapacityMultiplier: "수송 용량 배수",
   TransportManualControlTooltip: "이 자원을 건축/업그레이드용으로 전송합니다.",
   TransportPlanCache: "Transport Plan Cache",
   TransportPlanCacheDescHTML:
      "Every cycle, each building calculates the best transport plan based on its settings - this process requires high CPU power. Enabling this will attempt to cache the result of the transport plan if it is still valid and therefore reduce CPU usage and frame rate drop. <b>Experimental Feature</b>",
   TribuneUpgradeDescGreatPeopleWarning: "현재 회차에서 위인을 획득했습니다. <b>먼저 환생을 해야합니다</b>. 재무관으로 업그레이드하면 현재 회차는 리셋됩니다.",
   TribuneUpgradeDescGreatPeopleWarningTitle: "먼저 환생해주세요",
   TribuneUpgradeDescV4:
      "You can play the full game as Tribune if you do not plan to participate in the <b>optional</b> online features. To acquire unrestricted access to the online features, you will need to upgrade to Quaestor. <b>This is an anti-bot measure to keep the game free for everyone.</b> However, <b>when upgrading to Quaestor</b> you can carry over great people: <ul><li>Up to Level <b>3</b> for Bronze, Iron and Classical Age</li><li>Up to Level <b>2</b> for Middle Age, Renaissance and Industrial Age</li><li>Up to Level <b>1</b> for World Wars, Cold War and Information Age</li></ul>Great People Shards above the level and <b>Age Wisdom</b> levels <b>cannot</b> be carried over",
   TurnOffFullBuildings: "Turn Off All %{building} With Full Storage",
   TurnOnTimeWarpDesc: "%{speed} 개의 워프를 소모하여 %{speed}x 배의 속도로 제국이 진행되도록 가속합니다..",
   Tutorial: "튜토리얼",
   TutorialPlayerFlag: "플레이어 깃발을 설정하세요",
   TutorialPlayerHandle: "플레이어 핸들을 설정하세요",
   TV: "TV",
   TVStation: "TV 방송국",
   UnclaimedGreatPersonPermanent: "<b>획득하지 않은 영구적인 위인이 있습니다.</b>, 이곳을 눌러 획득하세요.",
   UnclaimedGreatPersonThisRun: "<b>이번 회차에 획득하지 않은 위인이 있습니다.</b>, 이곳을 눌러 획득하세요.",
   UnexploredTile: "탐험하지 않은 타일",
   UNGeneralAssemblyCurrent: "현재 UN 총회 #%{id}",
   UNGeneralAssemblyMultipliers: "<b>%{buildings}</b>에 대한 생산량, 노동자 능력 및 저장소 배수 <b>+%{count}</b> ",
   UNGeneralAssemblyNext: "다가오는 UN 총회 #%{id}",
   UNGeneralAssemblyVoteEndIn: "<b>%{time}</b> 후에 투표 종료. 그 전에 언제든지 투표를 변경할 수 있습니다.",
   UniqueBuildings: "특수한 건물",
   UniqueTechMultipliers: "유니크 기술 배수",
   UnitedNations: "연합 국가",
   UnitedNationsDesc: "모든 티어 IV, V, VI 건물은 +1 생산량, 노동자 능력, 저장소 배수를 얻습니다. UN 총회에 참여하고 매주 추가 부스트에 투표하세요",
   University: "대학",
   UnlockableResearch: "해금 가능한 연구 기술",
   UnlockBuilding: "해금",
   UnlockTechProgress: "진행도",
   UnlockXHTML: "<b>%{name}</b> 해금",
   Upgrade: "업그레이드",
   UpgradeBuilding: "건물 업그레이드",
   UpgradeBuildingNotProducingDescV2: "이 건물은 업그레이드 중입니다. <b>업그레이드가 완료될 때까지 생산이 중단됩니다.</b>",
   UpgradeTo: "레벨 %{level}로 업그레이드",
   Uranium: "우라늄",
   UraniumEnrichmentPlant: "우라늄 농축 공장",
   UraniumMine: "우라늄 광산",
   Urbanization: "도시화",
   UserAgent: "사용자 에이전트: %{driver}",
   View: "보기",
   ViewMenu: "화면",
   ViewTechnology: "보기",
   Vineyard: "포도밭",
   VirtualReality: "가상현실",
   Voltaire: "Voltaire",
   WallOfBabylon: "바빌론의 성벽",
   WallOfBabylonDesc: "모든 건물이 +N 저장소 배율을 얻습니다. N = 해제된 시대 수 / 2",
   WallStreet: "월스트리트",
   WallStreetDesc: "2타일 범위 내에서 동전, 지폐, 채권, 주식 및 외환을 생산하는 모든 건물은 +N 생산량 배수를 얻습니다. N = 1에서 5 사이의 임의 값으로 건물마다 다르며 시장이 새로 고쳐질 때마다 변경됩니다. 존 D. 록펠러 효과 2배",
   WaltDisney: "월트 디즈니",
   Warehouse: "창고",
   WarehouseAutopilotSettings: "자동 관리 설정",
   WarehouseAutopilotSettingsEnable: "자동 관리 활성화",
   WarehouseAutopilotSettingsRespectCapSetting: "저장소 요구 < 한도",
   WarehouseAutopilotSettingsRespectCapSettingTooltip: "자동 관리 옵션은 저장 용량이 한도 미만인 리소스만 운송합니다.",
   WarehouseDesc: "특정 자원을 운송하고 추가 저장소를 제공합니다.",
   WarehouseExtension: "창고 캐러밴 확장 모드 잠금 해제. 캐러밴에 인접한 창고가 플레이어 거래에 포함되도록 허용합니다.",
   WarehouseSettingsAutopilotDesc: "이 창고는 유휴용량을 사용하여 저장소가 가득한 건물로부터 자원을 수송받습니다. 현재 유휴 용량: %{capacity}",
   WarehouseUpgrade: "창고 오토파일럿 모드 해금. 창고와 인접한 건물간에 무료 운송",
   WarehouseUpgradeDesc: "이 창고와 인접한 건물간은 무료 운송이 적용됩니다",
   Warp: "워프",
   WarpSpeed: "Warp Speed",
   Water: "물",
   WellStockedTooltip: "재고가 풍부한 건물은 생산을 위한 충분한 자원을 보유한 건물입니다. 여기에는 생산 중인 건물 또는 저장고가 꽉 차 있거나 노동자 부족으로 인해 생산하지 않는 건물이 포함됩니다.",
   WernherVonBraun: "베르너 폰 브라운",
   Westminster: "Westminster",
   Wheat: "밀",
   WheatFarm: "밀농장",
   WildCardGreatPersonDescV2: "소모될 때 동일한 시대의 원하는 위인이 됩니다.",
   WilliamShakespeare: "윌리엄 셰익스피어",
   Wine: "와인",
   Winery: "와이너리",
   WishlistSpaceshipIdle: "Wishlist Spaceship Idle",
   Wonder: "불가사의",
   WonderBuilderCapacityDescHTML: "불가사의를 건축할때 <b>건축가 용량</b>은 불가사의가 해금된 <b>시대</b> 와 <b>기술</b>에 영향을 받습니다.",
   WondersBuilt: "불가사의 건설됨",
   WondersUnlocked: "불가사의 해금됨",
   WonderUpgradeLevel: "불가사의 레벨",
   Wood: "나무",
   Worker: "노동자",
   WorkerCapacityMultiplier: "노동자 능력 배수",
   WorkerHappinessPercentage: "행복도 배수",
   WorkerMultiplier: "노동자 용량",
   WorkerPercentagePerHappiness: "행복도당 %{value}% 배수",
   Workers: "노동자",
   WorkersAvailableAfterHappinessMultiplier: "행복도 배수 적용 후 노동자 수",
   WorkersAvailableBeforeHappinessMultiplier: "행복도 배수 적용 전 노동자 수",
   WorkersBusy: "노동 중인 노동자",
   WorkerScienceProduction: "노동자 과학 생산",
   WorkersRequiredAfterMultiplier: "필요한 노동자 수",
   WorkersRequiredBeforeMultiplier: "필요한 노동 능력",
   WorkersRequiredForProductionMultiplier: "노동자 당 생산능력",
   WorkersRequiredForTransportationMultiplier: "노동자 당 운송용량",
   WorkersRequiredInput: "운송",
   WorkersRequiredOutput: "생산",
   WorldWarAge: "세계대전 시대",
   WorldWideWeb: "월드 와이드 웹",
   WritersGuild: "작가 길드",
   Writing: "집필",
   WuZetian: "측천무후",
   WuZetianDesc: "+%{value} 운송 용량 배수",
   Xuanzang: "현장",
   YangtzeRiver: "양쯔강",
   YangtzeRiverDesc: "물을 소비하는 모든 건물은 생산력, 노동자 능력, 저장소 배수를 +1 얻습니다. 정화(위인)의 효과가 두 배로 증가합니다. 영구적인 측천무후(위인)은 레벨 당 모든 건물에 +1 저장소 배수를 제공합니다.",
   YearOfTheSnake: "Year of the Snake",
   YearOfTheSnakeDesc:
      "After completed, when entering a new age, instead of getting one great person of each unlocked age, get the same amount of great people in the current age. All buildings within 2-tile range get +1 Production Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Production Multiplier to buildings within 2-tile range. This wonder can only be constructed during the lunar new year period (1.20 ~ 2.10)",
   YellowCraneTower: "황학루",
   YellowCraneTowerDesc: "위인을 선택할 때 선택지 +1. 1 타일 범위 내의 모든 건물은 +1 생산, 노동자 능력 및 저장소 배수를 얻습니다. 양쯔강 옆에 건설 시 범위가 2타일로 증가합니다.",
   YuriGagarin: "유리 가가린",
   ZagrosMountains: "Zagros Mountains",
   ZagrosMountainsDesc: "All adjacent buildings that have less than 5 Production Multiplier get +2 Production Multiplier. Double the effect of Nebuchadnezzar II (Great Person)",
   ZahaHadid: "자하 하디드",
   ZahaHadidDesc: "건설자 용량 배수 +%{value}",
   Zenobia: "제노비아",
   ZenobiaDesc: "페트라 워프 최대치 +%{value} 시간",
   ZhengHe: "정화",
   ZigguratOfUr: "우르의 지구라트",
   ZigguratOfUrDescV2: "행복도 10마다 (최대치 50) 노동자를 생산하지 않고 이전 시대에 해제된 모든 건물에 +1 생산 배율을 제공합니다 (최대 = 해제된 시대 수 / 2). 불가사의(자연 불가사의 포함)는 더 이상 +1 행복도를 제공하지 않습니다. 이 효과는 끄고 킬 수 있습니다.",
   Zoroaster: "조로아스터",
   Zugspitze: "Zugspitze",
   ZugspitzeDesc: "For each unlocked age, get one point that can be used to provide one extra level to any Great Person that is born from this run",
};
