{"name": "CivIdle", "productName": "cividle", "version": "1.0.0", "description": "CivIdle is an idle/incremental game that allows you to lead your own civilization through thousands of years from ancient times to modern era", "main": "compiled/index.js", "scripts": {"clean": "node clean.js", "build": "tsc", "start": "npm run build && electron-forge start -- --in-process-gpu --disable-direct-composition", "package": "npm run clean && npm run build && electron-forge package", "make": "npm run build && electron-forge make", "publish": "npm run build && electron-forge publish", "lint": "echo \"No linting configured\""}, "keywords": [], "author": {"name": "Fish Pond Studio", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"fs-extra": "^11.1.0", "mime": "^3.0.0", "@fishpondstudio/steamworks.js": "^0.3.7"}, "devDependencies": {"@electron-forge/cli": "^6.0.4", "@electron-forge/maker-zip": "^6.0.4", "@types/fs-extra": "^11.0.1", "@types/mime": "^3.0.4", "electron": "22.3.27", "typescript": "^5.3.3"}}