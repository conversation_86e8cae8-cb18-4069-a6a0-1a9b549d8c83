export const DE = {
   About: "Über CivIdle",
   AbuSimbel: "Abu Simbel",
   AbuSimbelDesc: "Verdoppelt den Effekt von Ramsses II. Alle angrenzenden Wunder generieren +1 Zufriedenheit",
   AccountActiveTrade: "Aktiver Handel",
   AccountChatBadge: "Chat-Abzeichen",
   AccountCustomColor: "Benutzerdefinierte Farbe",
   AccountCustomColorDefault: "Voreinstellung",
   AccountGreatPeopleLevelRequirement: "Required Great People Level",
   AccountLevel: "Konto-Rang",
   AccountLevelAedile: "Ädil",
   AccountLevelConsul: "Konsul",
   AccountLevelMod: "Moderator",
   AccountLevelPlayTime: "Aktive Online-Spielzeit > %{requiredTime} (Deine <PERSON>lzeit ist %{actualTime})",
   AccountLevelPraetor: "Prätor",
   AccountLevelQuaestor: "Quästor",
   AccountLevelSupporterPack: "Besitzt Supporter Pack",
   AccountLevelTribune: "Tribune",
   AccountLevelUpgradeConditionAnyHTML: "Um dein Konto zu aktualisieren, ist es erforderlich, nur <b>eines der folgenden Kriterien</b> zu erfüllen:",
   AccountPlayTimeRequirement: "Required Play Time",
   AccountRankUp: "Upgrade Account Rank",
   AccountRankUpDesc: "All your progress will be carried over to your new rank",
   AccountRankUpTip: "Congratulations, your account is eligible for a higher rank - click here to upgrade!",
   AccountSupporter: "Supporter Pack Besitzer",
   AccountTradePriceRange: "Handels-Preisspanne",
   AccountTradeTileReservationTime: "Reservierung von Handelskacheln",
   AccountTradeTileReservationTimeDesc: "Dies ist die Zeit, in der dein Gebiet auf der Weltkarte noch für dich reserviert ist. Nach Ablauf der Reservierungsfrist können andere Spieler es für sich beanspruchen.",
   AccountTradeValuePerMinute: "Handelswert pro Minute",
   AccountTypeShowDetails: "Kontodetails anzeigen",
   AccountUpgradeButton: "Upgrade auf Quästor-Rang",
   AccountUpgradeConfirm: "Konto-Upgrade",
   AccountUpgradeConfirmDescV2: "Das Hochstufen deines Kontos <b>setzt den aktuellen Lauf zurück</b> und überträgt die permanenten herausragenden Persönlichkeiten im Rahmen der erlaubten Grenzen. Dies <b>kann nicht</b> rückgängig gemacht werden - soll wirklich fortgefahren werden?",
   Acknowledge: "Acknowledge",
   Acropolis: "Akropolis",
   ActorsGuild: "Schauspielergilde",
   AdaLovelace: "Ada Lovelace",
   AdamSmith: "Adam Smith",
   AdjustBuildingCapacity: "Produktivität",
   AdvisorElectricityContent:
      "Kraftwerke stellen zwei neue Systeme bereit. Erstens: 'Stromversorgung' wird durch das Strom-Symbol auf den benachbarten Feldern des Kraftwerks gekennzeichnet. Einige Gebäude (beginnend mit Radio im Zeitalter der Weltkriege) haben eine 'braucht Stromversorgung' Anforderung in der Verbrauchsliste. <b>Das bedeutet, sie müssen auf einem Feld mit Strom-Symbol gebaut werden, um zu funktionieren</b>. Gebäude, die Stromversorgung benötigen und diese haben, leiten Strom auch an benachbarte Felder weiter. D.h. man kann Ketten / Linien der Stromversorgung errichten, so lange mindestens ein Gebäude an ein Kraftwerk grenzt.<br><br>Zweitens: Das andere System 'Elektrifizierung' kan auf <b>alle Gebäude überall</b> auf der Karte angewendet werden - solange das Gebäude weder Arbeiter noch Wissenschaft produziert. Dabei wird der vom Kraftwerk erzeugte Strom verbraucht, um Verbrauch und Produktion des Gebäudes zu steigern. Höhere Stufen der Elektrifizierung erfordern mehr und mehr Strom. Die Elektrifizierung von Gebäuden, die eine Stromversorgung brauchen (z.B. Radio), ist effizienter als die Elektrifizierung von Gebäuden, die keine Stromversorgung brauchen (z.B. Börse).",
   AdvisorElectricityTitle: "Strom und Elektrifizierung",
   AdvisorGreatPeopleContent:
      "Jedes Mal, wenn Sie ein neues technologisches Zeitalter betreten, können Sie eine herausragende Persönlichkeit aus diesem und jedem vorherigen Zeitalter auswählen. Diese herausragende Persönlichkeiten gewähren globale Boni, die Produktion, Wissenschaft, Zufriedenheit und vieles mehr steigern können.<br><br>Diese Boni sind für den Rest der Wiedergeburt permanent. Wenn Sie wiedergeboren werden, werden alle Ihre herausragende Persönlichkeiten permanent und ihr Bonus bleibt für immer bestehen.<br><br>Wenn Sie in einem späteren Lauf dieselbe auswählen, werden Ihr permanenter und Ihr In-Run-Bonus gestapelt, und wenn Sie mit Duplikaten wiedergeboren werden, werden die Extras gespeichert und können verwendet werden, um den permanenten Bonus zu verbessern. Sie erreichen dies über das Menü <b>Permanente herausragende Persönlichkeiten verwalten</b> in Ihrem Wohngebäude.",
   AdvisorGreatPeopleTitle: "Herausragende Persönlichkeit",
   AdvisorHappinessContent:
      "Zufriedenheit ist der Kernmechanismus in CivIdle, der die Expansion begrenzt. Du erlangst Zufriedenheit, indem du neue Technologien freischaltest, in neue Zeitalter vordringst, Wunder baust, von herausragende Persönlichkeiten, die diese bereitstellen, und auf einige andere Arten, die im Laufe deines Lernens entdecken kannst. <b>Jedes neue Gebäude kostet 1 Zufriedenheit</b>. Für jeden Punkt über/unter 0 Zufriedenheit erhälst du einen Bonus oder eine Strafe von 2 % auf Gesamtzahl an Arbeitern (Begrenzung bei -50 und +50 Zufriedenheit). Du kannst eine detaillierte Aufschlüsselung deiner Zufriedenheit im Abschnitt <b>Zufriedenheit des Wohngebäudes</b> sehen.",
   AdvisorHappinessTitle: "Halte deine Arbeiter bei Laune",
   AdvisorOkay: "Verstanden, danke!",
   AdvisorScienceContent:
      "Deine fleißigen Arbeiter erzeugen Wissenschaft, mit der du neue Technologien freischalten und deine Zivilisation voranbringen kannst. Du kannst das Forschungsmenü auf verschiedene Weise aufrufen. Klicke auf die Wissenschaftsanzeige, greife in deinem Wohngebäude auf deine freischaltbaren Technologien zu oder verwenden das Menü „Ansicht“. All dies führt dich zum Technologiebaum, der dir alle Technologien sowie den für jede Technologie erforderlichen Wissenschaftsaufwand anzeigt. Wenn du über genügend Wissenschaft verfügen, um eine neue Technologie zu erlernen, klicke einfach darauf und drücke im Seitenleistenmenü auf „Freischalten“. <b>Jede neue Stufe und jedes neue Technologiezeitalter erfordert mehr und mehr Wissenschaft, aber du wirst auch neue und bessere Möglichkeiten freischalten, um Wissenschaft zu erlangen.</b>",
   AdvisorScienceTitle: "Wissenschaftliche Entdeckung!",
   AdvisorSkipAllTutorials: "Alle Tutorials überspringen",
   AdvisorStorageContent:
      "Gebäude verfügen zwar über eine anständige Lagerkapazität, können sich jedoch füllen, insbesondere wenn sie längere Zeit unbenutzt bleiben. <b>Wenn Gebäude voll sind, können sie nicht mehr produzieren</b>. Dies ist nicht immer ein Problem, da Sie offensichtlich einen großes Lager haben, da das Gebäude voll ist. Aber es ist im Allgemeinen besser, die Produktion aufrechtzuerhalten.<br><br>Eine Möglichkeit, das Problem des vollen Lagers zu lösen, ist ein Lagerhaus. Wenn du ein Lagerhaus baust, erhälst du ein Menü mit allen Produkten, die du entdeckt hast, und du kannst das Lagerhaus so einstellen, dass es alle Produkte in beliebiger Menge abruft, solange die Gesamtsumme aller Produkte innerhalb dessen liegt, was das Lagerhaus basierend auf seinem Level und Lagermultiplikator zur verfügung hat.<br><br>Eine einfache Möglichkeit, ein Lagerhaus einzurichten, besteht darin, jedes Produkt, das in das Lagerhaus importieren möchtest, auszuwählen und die Schaltflächen „Unter Auswahl neu verteilen“ zu verwenden, um deine Importrate und deinen Lagerbestand gleichmäßig aufzuteilen. Wenn du möchtest, dass Gebäude auch aus dem Lagerhaus abrufbar sind, aktiviere auch die Option „Unter Maximalmenge exportieren“.",
   AdvisorStorageTitle: "Lagerung und Lager",
   AdvisorTraditionContent:
      "Einige Wunder (Chogha Zanbil, Luxor-Tempel, Big Ben) bieten Zugriff auf eine Reihe neuer Optionen, mit denen der Weg deiner Wiedergeburt individuell gestalten kannst. Bei jedem Wunder kannst du jeweils eine von vier Optionen für die Tradition, Religion und Ideologie deiner Zivilisation wählen.<br><br>Sobald du eine Option gewählt hast, ist diese für diese Wiedergeburt gesperrt, du kannst jedoch bei zukünftigen Wiedergeburten andere auswählen. Nach der Wahl kann jedes Wunder auch mehrmals verbessert werden, indem die erforderlichen Ressourcen bereitgestellt werden. Die Belohnungen in jeder Stufe sind kumulativ, sodass Stufe 1 +1 Produktion für X und Stufe 2 +1 Produktion für X gibt, was bedeutet, dass du auf Stufe 2 insgesamt +2 Produktion für X hast.",
   AdvisorTraditionTitle: "Auswahl der Pfade und aufrüstbare Wunder",
   AdvisorWonderContent:
      "Wunder sind besondere Gebäude, die globale Effekte bieten, die dein Spiel erheblich beeinflussen können. Zusätzlich zu ihren aufgeführten Funktionen geben alle Wunder auch +1 Zufriedenheit. Du musst jedoch vorsichtig sein, da <b>Wunder VIELE Materialien erfordern und außerdem eine höhere Baukapazität als normal haben</b>. Das bedeutet, dass sie Ihre Vorräte an benötigten Inputs leicht aufbrauchen können, sodass deine anderen Gebäude unterversorgt sind. <b>Du kannst jeden Input frei ein- und ausschalten</b>, sodass schrittweise gebaut werden kann, während du genügend Materialien hortest, um alles am Laufen zu halten.",
   AdvisorWonderTitle: "Wonders Of The World",
   AdvisorWorkerContent:
      "Jedes Mal, wenn ein Gebäude Güter produziert oder transportiert, werden Arbeiter benötigt. Wenn du nicht genügend Arbeiter zur Verfügung hast, kannst du einige Gebäude diesen Zyklus nicht ausführen. Die offensichtliche Lösung hierfür besteht darin, die Gesamtzahl deiner verfügbaren Arbeiter zu erhöhen, indem du Gebäude baust oder verbesserst, die Arbeiter produzieren (Hütte/Haus/Wohnung/Eigentumswohnung).<br><br><b>Beachte jedoch, dass Gebäude während der Verbesserung ausgeschaltet werden und keine ihrer Ressourcen (einschließlich Arbeiter) bereitstellen können. Du solttest daher möglicherweise immer nur ein Wohngebäude gleichzeitig verbessern.</b> Ein gutes Ziel für die frühen Phasen des Spiels ist es, etwa 70 % deiner Arbeiter beschäftigt zu halten. Wenn mehr als 70 % beschäftigt sind, verbessere/baue Wohnraum. Wenn weniger als 70 % beschäftigt sind, erweitere die Produktion.",
   AdvisorWorkerTitle: "Verwaltung der Arbeitskräfte",
   Aeschylus: "Aischylos",
   Agamemnon: "Agamemnon",
   AgeWisdom: "Age Wisdom",
   AgeWisdomDescHTML: "Each level of Age Wisdom provides <b>an equivalent level</b> of eligible Permanent Great People of that age - it can be upgraded with eligible Permanent Great People shards",
   AgeWisdomGreatPeopleShardsNeeded: "Du brauchst %{amount} weitere Scherben herausragender Persönlichkeiten für das nächste Age Wisdom-Upgrade",
   AgeWisdomGreatPeopleShardsSatisfied: "Du hast genug herausragende Persönlichkeits-Splitter für das nächste Age Wisdom-Upgrade",
   AgeWisdomNeedMoreGreatPeopleShards: "Brauche mehr Scherben für herausragende Persönlichkeit",
   AgeWisdomNotEligible: "Diese herausragende Persönlichkeit hat keinen Anspruch auf Age Wisdom",
   AgeWisdomSource: "%{age} Weisheit: %{person}",
   AgeWisdomUpgradeWarningHTMLV3: "Age Wisdom <b>does not carry over</b> when upgrading from Tribune to Quaestor",
   AGreatPersonIsBorn: "Eine herausragende Persönlichkeit wird geboren",
   AircraftCarrier: "Flugzeugträger",
   AircraftCarrierYard: "Flugzeugträgerwerft",
   Airplane: "Flugzeug",
   AirplaneFactory: "Flugzeugfabrik",
   Akitu: "Akitu: Ziggurat von Ur und Euphrat-Fluss gelten für Gebäude, die im aktuellen Zeitalter freigeschaltet werden",
   AlanTuring: "Alan Turing",
   AlanTuringDesc: "+%{value} Wissenschaft von untätigen Arbeitern",
   AlbertEinstein: "Albert Einstein",
   Alcohol: "Alkohol",
   AldersonDisk: "Alderson-Scheibe",
   AldersonDiskDesc: "+25 Zufriedenheit. Dieses Wunder kann ausgebaut werden. Jeder Ausbau gewährt weitere +5 Zufriedenheit.",
   Alloy: "Legierung",
   Alps: "Alpen",
   AlpsDesc: "Bei jeder 10. Stufe eines Gebäudes erhöht sich der Multiplikator für Verbrauch und Produktion um 1.",
   Aluminum: "Aluminium",
   AluminumSmelter: "Aluminiumschmelze",
   AmeliaEarhart: "Amelia Earhart",
   American: "American",
   AndrewCarnegie: "Andrew Carnegie",
   AngkorWat: "Angkor Wat",
   AngkorWatDesc: "Bei allen angrenzenden Gebäuden wird der Multiplikator für die Arbeitskräfte um 1 erhöht. Zusätzlich werden 1.000 Arbeitskräfte bereitgestellt.",
   AntiCheatFailure: "Dein Konto wurde eingeschränkt, da du die Anti-Cheat-Prüfung <b>nicht bestanden hast</b>. Kontaktiere den Entwickler, wenn du dagegen Einspruch erheben möchtest.",
   AoiMatsuri: "Aoi Matsuri: Der Berg Fuji erzeugt die doppelte Verwerfung",
   Apartment: "Wohnhäuser",
   Aphrodite: "Aphrodite",
   AphroditeDescV2: "+1 Multiplikator für Bauarbeiter for jede Gebäude-Ausbaustufe wenn Gebäude über Stufe 20 ausgebaut werden. Alle freigeschalteten permanenten großen Persönlichkeiten der <b>Antike</b> erhalten +1 Stufe in diesem Durchlauf (d.h. bis zur Wiedergeburt).",
   ApolloProgram: "Apollo Program",
   ApolloProgramDesc: "Alle Raketenfabriken erhalten +2 Produktions-, Arbeitskapazitäts- und Lagermultiplikatoren. Satellitenfabriken, Raumschiff-Fabriken und Atomraketen-Silos erhalten +1 Produktionsmultiplikator für jede benachbarte Raketenfabrik.",
   ApplyToAll: "Auf alle anwenden",
   ApplyToAllBuilding: "Auf alle %{building} anwenden",
   ApplyToBuildingInTile: "Auf alle %{building} innerhalb von %{tile} Kacheln anwenden",
   ApplyToBuildingsToastHTML: "Erfolgreich angewendet auf <b>%{count} %{building}</b>",
   Aqueduct: "Aquädukt",
   ArcDeTriomphe: "Arc de Triomphe",
   ArcDeTriompheDescV2: "Every 1 happiness (capped) provides +1 builder capacity to all buildings",
   Archimedes: "Archimedes",
   Architecture: "Architektur",
   Aristophanes: "Aristophanes",
   AristophanesDesc: "+%{value} Zufriedenheit",
   Aristotle: "Aristotle",
   Arithmetic: "Arithmetik",
   Armor: "Rüstung",
   Armory: "Waffenkammer",
   ArtificialIntelligence: "Künstliche Intelligenz",
   Artillery: "Artillerie",
   ArtilleryFactory: "Artilleriefabrik",
   AshokaTheGreat: "Ashoka the Great",
   Ashurbanipal: "Ashurbanipal",
   Assembly: "Montage",
   Astronomy: "Astronomie",
   AtomicBomb: "Atombombe",
   AtomicFacility: "Atomare Anlage",
   AtomicTheory: "Atomwissenschaft",
   Atomium: "Atomium",
   AtomiumDescV2:
      "Alle Gebäude, die im Umkreis von 2 Kacheln Wissenschaft produzieren (außer Klon-Forschung), erhalten einen Produktionsmultiplikator von +5. Erzeugt Wissenschaft, die der Wissenschaftsproduktion im Umkreis von 2 Kacheln entspricht. Bei Fertigstellung wird einmalig Wissenschaft in Höhe der Kosten der teuersten freigeschalteten Technologie erzeugt.",
   Autocracy: "Autokratie",
   Aviation: "Luftfahrt",
   Babylonian: "Babylonisch",
   BackToCity: "Zurück in die Stadt",
   BackupRecovery: "Backup-Wiederherstellung",
   Bakery: "Bäckerei",
   Ballistics: "Ballistik",
   Bank: "Bank",
   Banking: "Bankwesen",
   BankingAdditionalUpgrade: "Bei allen Gebäuden, die mindestens auf Stufe 10 ausgebaut wurden, erhöht sich der Multiplikator für's Lager um 1",
   Banknote: "Banknoten",
   BaseCapacity: "Basiskapazität",
   BaseConsumption: "Grundverbrauch",
   BaseMultiplier: "Basismultiplikator",
   BaseProduction: "Basisproduktion",
   BastilleDay: "Bastille Day: Double the effect of Centre Pompidou and Arc de Triomphe. Double the Culture generation from Mont Saint-Michel",
   BatchModeTooltip: "%{count} Gebäude sind derzeit ausgewählt. Upgrade wird auf alle ausgewählten Gebäude angewendet",
   BatchSelectAllSameType: "Alle gleichen Typs",
   BatchSelectAnyType1Tile: "Alle Typen, Abstand 1 Kachel",
   BatchSelectAnyType2Tile: "Alle Typen, Abstand 2 Kacheln",
   BatchSelectAnyType3Tile: "Alle Typen, Abstand 3 Kacheln",
   BatchSelectSameType1Tile: "Gleicher Typ, Abstand 1 Kachel",
   BatchSelectSameType2Tile: "Gleicher Typ, Abstand 2 Kacheln",
   BatchSelectSameType3Tile: "Gleicher Typ, Abstand 3 Kacheln",
   BatchSelectSameTypeSameLevel: "Same Type Same Level",
   BatchSelectThisBuilding: "Dieses Gebäude",
   BatchStateSelectActive: "Active",
   BatchStateSelectAll: "All",
   BatchStateSelectTurnedFullStorage: "Full Storage",
   BatchStateSelectTurnedOff: "Turned Off",
   BatchUpgrade: "Batch Upgrade",
   Battleship: "Schlachtschiff",
   BattleshipBuilder: "Schlachtschiff-Werft",
   BigBen: "Big Ben",
   BigBenDesc: "+2 Wissenschaft von vielbeschäftigten Arbeitern. Wähle eine Imperiums-Ideologie, schalte mit jeder Wahl mehr Boost frei",
   Biplane: "Doppeldecker",
   BiplaneFactory: "Doppeldeckerfabrik",
   Bitcoin: "Bitcoin",
   BitcoinMiner: "Bitcoin Miner",
   BlackForest: "Schwarzwald",
   BlackForestDesc: "Wenn er entdeckt wird, werden alle Holz-Kacheln auf der Karte aufgedeckt. Erzeugt Holz auf angrenzenden Feldern. Alle Gebäude, die Holz verbrauchen, erhalten einen Produktionsmultiplikator von +5.",
   Blacksmith: "Schmiede",
   Blockchain: "Blockchain",
   BlueMosque: "Blue Mosque",
   BlueMosqueDesc: "All wonders provide +1 Production, Worker Capacity and Storage Multiplier to adjacent buildings. When constructed next to Hagia Sophia, provide extra +1 Production, Worker Capacity and Storage Multiplier",
   BobHope: "Bob Hope",
   BobHopeDesc: "+%{value} Zufriedenheit",
   Bond: "Anleihe",
   BondMarket: "Anleihenmarkt",
   Book: "Buch",
   BoostCyclesLeft: "Boost Cycles Left",
   BoostDescription: "+%{value} %{multipliers} für %{buildings}",
   Borobudur: "Borobudur",
   BorobudurDesc: "Borobudur",
   BranCastle: "Burg Bran",
   BranCastleDesc: "Burg Bran",
   BrandenburgGate: "Brandenburger Tor",
   BrandenburgGateDesc: "Der Multiplikator für Produktion, Lagerung und Arbeitskräfte erhöht sich bei allen Kohleminen und Ölquellen um 1. Zusätzlich erhöht sich der Multiplikator für Produktion, Lagerung und von Ölraffinerien um die Anzahl angrenzender Ölquellen.",
   Bread: "Brot",
   Brewery: "Brauerei",
   Brick: "Ziegelstein",
   Brickworks: "Ziegelbrennerei",
   BritishMuseum: "British Museum",
   BritishMuseumChooseWonder: "Choose a Wonder",
   BritishMuseumDesc: "After constructed, can transform into to a unique wonder from other civilizations",
   BritishMuseumTransform: "Transform",
   Broadway: "Broadway",
   BroadwayCurrentlySelected: "Currently selected",
   BroadwayDesc: "Eine herausragende Persönlichkeit des aktuellen Zeitalters und eine herausragende Persönlichkeit des vorherigen Zeitalters werden geboren. Wähle eine herausragende Persönlichkeit und verdopple ihre Wirkung",
   BronzeAge: "Bronzezeit",
   BronzeTech: "Bronzeverarbeitung",
   BuddhismLevelX: "Buddhismus %{level}",
   Build: "Bauen",
   BuilderCapacity: "Anzahl Baumeister",
   BuildingColor: "Gebäudefarbe",
   BuildingColorMatchBuilding: "Gebäudefarbe kopieren",
   BuildingColorMatchBuildingTooltip: "Kopiert die Ressourcenfarbe aus dem Gebäude, das diese Ressource produziert. Wenn mehrere Gebäude diese Ressource produzieren, wird ein zufälliges ausgewählt",
   BuildingDefaults: "Gebäudevoreinstellung",
   BuildingDefaultsCount: "%{count} Eigenschaften werden im Gebäude standardmäßig überschrieben",
   BuildingDefaultsRemove: "Alle Eigenschaftsüberschreibungen löschen",
   BuildingEmpireValue: "Wert des Imperiums aus Gebäuden / aus Ressourcen",
   BuildingMultipliers: "Beschleunigung der Baugeschwindigkeit",
   BuildingName: "Name",
   BuildingNoMultiplier: "%{building} wird <b>nicht</b> durch irgendwelche Multiplikatoren (Produktion, Arbeiter Kapazität, Speicher, etc) beeinflusst",
   BuildingSearchText: "Welches Gebäude/welche Ressource soll gesucht werden?",
   BuildingTier: "Stufe",
   Cable: "Kabel",
   CableFactory: "Kabelfabrik",
   Calendar: "Kalender",
   CambridgeUniversity: "Cambridge University",
   CambridgeUniversityDesc: "+1 Age Wisdom level for Renaissance and ages after",
   CambridgeUniversitySource: "Cambridge University (%{age})",
   Cancel: "Abbrechen",
   CancelAllUpgradeDesc: "Cancel all %{building} upgrades",
   CancelUpgrade: "Verbesserung abbrechen",
   CancelUpgradeDesc: "Alle Ressourcen, die bereits geliefert wurden, verbleiben im Lager.",
   Cannon: "Kanone",
   CannonWorkshop: "Kanonen-Gießerei",
   CannotEarnPermanentGreatPeopleDesc: "Da es sich um einen Probelauf handelt, können <b>keine</b> dauerhaften herausragenden Persönlichkeiten verdient werden",
   Capitalism: "Kapitalismus",
   Cappadocia: "Cappadocia",
   CappadociaDesc: "All buildings within 3 tile range get +1 Production, Worker Capacity and Storage Multiplier for every level above Level 30",
   Car: "Auto",
   Caravansary: "Karawanserei",
   CaravansaryDesc: "Tausche Ressourcen mit anderen Spielern und stelle zusätzliches Lager zur Verfügung",
   Caravel: "Karavelle",
   CaravelBuilder: "Karavellen-Werft",
   CarFactory: "Autofabrik",
   CarlFriedrichGauss: "Carl Friedrich Gauss",
   CarlFriedrichGaussDesc: "+%{idle} Wissenschaft von untätigen Arbeitern. +%{busy} Wissenschaft von fleißigen Arbeitern",
   CarlSagan: "Carl Sagan",
   Census: "Volkszählung",
   CentrePompidou: "Centre Pompidou",
   CentrePompidouDesc:
      "Once constructed, all buildings get +1 Production and +2 Storage Multiplier. The wonder will persist if the current run reaches Information Age and the next run is a different civilization. The wonder gets +1 level at rebirth for each run that reaches Information Age with a unique civilization. Each level provides +1 Production and +2 Storage Multiplier. The value of this wonder is excluded from total empire value and British Museum cannot transform into this wonder",
   CentrePompidouWarningHTML: "Centre Pompidou will disappear if you rebirth as <b>%{civ}</b>",
   CerneAbbasGiant: "Cerne Abbas Giant",
   CerneAbbasGiantDesc: "A great person of the current age is born when a wonder is constructed",
   ChangePlayerHandle: "Änderung",
   ChangePlayerHandleCancel: "Abbrechen",
   ChangePlayerHandledDesc: "Ihr Spielername muss zwischen 5 und 16 Buchstaben und Zahlen enthalten. Außerdem darf er noch nicht von einem anderen Spieler gewählt worden sein",
   Chariot: "Streitwagen",
   ChariotWorkshop: "Streitwagenwerkstatt",
   Charlemagne: "Karl der Große",
   CharlesDarwin: "Charles Darwin",
   CharlesDarwinDesc: "+%{value} Forschung durch beschäftigte Arbeitskräfte",
   CharlesMartinHall: "Charles Martin Hall",
   CharlesParsons: "Charles Parsons",
   CharlieChaplin: "Charlie Chaplin",
   CharlieChaplinDesc: "+%{value} Zufriedenheit",
   Chat: "Chatten",
   ChatChannel: "Chat-Kanal",
   ChatChannelLanguage: "Sprache",
   ChatHideLatestMessage: "Neuesten Nachrichteninhalt ausblenden",
   ChatNoMessage: "Keine Chat-Nachrichten",
   ChatReconnect: "Getrennt, Verbindung wird wieder hergestellt...",
   ChatSend: "Senden",
   CheckInAndExit: "Check In And Exit",
   CheckInCloudSave: "Check In Save",
   CheckOutCloudSave: "Check Out Save",
   Cheese: "Käse",
   CheeseMaker: "Käserei",
   Chemistry: "Chemie",
   ChesterWNimitz: "Chester W. Nimitz",
   ChichenItza: "Chichén Itzá",
   ChichenItzaDesc: "Bei allen angrenzenden Gebäuden erhöht sich der Multiplikator für Produktion, Lagervolumen und Arbeitskräfte um 1.",
   Chinese: "Chinesisch",
   ChoghaZanbil: "Chogha Zanbil",
   ChoghaZanbilDescV2: "Wähle eine Reichstradition und schalte mit jeder Wahl einen weiteren Boost frei",
   ChooseGreatPersonChoicesLeft: "Du kannst noch %{count} herausragende Persönlichkeiten auswählen.",
   ChristianityLevelX: "Christentum %{level}",
   Church: "Kirche",
   CircusMaximus: "Zirkus Maximus",
   CircusMaximusDescV2: "+5 Zufriedenheit. Bei sämtlichen Musiker-, Schriftsteller- und Malergilden steigt der Multiplikator für Produktion und Lagerkapazität um 1",
   CityState: "Stadtstaat",
   CityViewMap: "Stadt",
   CivGPT: "CivGPT",
   CivIdle: "CivIdle",
   CivIdleInfo: "stolz präsentiert vom Fish Pond Studio",
   Civilization: "Civilization",
   CivilService: "Öffentlicher Dienst",
   CivOasis: "CivOasis",
   CivTok: "CivTok",
   ClaimedGreatPeople: "beanspruchte herausragende Persönlichkeiten",
   ClaimedGreatPeopleTooltip: "Du hast %{total} herausragende Persönlichkeiten bei Wiedergeburt, %{claimed} davon sind bereits beansprucht",
   ClassicalAge: "Antike",
   ClearAfterUpdate: "Alle Handelsangebote nach dem Markt-Update löschen",
   ClearSelected: "Auswahl löschen",
   ClearSelection: "Löschen",
   ClearTransportPlanCache: "Transportplan-Cache löschen",
   Cleopatra: "Cleopatra",
   CloneFactory: "Klon-Fabrik",
   CloneFactoryDesc: "Klont beliebige Ressourcen",
   CloneFactoryInputDescHTML: "Die Klonfabrik kann nur <b>%{res}</b> klonen, die direkt von <b>%{buildings}</b> transportiert wurden.",
   CloneLab: "Klon-Forschung",
   CloneLabDesc: "Wandelt beliebige Ressourcen in Forschung um",
   CloneLabScienceMultiplierHTML: "Produktionsmultiplikatoren, die <b>nur für wissenschaftliche Produktionsgebäude gelten</b> (z. B. Produktionsmultiplikatoren von Atomium), <b>gelten</b> nicht für Clone Lab",
   Cloth: "Stoffe",
   CloudComputing: "Cloud Computing",
   CloudSaveRefresh: "Refresh",
   CloudSaveReturnToGame: "Return To Game",
   CNTower: "CN Tower",
   CNTowerDesc: "Alle Filmstudios, Radiostationen und Fernsehsender sind von -1 Zufriedenheit ausgenommen. Alle Gebäude, die in den Weltkriegen und im Kalten Krieg freigeschaltet werden, erhalten +N Produktions-, Arbeitskraft- und Lagermultiplikator. N = Zeitalter des Gebäudes - Stufe des Gebäudes.",
   Coal: "Kohle",
   CoalMine: "Kohlebergwerk",
   CoalPowerPlant: "Kohlekraftwerk",
   Coin: "Münze",
   CoinMint: "Münzprägerei",
   ColdWarAge: "Kalter Krieg",
   CologneCathedral: "Kölner Dom",
   CologneCathedralDesc:
      "Erzeugt beim Bau einmalig Wissenschaft, die den Kosten der teuersten Technologie des aktuellen Zeitalters entspricht. Alle Gebäude, die Wissenschaft produzieren (außer dem Klonlabor), erhalten einen Produktionsmultiplikator von +1. Dieses Wunder kann aufgerüstet werden und jede zusätzliche Aufrüstung verleiht allen Gebäuden, die Wissenschaft produzieren (außer dem Klonlabor), einen Produktionsmultiplikator von +1.",
   Colonialism: "Kolonialismus",
   Colosseum: "Kolosseum",
   ColosseumDescV2: "Streitwagenwerkstätten Chariot sind von -1 Zufriedenheit ausgenommen. Verbraucht 10 Streitwagen und erzeugt 10 Zufriedenheit. Jedes freigeschaltete Zeitalter erzeugt +2 zusätzliche Zufriedenheit.",
   ColossusOfRhodes: "Koloss von Rhodos",
   ColossusOfRhodesDesc: "Durch jedes angrenzenden Gebäude, das keine Arbeitskräfte produziert, erhöht sich die Zufriedenheit um 1",
   Combustion: "Verbrennung",
   Commerce4UpgradeHTMLV2: "Wenn freigeschaltet, erhalten alle <b>angrenzenden Banken</b> ein kostenloses Upgrade auf Stufe 30",
   CommerceLevelX: "Handel %{level}",
   Communism: "Kommunismus",
   CommunismLevel4DescHTML: "Eine herausragende Persönlichkeit des <b>Industriezeitalters</b> und eine große Persönlichkeit des <b>Zeitalters der Weltkriege</b> werden geboren",
   CommunismLevel5DescHTML: "Eine herausragende Persönlichkeit des <b>Kalten Krieges</b> wird geboren. Wenn du in ein neues Zeitalter eintrittst, erhälst du <b>2 weitere</b> herausragende Persönlichkeiten dieses Zeitalters",
   CommunismLevelX: "Kommunismus Level %{level}",
   Computer: "Computer",
   ComputerFactory: "Computerfabrik",
   ComputerLab: "Computer Lab",
   Concrete: "Beton",
   ConcretePlant: "Zementfabrik",
   Condo: "Condo",
   ConfirmDestroyResourceContent: "Du bist im Begriff, %{amount} %{resource} zu zerstören. Dies kann nicht rückgängig gemacht werden",
   ConfirmNo: "Nein",
   ConfirmYes: "Ja",
   Confucius: "Konfuzius",
   ConfuciusDescV2: "+%{value} Wissenschaft von allen Arbeitern, wenn mehr als 50% der Arbeiter beschäftigt sind und weniger als 50% der beschäftigten Arbeiter im Transportwesen arbeiten",
   ConnectToADevice: "Connect To A Device",
   Conservatism: "Konservatismus",
   ConservatismLevelX: "Konservatismus Level %{level}",
   Constitution: "Verfassung",
   Construction: "Konstruktion",
   ConstructionBuilderBaseCapacity: "Grundmenge an Bauarbeitern",
   ConstructionBuilderCapacity: "Bauarbeiter",
   ConstructionBuilderMultiplier: "Multiplikator für Bauarbeiter",
   ConstructionBuilderMultiplierFull: "Gesamter Multiplikator für Bauarbeiter",
   ConstructionCost: "Baukosten: %{cost}",
   ConstructionDelivered: "Geliefert",
   ConstructionPriority: "Priorität Bau",
   ConstructionProgress: "Fortschritt",
   ConstructionResource: "Ressource",
   Consume: "verbraucht",
   ConsumeResource: "Verbrauch: %{resource}",
   ConsumptionMultiplier: "Verbrauchsmultiplikator",
   ContentInDevelopment: "Inhalte in Entwicklung",
   ContentInDevelopmentDesc: "Dieser Spielinhalt befindet sich noch in der Entwicklung und wird in einem zukünftigen Spielupdate verfügbar sein, bleib dran!",
   Copper: "Kupfer",
   CopperMiningCamp: "Kupferbergwerk",
   CosimoDeMedici: "Cosimo de' Medici",
   Cotton: "Baumwolle",
   CottonMill: "Baumwollspinnerei",
   CottonPlantation: "Baumwollplantage",
   Counting: "Zählung",
   Courthouse: "Gerichtsgebäude",
   CristoRedentor: "Christus, der Erlöser",
   CristoRedentorDesc: "Alle Gebäude im Umkreis von 2 Feldern sind von -1 Zufriedenheit ausgenommen.",
   CrossPlatformAccount: "Platform Account",
   CrossPlatformConnect: "Connect",
   CrossPlatformSave: "Cross Platform Save",
   CrossPlatformSaveLastCheckIn: "Last Check In",
   CrossPlatformSaveStatus: "Current Status",
   CrossPlatformSaveStatusCheckedIn: "Checked In",
   CrossPlatformSaveStatusCheckedOut: "Checked Out on %{platform}",
   CrossPlatformSaveStatusCheckedOutTooltip: "Your cross platform save has been checked out on another platform, you have to check in on that platform before you can check out on this platform",
   Cultivation4UpgradeHTML: "Eine herausragende Persönlichkeit des <b>Renaissancezeitalters</b> wird geboren",
   CultivationLevelX: "Kultivierung %{level}",
   Culture: "Kultur",
   Culus: "Cülus: Double the effect of Cappadocia. Mount Ararat's effect becomes based on square root of Effective Great People Level, instead of cubic root",
   CurrentLanguage: "Deutsch",
   CurrentPlatform: "Current Platform",
   CursorBigOldFashioned: "3D (gross)",
   CursorOldFashioned: "3D",
   CursorStyle: "Cursor-Stil",
   CursorStyleDescHTML: "Anpassen des Cursor-Stils. <b>Erfordert einen Neustart des Spiels, um wirksam zu werden</b>",
   CursorSystem: "System",
   Cycle: "Cycle",
   CyrusII: "Kyros II.",
   DairyFarm: "Milchproduktion",
   DefaultBuildingLevel: "Standard-Gebäudelevel",
   DefaultConstructionPriority: "Standard-Konstruktionspriorität",
   DefaultProductionPriority: "Standard-Produktionspriorität",
   DefaultStockpileMax: "Standardmäßiger maximaler Vorrat",
   DefaultStockpileSettings: "Standard-Halden-Eingangskapazität",
   DeficitResources: "Ressourcendefizit",
   Democracy: "Demokratie",
   DemolishAllBuilding: "Reiße alle %{building} innerhalb von %{tile} Kacheln ab",
   DemolishAllBuildingConfirmContent: "Bist du sicher, dass du %{count} %{name} zerstören möchten?",
   DemolishAllBuildingConfirmTitle: "%{count} Gebäude abreißen?",
   DemolishBuilding: "Gebäude abreißen",
   DennisRitchie: "Dennis Ritchie",
   Deposit: "Vorkommen",
   DepositTileCountDesc: "In %{city} gibt es %{count} Kacheln mit %{deposit}.",
   Dido: "Dido",
   Diplomacy: "Diplomatie",
   DistanceInfinity: "Unbegrenzt",
   DistanceInTiles: "Entfernung (in Kacheln)",
   DolmabahcePalace: "Dolmabahçe Palace",
   Drilling: "Bohren",
   DukeOfZhou: "Herzog von Zhou",
   DuneOfPilat: "Dune of Pilat",
   DuneOfPilatDesc: "In each age, double the age wisdom for the previous age",
   DynamicMultiplierTooltip: "Dieser Multiplikator ist dynamisch - er wirkt sich nicht auf Arbeiter und Lagerhaltung aus.",
   Dynamite: "Dynamit",
   DynamiteWorkshop: "Dynamit-Werkstatt",
   DysonSphere: "Dyson-Sphäre",
   DysonSphereDesc: "Alle Gebäude erhalten +5 Produktionsmultiplikator. Dieses Wunder kann ausgebaut werden. Jeder Ausbau gewährt allen Gebäuden +1 Produktionsmultiplikator.",
   EasterBunny: "Easter Bunny",
   EasterBunnyDesc: "Once constructed, 10% of the extra Great People at Rebirth from this run will carry forward to the next run and are born after building the Easter Bunny in the new run. This wonder can only be constructed during April",
   EastIndiaCompany: "East India Company",
   EastIndiaCompanyDescV2:
      "This wonder accumulates the total value of your completed player trade transactions. For every 2,000 trade value, all buildings adjacent to caravansaries get a +0.5 Production Multiplier for 1 cycle. This wonder can be upgraded and each upgrade provides an additional +0.5 Production Multiplier. A trade transaction counts when you either fulfill another player's trade request or when your own trade request is fulfilled. Multiple boosts stack by extending the duration",
   Education: "Bildung",
   EffectiveGreatPeopleLevel: "Effektive herausragender Persönlichkeiten-Stufe",
   EffectiveGreatPeopleLevelDesc: "Die effektive Stufe herausragender Persönlichkeiten ist die Summe aller permanenten Stufen herausragender Persönlichkeiten und der Altersweisheit. Sie misst die Effektverstärkung durch herausragender Persönlichkeiten und Altersweisheit",
   Egyptian: "Egyptian",
   EiffelTower: "Eiffelturm",
   EiffelTowerDesc: "Bei allen angrenzenden Stahlwerken erhöht sich der Multiplikator für Produktion, Lager und Arbeitskräfte um die Anzahl der jeweils angrenzenden Stahlwerke",
   Elbphilharmonie: "Elbphilharmonie",
   ElbphilharmonieDesc: "Alle Gebäude innerhalb von 3 Kacheln erhalten +1 Produktionsmultiplikator für jedes angrenzende funktionierende Gebäude mit unterschiedlicher Stufe.",
   Electricity: "Elektrizität",
   Electrification: "Elektrifizierung",
   ElectrificationPowerRequired: "Elektrifizierung benötigt",
   ElectrificationStatusActive: "Aktiv",
   ElectrificationStatusDesc: "Sowohl Gebäude, die Strom benötigen, als auch Gebäude, die keinen Strom benötigen, können elektrifiziert werden. Allerdings bieten Gebäude, die Strom benötigen, eine höhere Elektrifizierungseffizienz",
   ElectrificationStatusNoPowerV2: "Nicht genug Strom",
   ElectrificationStatusNotActive: "Nicht aktiv",
   ElectrificationStatusV2: "Status der Elektrifizierung",
   ElectrificationUpgrade: "Damit kannst du die Produktivität von Gebäuden erhöhen.",
   Electrolysis: "Elektrolyse",
   ElvisPresley: "Elvis Presley",
   ElyseePalace: "Élysée Palace",
   EmailDeveloper: "Email Entwickler",
   Embassy: "Botschaft",
   EmperorWuOfHan: "Kaiser Wu von Han",
   EmpireValue: "Wert des Imperiums",
   EmpireValueByHour: "Wert des Imperiums pro Stunde",
   EmpireValueFromBuilding: "Wert des Imperiums durch Gebäude",
   EmpireValueFromBuildingsStat: "durch Gebäude",
   EmpireValueFromResources: "Durch Ressourcen",
   EmpireValueFromResourcesStat: "durch Ressourcen",
   EmpireValueIncrease: "Zuwachs des Wertes des Imperiums",
   EmptyTilePageBuildLastBuilding: "Letztes Gebäude bauen",
   EndConstruction: "Bau abbrechen",
   EndConstructionDescHTML: "Wenn Du den Bau abbrichst, gehen alle bereits verbrauchten Ressourcen verloren",
   Engine: "Motor",
   Engineering: "Ingenieurswesen",
   English: "English",
   Enlightenment: "Erleuchtung",
   Enrichment: "Anreicherung",
   EnricoFermi: "Enrico Fermi",
   EstimatedTimeLeft: "Geschätzte verbleibende Zeit",
   EuphratesRiver: "Euphrates River",
   EuphratesRiverDesc:
      "Jede 10% der beschäftigten Arbeiter, die in der Produktion (nicht im Transport) sind, geben +1 Produktionsmultiplikator für alle Gebäude, die keine Arbeiter produzieren (max = Anzahl der freigeschalteten Zeitalter / 2). Wenn der Hängende Garten daneben gebaut wird, erhält der Hängende Garten +1 Effekt für jedes Zeitalter, nachdem der Hängende Garten freigeschaltet wurde. Wenn er entdeckt wird, wird auf allen angrenzenden Plättchen, die keine Ablagerungen haben, Wasser erzeugt.",
   ExpansionLevelX: "Erweiterung %{level}",
   Exploration: "Erkundung",
   Explorer: "Erkunder",
   ExplorerRangeUpgradeDesc: "Erhöht die Reichweite von Erkundern auf %{range}",
   ExploreThisTile: "Erkunder schicken",
   ExploreThisTileHTML: "Ein Erkunder wird <b>dieses Feld und seine Nachbarfelder</b> erforschen. Erkunder werden in %{name} erzeugt. Du hast %{count} Erkunder übrig",
   ExtraGreatPeople: "%{count} Zusätzliche herausragende Persönlichkeiten",
   ExtraGreatPeopleAtReborn: "Zusätzliche herausragende Persönlichkeiten bei der Wiedergeburt",
   ExtraTileInfoType: "Zusätzliche Kachelinformationen",
   ExtraTileInfoTypeDesc: "Wähle, welche Informationen unter jeder Kachel angezeigt werden sollen",
   ExtraTileInfoTypeEmpireValue: "Wert des Reichs",
   ExtraTileInfoTypeNone: "Keine",
   ExtraTileInfoTypeStoragePercentage: "Lagerfüllstand",
   Faith: "Glaube",
   Farming: "Landwirtschaft",
   FavoriteBuildingAdd: "Zu Favoriten hinzufügen",
   FavoriteBuildingEmptyToast: "Du hast keine favorisierten Gebäude",
   FavoriteBuildingRemove: "Aus Favoriten entfernen",
   FeatureRequireQuaestorOrAbove: "Für diese Funktion ist der Rang eines Quästors oder höher erforderlich.",
   Festival: "Festival",
   FestivalCycle: "Festivalzyklus",
   FestivalTechTooltipV2: "Positive Zufriedenheit (max. 50) wird in Festivalpunkte umgewandelt. Für jeweils %{point} Festivalpunkte tritt dein Imperium in einen Festivalzyklus ein, der einen erheblichen kartenspezifischen Boost gewährt. Das Festival auf dieser Karte ist %{desc}",
   FestivalTechV2: "Festival freischalten - positive Zufriedenheit (max. 50) wird in Festivalpunkte umgewandelt. Für jeweils %{point} Festivalpunkte tritt dein Imperium in einen Festivalzyklus ein, der einen erheblichen kartenspezifischen Boost gewährt",
   Feudalism: "Feudalismus",
   Fibonacci: "Fibonacci",
   FibonacciDescV2: "+%{idle} Forschung durch Arbeitskräfte ohne Beschäftigung. +%{busy} Forschung durch beschäftigte Arbeitskräfte. Das Upgrade von Fibonacci erzeugt Kosten entsprechend der Fibonacci-Folge",
   FighterJet: "Kampfjet",
   FighterJetPlant: "Kampfjet-Fabrik",
   FilterByAge: "Filter by Age",
   FinancialArbitrage: "Finanzielle Arbitrage",
   FinancialLeverage: "Finanzielle Hebelwirkung",
   Fire: "Feuer",
   Firearm: "Schusswaffe",
   FirstTimeGuideNext: "Weiter",
   FirstTimeTutorialWelcome: "Wilkommen bei CivIdle",
   FirstTimeTutorialWelcome1HTML:
      "Willkommen bei CivIdle. In diesem Spiel führst du dein eigenes Imperium: <b>Verwalte Produktionen, schalte Technologien frei, handel Ressourcen mit anderen Spielern, erschaffe herausragende Persönlichkeiten und baue Weltwunder</b>.<br><br>Ziehe deine Maus, um dich zu bewegen. Verwende das Scrollrad zum Vergrößern oder Verkleinern. Klicke auf eine leere Kachel, um neue Gebäude zu bauen, und klicke auf ein Gebäude, um es zu inspizieren.<br><br>Bestimmte Gebäude wie Steinbruch und Holzfällerlager müssen auf der Ressourcenkachel gebaut werden. Ich empfehle, eine Hütte, die Arbeiter liefert, neben den Nebel zu stellen – der Bau des Gebäudes wird einige Zeit in Anspruch nehmen. Nach der Fertigstellung wird der Nebel in der Nähe aufgedeckt.",
   FirstTimeTutorialWelcome2HTML:
      "Gebäude können aufgerüstet werden – das kostet Ressourcen und braucht Zeit. Wenn ein Gebäude aufgerüstet wird, <b>wird es nicht mehr produzieren</b>. Dies gilt auch für Gebäude, die Arbeiter beschäftigen, <b>rüste also nie alle deine Gebäude gleichzeitig auf!</b><br><br>Wenn dein Imperium wächst, erhälst du mehr Wissenschaft und schaltest neue Technologien frei. Ich werde dir mehr darüber erzählen, wenn wir soweit sind, aber du kannst unter Ansicht -> Forschung einen kurzen Blick darauf werfen<br><br>",
   FirstTimeTutorialWelcome3HTML:
      "Jetzt, da du alle Grundlagen des Spiels kennst, kannst du mit dem Aufbau deines Imperiums beginnen. Aber bevor ich dich gehen lasse, solltest du <b>einen Spielernamen wählen</b> und im In-Game-Chat Hallo sagen. Wir haben eine unglaublich hilfsbereite Community: Wenn du nicht weiterkommst, zögere nicht zu fragen!",
   Fish: "Fisch",
   FishPond: "Angelteich",
   FlorenceNightingale: "Florence Nightingale",
   FlorenceNightingaleDesc: "+%{value} Zufriedenheit",
   Flour: "Mehl",
   FlourMill: "Mühle",
   FontSizeScale: "Schriftgrößen-Skala",
   FontSizeScaleDescHTML: "Ändert die Skalierung der Schriftgröße auf der Benutzeroberfläche des Spiels.  <b>Eine Einstellung der Skala größer als 1x kann einige Benutzeroberflächen-Layouts zerstören</b>",
   ForbiddenCity: "Verbotene Stadt",
   ForbiddenCityDesc: "Bei allen Papiermachern, Schriftstellergilden und Druckereien wird der Multiplikator für Produktion, Arbeitskräfte und Lagervolumen um 1 erhöht",
   Forex: "Devisen",
   ForexMarket: "Devisenmarkt",
   FrankLloydWright: "Frank Lloyd Wright",
   FrankLloydWrightDesc: "+%{value} Multiplikator für Bauarbeiter",
   FrankWhittle: "Frank Whittle",
   FreeThisWeek: "Diese Woche gratis",
   FreeThisWeekDescHTMLV2: "<b>Jede Woche</b> ist eine der Premium-Zivilisationen kostenlos spielbar. Die kostenlose Zivilisation dieser Woche ist <b>%{city}</b>",
   French: "French",
   Frigate: "Fregatte",
   FrigateBuilder: "Fregatten-Werft",
   Furniture: "Möbel",
   FurnitureWorkshop: "Tischlerei",
   Future: "Zukunft",
   GabrielGarciaMarquez: "Gabriel García Márquez",
   GabrielGarciaMarquezDesc: "+%{value} Zufriedenheit",
   GalileoGalilei: "Galileo Galilei",
   GalileoGalileiDesc: "+%{value} Wissenschaft von untätigen Arbeitern",
   Galleon: "Galeone",
   GalleonBuilder: "Galeonen-Werft",
   Gameplay: "Spielablauf",
   Garment: "Bekleidung",
   GarmentWorkshop: "Schneiderei",
   GasPipeline: "Gas-Pipeline",
   GasPowerPlant: "Gaskraftwerk",
   GatlingGun: "Gatling Gun",
   GatlingGunFactory: "Gatling-Gun-Fabrik",
   Genetics: "Genetik",
   Geography: "Geographie",
   GeorgeCMarshall: "George C. Marshall",
   GeorgeWashington: "George Washington",
   GeorgiusAgricola: "Georgius Agricola",
   German: "Deutsch",
   Glass: "Glas",
   Glassworks: "Glashütte",
   GlobalBuildingDefault: "Globale Gebäudevoreinstellung",
   Globalization: "Globalisierung",
   GoBack: "Gehe zurück",
   Gold: "Gold",
   GoldenGateBridge: "Golden Gate Brücke",
   GoldenGateBridgeDesc: "Alle Kraftwerke erhalten +1 Produktionsmultiplikator. Gewährt Stromversorgung im Umkreis von 2 Kacheln um die Golden Gate Brücke.",
   GoldenPavilion: "Golden Pavilion",
   GoldenPavilionDesc: "Alle Gebäude im Umkreis von 3 Kacheln erhalten einen Produktionsmultiplikator von +1 für jedes benachbarte Gebäude, das eine seiner verbrauchten Ressourcen produziert (ausgenommen Klonlabor und Klonfabrik, und das Gebäude kann nicht abgeschaltet werden).",
   GoldMiningCamp: "Goldschürfer",
   GordonMoore: "Gordon Moore",
   GrandBazaar: "Großer Basar",
   GrandBazaarDesc: "Alle Märkte kontrollieren. Angrenzende Karawansereien erhalten +5 Produktions- und Lagermultiplikator. Angrenzende Märkte erhalten verschiedene Marktangebote",
   GrandBazaarFilters: "Filter",
   GrandBazaarFilterWarningHTML: "Um den Handel irgendeines Markts anzuzeigen, muss vorher ein Filter gewählt werden",
   GrandBazaarFilterYouGet: "Du bekommst",
   GrandBazaarFilterYouPay: "Du bezahlst",
   GrandBazaarSeach: "Search",
   GrandBazaarSearchGet: "Get",
   GrandBazaarSearchPay: "Pay",
   GrandBazaarTabActive: "Aktive",
   GrandBazaarTabTrades: "Händel",
   GrandCanyon: "Grand Canyon",
   GrandCanyonDesc: "Gebäude, die im aktuellen Zeitalter freigeschaltet werden, erhalten +2 Produktionsmultiplikator. Verdopple den Effekt von J.P. Morgan",
   GraphicsDriver: "Grafiktreiber",
   GreatDagonPagoda: "Große Dagon-Pagode",
   GreatDagonPagodaDescV2: "Alle Pagoden sind von -1 Zufriendenheit ausgenommen. Erzeuge Wissenschaft basierend auf der Glaubensproduktion aller Pagoden",
   GreatMosqueOfSamarra: "Große Moschee von Samarra",
   GreatMosqueOfSamarraDescV2: "Sichtweite aller Gebäude erhöht sich um 1. Außerdem werden 5 zufällig ausgewählte Lagerstätten sichtbar. Auf jeder davon wird eine Mine auf Stufe 10 errichtet.",
   GreatPeople: "Herausragende Persönlichkeiten",
   GreatPeopleEffect: "Wirkung",
   GreatPeopleFilter: "Gebe den Namen oder das Alter ein, um tolle herausragende Persönlichkeiten herauszufiltern",
   GreatPeopleName: "Name",
   GreatPeoplePermanentColumn: "in allen weiteren Durchgängen",
   GreatPeoplePermanentShort: "in allen weiteren Durchgängen",
   GreatPeoplePickPerRoll: "Herausragende Persönlichkeiten pro Durchgang",
   GreatPeopleThisRun: "Herausragende Persönlichkeiten in diesem Durchgang",
   GreatPeopleThisRunColumn: "Dieser Durchgang",
   GreatPeopleThisRunShort: "Dieser Durchgang",
   GreatPersonLevelRequired: "Dauerhaftes „Großartige Leute“-Level erforderlich",
   GreatPersonLevelRequiredDescV2: "Die Zivilisation der %{city} erfordert %{required} permanente Große-Leute-Level. Du hast derzeit %{current}",
   GreatPersonPromotionPromote: "Befördern",
   GreatPersonThisRunEffectiveLevel: "Du hast aktuell %{count} %{person} aus diesem Durchgang. Eine zusätzliche %{person} wird den Effekt um 1/%{effect} erhöhen.",
   GreatPersonWildCardBirth: "Geburt",
   GreatSphinx: "Große Sphinx",
   GreatSphinxDesc: "Alle Tier II Gebäude oder höher innerhalb von 2 Feldern erhalten +N Verbrauch und Multiplikator für Produktion. N = Zahl der benachbarten Gebäude des selben Types",
   GreatWall: "Große Mauer",
   GreatWallDesc: "Alle angrenzenden Gebäude erhalten +N Produktion, Arbeiterkapazität und Lagermultiplikator. N = Die Anzahl der Zeitalter zwischen dem aktuellen und dem Zeitalter, als das Gebäude freigeschaltet wurde. Wenn sie neben der Verbotenen Stadt gebaut wird, erhöht sich die Reichweite auf 2 Kacheln.",
   GreedyTransport: "Bau/Upgrade Gieriger Transport",
   GreedyTransportDescHTML: "Dadurch transportieren Gebäude weiterhin Ressourcen, auch wenn sie über genügend Ressourcen für das aktuelle Upgrade verfügen. Dadurch kann das Upgrade mehrerer Ebenen <b>schneller</b> erfolgen, am Ende werden jedoch <b>mehr Ressourcen als nötig</b> transportiert.",
   Greek: "griechisch",
   GrottaAzzurra: "Blaue Grotte",
   GrottaAzzurraDescV2: "Wenn sie entdeckt wird, erhöht sich bei allen Stufe 1 Gebäuden die Ausbaustufe um 5 und die Multiplikatoren für Produktion, Arbeiterkapazität und Speicherplatz um +1.",
   Gunpowder: "Schießpulver",
   GunpowderMill: "Schießpulvermühle",
   GuyFawkesNightV2: "Guy Fawkes Night: East India Company provides double the Production Multiplier to buildings adjacent to caravansaries. Tower Bridge generates great people 20% faster",
   HagiaSophia: "Hagia Sophia",
   HagiaSophiaDescV2: "+5 Zufriedenheit. Gebäude mit 0% Produktionskapazität sind von -1 Zufriedenheit ausgenommen. Während des Spielstarts zusätzliches Glück bereitstellen, um einen Produktionsstopp zu vermeiden",
   HallOfFame: "Hall of Fame",
   HallOfSupremeHarmony: "Halle der höchsten Harmonie",
   Hammurabi: "Hammurabi",
   HangingGarden: "Hängende Gärten",
   HangingGardenDesc: "Der Multiplikator für Bauarbeiter erhöht sich um 1. Bei benachbarten Aquädukten erhöht sich der Multiplikator für Produktion, Lagerung und Arbeitskräfte um 1",
   Happiness: "Zufriedenheit",
   HappinessFromBuilding: "Von Gebäuden (exkl. Wunder)",
   HappinessFromBuildingTypes: "aus gut versorgten Gebäudetypen",
   HappinessFromHighestTierBuilding: "Aus dem höchsten Arbeitsgebäude",
   HappinessFromUnlockedAge: "Durch freigeschaltete Zeitalter",
   HappinessFromUnlockedTech: "Von der freigeschalteten Technologie",
   HappinessFromWonders: "Von Wundern (inkl. Naturwunder)",
   HappinessUncapped: "Zufriedenheit (ohne Beschränkung)",
   HarryMarkowitz: "Harry Markowitz",
   HarunAlRashid: "Harun al-Raschid",
   Hatshepsut: "Hatshepsut",
   HatshepsutTemple: "Hatschepsut-Tempel",
   HatshepsutTempleDesc: "Deckt alle Gebiete mit Wasserquellen auf. Weizenfarmen erhalten +1 Produktionsmultiplikator für jede angrenzende Kachel mit Wasser.",
   Headquarter: "Hauptsitz",
   HedgeFund: "Hedgefonds",
   HelpMenu: "Hilfe",
   HenryFord: "Henry Ford",
   Herding: "Herdenhaltung",
   Herodotus: "Herodot",
   HighlightBuilding: "Highlight %{building}",
   HimejiCastle: "Burg Himeji",
   HimejiCastleDesc: "Bei allen Karavellen-, Galeonen- und Fregattenbauer werden die Multiplikatoren für Produktion, Arbeitskräfte und Lagervolumen um 1 erhöht",
   Hollywood: "Hollywood",
   HollywoodDesc: "+5 Zufriedenheit. +1 Zufriedenheit für alle gut versorgten Gebäude im Umkreis von 2 Kacheln, die Kultur produzieren oder konsumieren",
   HolyEmpire: "Heiliges Reich",
   Homer: "Homer",
   Honor4UpgradeHTML: "Verdoppelt den Effekt von <b>Zheng He</b> (Große Persönlichkeit)",
   HonorLevelX: "Ehre %{level}",
   Horse: "Pferd",
   HorsebackRiding: "Reiten",
   House: "Haus",
   Housing: "Wohnen",
   Hut: "Hütte",
   HydroDam: "Wasserstaudamm",
   Hydroelectricity: "Wasserkraft",
   HymanGRickover: "Hyman G. Rickover",
   IdeologyDescHTML: "Wähl zwischen <b>Liberalismus, Konservatismus, Sozialismus oder Kommunismus</b> als Ideologie deines Reiches. Du kannst die Ideologie <b>nicht mehr wechseln</b>, nachdem du sie gewählt hast. Du kannst innerhalb jeder Ideologie weitere Boosts freischalten.",
   IMPei: "I. M. Pei",
   IMPeiDesc: "+%{value} Arbeiterkapazitäts-Multiplikator",
   Imperialism: "Imperialismus",
   ImperialPalace: "Kaiserpalast",
   IndustrialAge: "Industrialisierung",
   InformationAge: "Informationszeitalter",
   InputResourceForCloning: "Eingaberessource für das Klonen",
   InternationalSpaceStation: "Internationale Raumstation",
   InternationalSpaceStationDesc: "Bei allen Gebäuden steigt der Multiplikator für Lagerkapazität um 5. Dieses Wunder kann ausgebaut werden. Mit jeder zusätzlichen Stufe steigt der Multiplikator für Lagerkapazität um 1.",
   Internet: "Internet",
   InternetServiceProvider: "Internet Service Provider",
   InverseSelection: "Umgekehrt",
   Iron: "Eisen",
   IronAge: "Eisenzeit",
   Ironclad: "Panzerschiff",
   IroncladBuilder: "Panzerschiff-Werft",
   IronForge: "Eisenschmiede",
   IronMiningCamp: "Eisenbergwerk",
   IronTech: "Eisenverarbeitung",
   IsaacNewton: "Isaac Newton",
   IsaacNewtonDescV2: "+%{value} Wissenschaft von allen Arbeitern, wenn mehr als 50% der Arbeiter beschäftigt sind und weniger als 50% der beschäftigten Arbeiter im Verkehrssektor arbeiten",
   IsambardKingdomBrunel: "Isambard Kingdom Brunel",
   IsidoreOfMiletus: "Isidore von Milet",
   IsidoreOfMiletusDesc: "+%{value} Multiplikator für die Baukapazität",
   Islam5UpgradeHTML: "Beim Freischalten erzeugt sie einmalig Wissenschaft, die den Kosten der teuersten <b>industriellen</b> Technologie entspricht.",
   IslamLevelX: "Islam %{level}",
   ItsukushimaShrine: "Itsukushima-Schrein",
   ItsukushimaShrineDescV2: "Wenn alle Technologien innerhalb eines Zeitalters freigeschaltet sind, wird einmalige Wissenschaft in Höhe der Kosten der billigsten Technologie im nächsten Zeitalter erzeugt.",
   JamesWatson: "James Watson",
   JamesWatsonDesc: "+%{value} Wissenschaft von beschäftigten Arbeitern",
   JamesWatt: "James Watt",
   Japanese: "Japanisch",
   JetPropulsion: "Düsenantrieb",
   JohannesGutenberg: "Johannes Gutenberg",
   JohannesKepler: "Johannes Kepler",
   JohnCarmack: "John Carmack",
   JohnDRockefeller: "John D. Rockefeller",
   JohnMcCarthy: "John McCarthy",
   JohnVonNeumann: "John von Neumann",
   JohnVonNeumannDesc: "+%{value} Wissenschaft von beschäftigten Arbeitern",
   JoinDiscord: "Discord beitreten",
   JosephPulitzer: "Joseph Pulitzer",
   Journalism: "Journalismus",
   JPMorgan: "J.P. Morgan",
   JRobertOppenheimer: "J. Robert Oppenheimer",
   JuliusCaesar: "Julius Caesar",
   Justinian: "Justinian",
   Kanagawa: "Kanagawa",
   KanagawaDesc: "Alle herausragenden Persönlichkeiten des aktuellen Zeitalters erhalten eine zusätzliche Stufe für diesen Lauf (außer Zenobia)",
   KarlMarx: "Karl Marx",
   Knight: "Ritter",
   KnightCamp: "Ritterlager",
   Koti: "Koti",
   KotiInStorage: "Koti In Storage",
   KotiProduction: "Koti Production",
   LandTrade: "Landhandel",
   Language: "Sprache",
   Lapland: "Lappland",
   LaplandDesc: "Wenn sie entdeckt wird, wird die gesamte Karte aufgedeckt. Alle Gebäude im Umkreis von 2 Feldern erhalten einen Produktionsmultiplikator von +5. Dieses Naturwunder kann nur im Dezember entdeckt werden",
   LargeHadronCollider: "Großer Hadron-Beschleuniger",
   LargeHadronColliderDescV2: "Alle herausragenden Persönlichkeiten des Informationszeitalters erhalten für diesen Lauf +2 Level. Dieses Wunder kann verbessert werden und jede zusätzliche Verbesserung verleiht allen herausragenden Persönlichkeiten des Informationszeitalters für diesen Lauf +1 Level.",
   Law: "Recht",
   Lens: "Linse",
   LensWorkshop: "Optiker",
   LeonardoDaVinci: "Leonardo da Vinci",
   Level: "Ausbaustufe",
   LevelX: "Ausbaustufe %{level}",
   Liberalism: "Liberalismus",
   LiberalismLevel3DescHTML: "Kostenloser Transport <b>von</b> und <b>zu</b> Lagern",
   LiberalismLevel5DescHTML: "<b>Verdoppelt</b> den Elektrifizierungseffekt",
   LiberalismLevelX: "Liberalismus Level %{level}",
   Library: "Bibliothek",
   LighthouseOfAlexandria: "Leuchtturm von Alexandria",
   LighthouseOfAlexandriaDesc: "Bei allen angrenzenden Gebäuden erhöht sich der Multiplikator für die Lagerkapazität um 5",
   LinusPauling: "Linus Pauling",
   LinusPaulingDesc: "+%{value} Wissenschaft von untätigen Arbeitern",
   Literature: "Literatur",
   LiveData: "Live Value",
   LocomotiveFactory: "Lokomotivfabrik",
   Logging: "Protokollierung",
   LoggingCamp: "Holzfäller",
   LouisSullivan: "Louis Sullivan",
   LouisSullivanDesc: "+%{value} Multiplikator für Bauarbeiter",
   Louvre: "Louvre",
   LouvreDesc: "For every 10 Extra Great People at Rebirth, one great person from all unlocked ages is born",
   Lumber: "Bretter",
   LumberMill: "Sägewerk",
   LunarNewYear: "Neujahrsfest: Die Große Mauer verleiht Gebäuden den doppelten Boost. Der Porzellanturm verleiht allen herausragenden Persönlichkeiten aus diesem Lauf +1 Stufe.",
   LuxorTemple: "Luxor-Tempel",
   LuxorTempleDescV2: "+1 Wissenschaft von beschäftigten Arbeitern. Wähle eine Reichsreligion, schalte mit jeder Auswahl mehr Boosts frei.",
   Machinery: "Maschinenwesen",
   Magazine: "Magazin",
   MagazinePublisher: "Zeitschriftenverlag",
   Maglev: "Transrapid",
   MaglevFactory: "Transrapid-Fabrik",
   MahatmaGandhi: "Mahatma Gandhi",
   ManageAgeWisdom: "Verwalten von Altersweisheit",
   ManagedImport: "Verwalteter Import",
   ManagedImportDescV2: "Dieses Gebäude importiert automatisch Ressourcen, die innerhalb des Bereichs von %{range} Kacheln produziert werden. Ressourcentransporte für dieses Gebäude können nicht manuell geändert werden. Die maximale Transportdistanz wird ignoriert.",
   ManageGreatPeople: "Herausragende Persönlichkeiten verwalten",
   ManagePermanentGreatPeople: "Herausragende Persönlichkeiten verwalten",
   ManageSave: "Manage Save",
   ManageWonders: "Wunder verwalten",
   Manhattan: "Manhattan",
   ManhattanProject: "Manhattan Projekt",
   ManhattanProjectDesc: "Bei allen Uranminen erhöht sich der Multiplikator für Produktion, Lagervolumen und Arbeiter um 2. Urananreicherung und Atomare Anlagen erhalten +1 Produktionsmultiplikator für jede angrenzende Uranmine, die auf einer Uranlagerstätte steht.",
   Marble: "Marmor",
   Marbleworks: "Marmorwerke",
   MarcoPolo: "Marco Polo",
   MarieCurie: "Marie Curie",
   MarinaBaySands: "Marina Bay Sands",
   MarinaBaySandsDesc: "Alle Gebäude erhalten +5 Arbeiter-Kapazitäts-Multiplikator. Dieses Wunder kann aufgerüstet werden und jede weitere Aufrüstung verleiht allen Gebäuden +1 Arbeitskapazitätsmultiplikator",
   Market: "Markt",
   MarketDesc: "Eine Ressource gegen eine andere austauschen, verfügbare Ressourcen werden stündlich aktualisiert",
   MarketRefreshMessage: "Der Handel in %{count} Märkten wurde aktualisiert",
   MarketSell: "Verkaufen",
   MarketSettings: "Markteinstellungen",
   MarketValueDesc: "%{value} des Durchschnittspreises",
   MarketYouGet: "Du erhältst",
   MarketYouPay: "Du bezahlst",
   MartinLuther: "Martin Luther",
   MaryamMirzakhani: "Maryam Mirzakhani",
   MaryamMirzakhaniDesc: "+%{value} Wissenschaft von untätigen Arbeitern",
   Masonry: "Mauerwerk",
   MatrioshkaBrain: "Matrjoschka-Gehirn",
   MatrioshkaBrainDescV2:
      "Erlaube die Zählung von Wissenschaft bei der Berechnung des Reichswertes (5 Wissenschaft = 1 Reichswert). +5 Wissenschaft pro beschäftigtem und untätigem Arbeiter. Dieses Wunder kann aufgerüstet werden und jede zusätzliche Aufrüstung bringt +1 Wissenschaft pro beschäftigtem und untätigem Arbeiter und +1 Produktionsmultiplikator für Gebäude, die Wissenschaft produzieren.",
   MausoleumAtHalicarnassus: "Mausoleum von Halikarnassos",
   MausoleumAtHalicarnassusDescV2: "Transports from or to buildings within 2 tile range do not cost workers",
   MaxExplorers: "Max Entdecker",
   MaxTransportDistance: "Max Transport Entfernung",
   Meat: "Fleisch",
   Metallurgy: "Metallurgie",
   Michelangelo: "Michelangelo",
   MiddleAge: "Mittelalter",
   MilitaryTactics: "Militärische Taktik",
   Milk: "Milch",
   Moai: "Moai",
   MoaiDesc: "Moai",
   MobileOverride: "Mobile Override",
   MogaoCaves: "Mogao-Höhlen",
   MogaoCavesDescV3: "+1 Zufriedenheit für jede 10% der beschäftigten Arbeiter. Alle angrenzenden Gebäude, die Glauben produzieren, sind von -1 Zufriedenheit ausgenommen.",
   MonetarySystem: "Währungssystem",
   MontSaintMichel: "Mont Saint-Michel",
   MontSaintMichelDesc: "Generate Culture from Idle Workers. Provide +1 Storage Multiplier to all buildings within 2-tile range. This wonder can be upgraded using the generated Culture and each level provides addtional +1 Storage Multiplier",
   Mosque: "Moschee",
   MotionPicture: "Spielfilm",
   MountArarat: "Mount Ararat",
   MountAraratDesc: "All buildings within 2 tile range get +X Production, Worker Capacity and Storage Multiplier. X = cubic root of Effective Great People Level",
   MountFuji: "Mount Fuji",
   MountFujiDescV2: "When Petra is built next to it, Petra gets +8h Warp storage. When the game is running, generate 20 warp every minute in Petra (not accelerated by Petra itself, not generating when the game is offline)",
   MountSinai: "Mount Sinai",
   MountSinaiDesc: "Bei der Entdeckung erhältst Du eine herausragende Persönlichkeit des aktuellen Zeitalters. Alle Gebäude, die Glauben generieren, bekommen einen Multiplier von +5 auf die Lagergröße",
   MountTai: "Mount Tai",
   MountTaiDesc: "Alle Gebäude, die Wissenschaft produzieren, erhalten +1 Produktionsmultiplikator. Verdopple den Effekt von Konfuzius (Große Persönlichkeit). Wenn er entdeckt wird, erzeugt er einmalig Wissenschaft in Höhe der Kosten der teuersten freigeschalteten Technologie.",
   MoveBuilding: "Gebäude verschieben",
   MoveBuildingFail: "Ausgewählte Kachel ist nicht gültig",
   MoveBuildingNoTeleport: "Du hast nicht genug Teleport",
   MoveBuildingSelectTile: "Wähle eine Kachel...",
   MoveBuildingSelectTileToastHTML: "Wähle <b>eine leere erkundete Kachel</b> auf der Karte als Ziel",
   Movie: "Film",
   MovieStudio: "Filmstudio",
   Museum: "Museum",
   Music: "Musik",
   MusiciansGuild: "Musikergilde",
   MutualAssuredDestruction: "Gegenseitig gesicherte Zerstörung",
   MutualFund: "Investmentfonds",
   Name: "Name",
   Nanotechnology: "Nanotechnologie",
   NapoleonBonaparte: "Napoleon Bonaparte",
   NaturalGas: "Erdgas",
   NaturalGasWell: "Erdgasbohrung",
   NaturalWonderName: "Naturwunder",
   NaturalWonders: "Naturwunder",
   Navigation: "Navigation",
   NebuchadnezzarII: "Nebukadnezar II.",
   Neuschwanstein: "Neuschwanstein",
   NeuschwansteinDesc: "Der Multiplikator für Bauarbeiter erhöht sich beim Bau von Wundern um 10.",
   Newspaper: "Zeitung",
   NextExplorersIn: "Nächste Entdecker In",
   NextMarketUpdateIn: "Nächste Aktualisierung der Angebote in",
   NiagaraFalls: "Niagarafälle",
   NiagaraFallsDescV2: "Alle Lagerhäuser, Märkte und Karawansereien erhalten +N Lagermultiplikator. N = Anzahl der freigeschalteten Zeitalter. Albert Einstein bietet +1 Produktionsmultiplikator für den Forschungsfonds (nicht beeinflusst von anderen Boosts wie Broadway)",
   NielsBohr: "Niels Bohr",
   NielsBohrDescV2: "+%{value} Wissenschaft von allen Arbeitnehmern, wenn mehr als 50% der Arbeiter beschäftigt sind und weniger als 50% der beschäftigten Arbeiter im Transportwesen arbeiten",
   NileRiver: "Nile River",
   NileRiverDesc: "Verdoppelt den Effekt von Hatshepsut. Alle Weizenfarmen erhalten +1 Multiplikator für Produktion und Speicher. Alle benachbarten Weizenfarmen erhalten +5 Multiplikator für Produktion und Speicher",
   NoPowerRequired: "Dieses Gebäude benötigt keinen Strom",
   NothingHere: "Noch keine Inhalte",
   NotProducingBuildings: "Gebäude, die nicht produzieren",
   NuclearFission: "Kernspaltung",
   NuclearFuelRod: "Nuklearer Brennstab",
   NuclearMissile: "Atomrakete",
   NuclearMissileSilo: "Atomraketensilo",
   NuclearPowerPlant: "Kernkraftwerk",
   NuclearReactor: "Kernreaktor",
   NuclearSubmarine: "Atom-U-Boot",
   NuclearSubmarineYard: "Nuklear-U-Boot-Werft",
   OdaNobunaga: "Oda Nobunaga",
   OfflineErrorMessage: "Du bist derzeit offline, für diesen Vorgang ist eine Internetverbindung erforderlich",
   OfflineProduction: "Offline-Produktion",
   OfflineProductionTime: "Offline Production Time",
   OfflineProductionTimeDescHTML: "For the <b>first %{time} offline time</b>, you can choose either offline production or time warp - you can set the split here. The <b>rest of the offline time</b> can only be converted to time warp",
   OfflineTime: "Offline-Zeit",
   Oil: "Rohöl",
   OilPress: "Olivenpresse",
   OilRefinery: "Ölraffinerie",
   OilWell: "Ölquelle",
   Ok: "OK",
   Oktoberfest: "Oktoberfest: verdoppelt die Wirkung der Zugspitze",
   Olive: "Olive",
   OlivePlantation: "Olivenhain",
   Olympics: "Olympische Spiele",
   OnlyAvailableWhenPlaying: "Nur beim Spielen verfügbar %{city}",
   OpenLogFolder: "Log-Ordner öffnen",
   OpenSaveBackupFolder: "Sicherungsordner öffnen",
   OpenSaveFolder: "Speicherordner öffnen",
   Opera: "Oper",
   OperationNotAllowedError: "Dieser Vorgang ist nicht zulässig",
   Opet: "Opet: Große Sphinx erhöht nicht mehr den Verbrauchsmultiplikator",
   OpticalFiber: "Lichtwellenleiter",
   OpticalFiberPlant: "Lichtwellenleiterfabrik",
   Optics: "Optik",
   OptionsMenu: "Optionen",
   OptionsUseModernUIV2: "Anti-Aliased-Schrift verwenden",
   OsakaCastle: "Osaka Castle",
   OsakaCastleDesc: "Versorgt alle Kacheln im Umkreis von 2 Kacheln mit Strom. Erlaubt die Elektrifizierung von wissenschaftlichen Gebäuden (einschließlich Klonlabor)",
   OtherPlatform: "Other Platform",
   Ottoman: "Ottoman",
   OttoVonBismarck: "Otto von Bismarck",
   OxfordUniversity: "Universität Oxford",
   OxfordUniversityDescV3: "+10% mehr Wissenschaft für Gebäude, die Wissenschaft produzieren. Beim Abschluss werden einmalig die Kosten der teuersten bislang freigeschalteten Technologie generiert",
   PabloPicasso: "Pablo Picasso",
   Pagoda: "Pagoda",
   PaintersGuild: "Malergilde",
   Painting: "Malerei",
   PalmJumeirah: "Palm Jumeirah",
   PalmJumeirahDesc: "+10 Arbeiterkapazität. Dieses Wunder kann aufgerüstet werden und jede weitere Aufrüstung bringt +2 Baumeisterkapazität.",
   Pamukkale: "Pamukkale",
   PamukkaleDesc: "When discovered, convert each one of the Permanent Great People Shards (except for Promotion and Wildcard) to the same Great Person From This Run",
   Panathenaea: "Panathenäen: Poseidon verleiht allen Gebäuden +1 Produktionsmultiplikator",
   Pantheon: "Pantheon",
   PantheonDescV2: "Alle Gebäude im Umkreis von 2 Kacheln erhalten +1 Arbeiterkapazität und Lagermultiplikator. Erzeuge Wissenschaft basierend auf der Glaubensproduktion aller Schreine",
   Paper: "Papier",
   PaperMaker: "Papiermacher",
   Parliament: "Parlament",
   Parthenon: "Parthenon",
   ParthenonDescV2: "2 herausragende Persönlichkeiten der Klassik werden geboren und Du kannst jeweils aus 4 auswählen. Musiker- und  Malergilden erhalten +1 Produktion, Arbeiter- und Speicherkapzität und sind von -1 Happiness befreit",
   Passcode: "Passcode",
   PasscodeToastHTML: "<b>%{code}</b> is your passcode and it's valid for 30 minutes",
   PatchNotes: "Patch Notes",
   Peace: "Frieden",
   Peacekeeper: "Friedensstifter",
   Penthouse: "Penthouse",
   PercentageOfProductionWorkers: "Prozentsatz der Beschäftigten in der Produktion",
   Performance: "Performance",
   PermanentGreatPeople: "Herausragende Persönlichkeiten in allen weiteren Durchgängen ",
   PermanentGreatPeopleAcquired: "Erlangte permanente herausragende Persönlichkeiten",
   PermanentGreatPeopleUpgradeUndo: "Permanentes Upgrade herausragender Persönlichkeit rückgängig machen: Dadurch wird das Upgrade-Level wieder in Scherben umgewandelt - du erhälst %{amount} Scherben",
   Persepolis: "Persepolis",
   PersepolisDesc: "In allen Kupferminen, Holzfällerlagern und Steinbrüchen erhöht sich der Multiplikator für Produktion, Arbeitskräfte und Lagerkapazität um 1.",
   PeterHiggs: "Peter Higgs",
   PeterHiggsDesc: "+%{value} Wissenschaft von beschäftigten Arbeitern",
   Petra: "Petra",
   PetraDesc: "Wenn du offline bist wird eine Zeitschleife erzeugt, durch die dein Imperium beschleunigen kannst.",
   PetraOfflineTimeReconciliation: "Du bekommst %{count} warp nach dem Abgleich der Server offline Zeit",
   Petrol: "Benzin",
   PhiloFarnsworth: "Philo Farnsworth",
   Philosophy: "Philosophie",
   Physics: "Physik",
   PierreDeCoubertin: "Pierre de Coubertin",
   Pizza: "Pizza",
   Pizzeria: "Pizzeria",
   PlanetaryRover: "Planetenrover",
   Plastics: "Kunststoffe",
   PlasticsFactory: "Fabrik für Kunststoffe",
   PlatformAndroid: "Android",
   PlatformiOS: "iOS",
   PlatformSteam: "Steam",
   PlatformSyncInstructionHTML: "If you want to sync your progress on this device to a new device, click <b>Sync To A New Device</b> and get a one-time passcode. On your new device, click <b>Connect To A Device</b> and type in the one-time passcode",
   Plato: "Plato",
   PlayerHandle: "Spieler-Handle",
   PlayerHandleOffline: "Du bist gerade offline",
   PlayerMapClaimThisTile: "Dieses Gebiet beanspruchen",
   PlayerMapClaimTileCondition2: "Du wurdest nicht von Anti-Cheat gebannt.",
   PlayerMapClaimTileCondition3: "Du hast die erforderliche Technologie freigeschaltet.",
   PlayerMapClaimTileCondition4: "Entweder hast Du kein Gebiet beansprucht oder zu lange fürs Ändern des Gebietes gebraucht.",
   PlayerMapClaimTileCooldownLeft: "Abklingzeit übrig",
   PlayerMapClaimTileNoLongerReserved: "Dieses Gebiet ist nicht mehr reserviert. Du kannst <b>%{name}</b> entfernen und es für dich beanspruchen.",
   PlayerMapEstablishedSince: "Gegründet am",
   PlayerMapLastSeenAt: "Zuletzt gesehen",
   PlayerMapMapTileBonus: "Trade Tile Bonus",
   PlayerMapMenu: "Handel",
   PlayerMapOccupyThisTile: "Occupy This Tile",
   PlayerMapOccupyTileCondition1: "This tile is adjacent to your home or occupied tiles",
   PlayerMapPageGoBackToCity: "Geh zurück in die Stadt",
   PlayerMapSetYourTariff: "Lege deinen Zollsatz fest",
   PlayerMapTariff: "Zoll",
   PlayerMapTariffApply: "Zollsatz anwenden",
   PlayerMapTariffDesc: "Je höher der Zoll, desto mehr verdienst du an jedem Handel, aber desto weniger Handel wird mit deinem Gebiet getrieben.",
   PlayerMapTileAvailableTilePoint: "Available Tile Point",
   PlayerMapTileFromOccupying: "From Owned/Occupied Tiles",
   PlayerMapTileFromOccupyingTooltipHTML: "An owned/occupied tile generates <b>%{point}</b> tile point per hour (up to %{max} days from the first claimed tile)",
   PlayerMapTileFromRank: "From Account Rank",
   PlayerMapTileTilePoint: "Tile Point",
   PlayerMapTileUsedTilePoint: "Used Tile Point",
   PlayerMapTileUsedTilePointTooltipHTML: "You need <b>1 tile point</b> to own/occupy a tile",
   PlayerMapTradesFrom: "Handel von %{name}",
   PlayerMapUnclaimedTile: "Nicht beanspruchtes Gebiet",
   PlayerMapYourTile: "Dein Gebiet",
   PlayerTrade: "Handel mit Spielern",
   PlayerTradeAddSuccess: "Der Handel wurde erfolgreich hinzugefügt.",
   PlayerTradeAddTradeCancel: "Abbrechen",
   PlayerTradeAmount: "Betrag",
   PlayerTradeCancelDescHTML: "Du erhälst <b>%{res}</b> zurück, nachdem du diesen Handel storniert hast: <b>%{percent}</b> für die Rückerstattung berechnet und <b>%{discard}</b> verworfen aufgrund von Speicherüberlauf<br><b>Sie sicher, dass Sie stornieren wollen?</b>",
   PlayerTradeCancelTrade: "Handel abbrechen",
   PlayerTradeClaim: "Anspruch",
   PlayerTradeClaimAll: "Alle beanspruchen",
   PlayerTradeClaimAllFailedMessageV2: "Ansprüche auf Handel gescheitert - ist der Speicher voll?",
   PlayerTradeClaimAllMessageV2: "Du hast beansprucht: <b>%{resources}</b>",
   PlayerTradeClaimAvailable: "Handel %{count} wurde ausgefüllt und steht für die Inanspruchnahme zur Verfügung",
   PlayerTradeClaimTileFirst: "Beanspruche ein Gebiet auf der Handelskarte.",
   PlayerTradeClaimTileFirstWarning: "Du kannst erst mit anderen Spielern handeln, nachdem du ein Gebiet auf der Handelskarte beansprucht hast.",
   PlayerTradeClearAll: "Alle Handelaufträge löschen",
   PlayerTradeClearFilter: "Clear Filters",
   PlayerTradeDisabledBeta: "Du kannst erst nach der Veröffentlichung der Betaversion einen Spielerhandel erstellen.",
   PlayerTradeFill: "erfüllen",
   PlayerTradeFill50: "Fill 50%",
   PlayerTradeFill95: "Fill 95%",
   PlayerTradeFillAmount: "Füllmenge",
   PlayerTradeFillAmountMaxV2: "Max befüllen",
   PlayerTradeFillBy: "Ausfüllen nach",
   PlayerTradeFillPercentage: "Prozentsatz der Befüllung",
   PlayerTradeFillSuccessV2: "<b>%{success}/%{total}</b> Handel eingereicht. Du hast <b>%{fillAmount} %{fillResource}</b> bezahlt und <b>%{receivedAmount} %{receivedResource}</b> erhalten",
   PlayerTradeFillTradeButton: "Handel einreichen",
   PlayerTradeFillTradeTitle: "Handel einreichen",
   PlayerTradeFilters: "Filter",
   PlayerTradeFiltersApply: "Anwenden",
   PlayerTradeFiltersClear: "Löschen",
   PlayerTradeFilterWhatIHave: "Filter By What I Have",
   PlayerTradeFrom: "Von",
   PlayerTradeIOffer: "Ich biete",
   PlayerTradeIWant: "Ich will",
   PlayerTradeMaxAll: "Maximum aller Füllungen",
   PlayerTradeMaxTradeAmountFilter: "Maximaler Betrag",
   PlayerTradeMaxTradeExceeded: "Du hast die maximale Anzahl aktiver Handelsaufträge für Ihren Kontorang überschritten.",
   PlayerTradeNewTrade: "Neuer Handel",
   PlayerTradeNoFillBecauseOfResources: "Aufgrund unzureichender Ressourcen wurde kein Geschäft abgeschlossen.",
   PlayerTradeNoValidRoute: "Es kann keine gültige Handelsroute zwischen Dir und %{name} gefunden werden",
   PlayerTradeOffer: "Angebot",
   PlayerTradePlaceTrade: "Handel platzieren",
   PlayerTradePlayerNameFilter: "Spielername",
   PlayerTradeResource: "Ressource",
   PlayerTradeStorageRequired: "Lager erforderlich",
   PlayerTradeTabImport: "Import",
   PlayerTradeTabPendingTrades: "Ausstehende Geschäfte",
   PlayerTradeTabTrades: "Geschäfte",
   PlayerTradeTariffTooltip: "aus einem Handelstarif erhoben werden",
   PlayerTradeWant: "Will",
   PlayerTradeYouGetGross: "Du bekommst (vor Zoll): %{res}",
   PlayerTradeYouGetNet: "Du bekommst (nach Zoll): %{res}",
   PlayerTradeYouPay: "Du bezahlst: %{res}",
   Poem: "Gedicht",
   PoetrySchool: "Poesieschule",
   Politics: "Politik",
   PolytheismLevelX: "Polytheism %{level}",
   PorcelainTower: "Porcelain Tower",
   PorcelainTowerDesc: "+5 Zufriedenheit. Wenn du sie gebaut hast, werden alle deine zusätzlichen großartigen Persönlichkeiten bei der Wiedergeburt für diesen Lauf verfügbar (sie werden nach denselben Regeln gewürfelt wie permanente großartige Persönlichkeiten)",
   PorcelainTowerMaxPickPerRoll: "Prefer Max Pick Per Roll",
   PorcelainTowerMaxPickPerRollDescHTML: "Bei der Auswahl der herausragenden Persönlichkeit nach der Fertigstellung des Porzellanturms, bevorzuge die maximale Auswahl pro Rolle für den verfügbaren Betrag",
   Poseidon: "Poseidon",
   PoseidonDescV2: "Alle angrenzenden Gebäude erhalten kostenlose Upgrades auf Ausbaustufe 25 sowie +N Produktions-, Arbeitskraft- und Lagermultiplikator. N = Stufe des Gebäudes.",
   PoultryFarm: "Geflügelproduktion",
   Power: "Leistung",
   PowerAvailable: "Strom verfügbar",
   PowerUsed: "Verbrauchte Leistung",
   PreciousMetal: "Edelmetall",
   Printing: "Drucken",
   PrintingHouse: "Druckerei",
   PrintingPress: "Druckpresse",
   PrivateOwnership: "Privateigentum",
   Produce: "produziert",
   ProduceResource: "Produziert: %{resource}",
   ProductionMultiplier: "Produktionsmultiplikator",
   ProductionPriority: "Priorität der Produktion",
   ProductionPriorityDescV4: "Priority determins the order that buildings transport and produce - a bigger number means a building transports and produces before other buildings",
   ProductionWorkers: "Produktionsarbeiter",
   Progress: "Fortschritt",
   ProgressTowardsNextGreatPerson: "Fortschritte auf dem Weg zur nächsten herausragenden Persönlichkeit bei der Wiedergeburt",
   ProgressTowardsTheNextGreatPerson: "Progress Towards the Next Great Person",
   PromotionGreatPersonDescV2: "Wenn er verbraucht ist, befördert er alle großen Menschen desselben Zeitalters in das nächste Zeitalter.",
   ProphetsMosque: "Prophet's Mosque",
   ProphetsMosqueDesc: "Verdoppelung der Wirkung von Harun al-Rashid. Erzeugung von Wissenschaft auf der Grundlage der Glaubensproduktion aller Moscheen",
   Province: "Provinz",
   ProvinceAegyptus: "Aegyptus",
   ProvinceAfrica: "Afrika",
   ProvinceAsia: "Asien",
   ProvinceBithynia: "Bithynien",
   ProvinceCantabri: "Kantabrien",
   ProvinceCappadocia: "Kappadokien",
   ProvinceCilicia: "Kilikien",
   ProvinceCommagene: "Kommagene",
   ProvinceCreta: "Kreta",
   ProvinceCyprus: "Zypern",
   ProvinceCyrene: "Cyrene",
   ProvinceGalatia: "Galatien",
   ProvinceGallia: "Gallia",
   ProvinceGalliaCisalpina: "Cisalpinisches Gallien",
   ProvinceGalliaTransalpina: "Gallia Transalpina",
   ProvinceHispania: "Hispania",
   ProvinceIllyricum: "Illyricum",
   ProvinceItalia: "Italia",
   ProvinceJudia: "Judia",
   ProvinceLycia: "Lykien",
   ProvinceMacedonia: "Mazedonien",
   ProvinceMauretania: "Mauretanien",
   ProvinceNumidia: "Numidien",
   ProvincePontus: "Pontus",
   ProvinceSardiniaAndCorsica: "Sardinien und Korsika",
   ProvinceSicillia: "Sizilien",
   ProvinceSophene: "Sophene",
   ProvinceSyria: "Syrien",
   PublishingHouse: "Verlag",
   PyramidOfGiza: "Pyramide von Gizeh",
   PyramidOfGizaDesc: "Bei allen Gebäuden, die Arbeitskräfte produzieren, erhöht sich der Multiplikator für die Produktion um 1.",
   QinShiHuang: "Qin Shi Huang",
   Radio: "Radio",
   RadioStation: "Radio Station",
   Railway: "Eisenbahn",
   RamessesII: "Ramses II.",
   RamessesIIDesc: "+%{value} Multiplikator für Baukapazität",
   RandomColorScheme: "Zufälliges Farb-Schema",
   RapidFire: "Schnellfeuer",
   ReadFullPatchNotes: "Patch-Notizen lesen",
   RebirthHistory: "Rebirth History",
   RebirthTime: "Rebirth Time",
   Reborn: "Wiedergeburt",
   RebornModalDescV3:
      "Du wirst ein neues Imperium gründen, aber alle deine herausragenden Persönlichkeiten <b>aus diesem Lauf</b> werden zu permanenten Splittern, mit denen du dein <b>permanentes herausragende Persönlichkeit</b>-Level verbessern kannst. Du erhälst außerdem zusätzliche Scherben herausragender Persönlichkeiten basierend auf deinem <b>Gesamtwert des Imperiums</b>",
   RebornOfflineWarning: "Du bist derzeit offline. Du kannst nur wiedergeboren werden, wenn du mit dem Server verbunden bist",
   RebornTradeWarning: "Du hast mindestens einen Handel, der aktiv ist oder beansprucht werden kann. Eine <b>Wiedergeburt löscht ihn.</b> - Du solltest in Erwägung ziehen, ihn zuerst zu stornieren oder einen Anspruch geltend zu machen.",
   RedistributeAmongSelected: "Auf ausgewählte verteilen",
   RedistributeAmongSelectedCap: "Grenze",
   RedistributeAmongSelectedImport: "Importieren",
   Refinery: "Raffinerie",
   Reichstag: "Reichstag",
   Religion: "Religion",
   ReligionBuddhism: "Buddhismus",
   ReligionChristianity: "Christentum",
   ReligionDescHTML: "Wähle zwischen <b>Christentum, Islam, Buddhismus oder Polytheismus</b> als deine Reichsreligion. Du kannst die Religion <b>nicht mehr wechseln</b>, nachdem du sie gewählt hast. Du kannst innerhalb jeder Religion weitere Verstärkungen freischalten.",
   ReligionIslam: "Islam",
   ReligionPolytheism: "Polytheismus",
   Renaissance: "Renaissance",
   RenaissanceAge: "Renaissance",
   ReneDescartes: "René Descartes",
   RequiredDeposit: "Erforderliches Vorkommen",
   RequiredWorkersTooltipV2: "Die für die Produktion erforderliche Anzahl von Arbeitskräften entspricht der Summe aller verbrauchten und produzierten Ressourcen nach Multiplikatoren (ohne dynamische Multiplikatoren)",
   RequirePower: "braucht Stromversorgung",
   RequirePowerDesc: "Dieses Gebäude muss auf einem Feld mit Stromversorgung gebaut werden und kann die Stromversorgung auf seine Nachbarfelder erweitern",
   Research: "Forschung",
   ResearchFund: "Forschungsfonds",
   ResearchLab: "Forschungslabor",
   ResearchMenu: "Forschung",
   ResourceAmount: "Betrag",
   ResourceBar: "Ressourcenleiste",
   ResourceBarExcludeStorageFullHTML: "Gebäude, die <b>voll gelagert</b> sind, von den nicht-produzierenden Gebäuden ausschließen",
   ResourceBarExcludeTurnedOffOrNoActiveTransportHTML: "Ausschluss von Gebäuden, die <b>abgeschaltet</b> sind, von den nicht produzierenden Gebäuden",
   ResourceBarShowUncappedHappiness: "Unbeschränkte Zufriedenheit anzeigen",
   ResourceCloneTooltip: "Der Produktionsmultiplikator gilt nur für die geklonte Ressource (d. h. die zusätzliche Kopie)",
   ResourceColor: "Farbe der Ressource",
   ResourceExportBelowCap: "Exportiere unterhalb der Kapazität",
   ResourceExportBelowCapTooltip: "Erlaube Gebäuden den Transport von hier selbst wenn die Menge unter der Kapazität liegt.",
   ResourceExportToSameType: "Exportiere zum gleichen Typ",
   ResourceExportToSameTypeTooltip: "Erlaube Gebäude des gleichen Typs Ware von hier zu transportieren.",
   ResourceFromBuilding: "%{resource} von %{building}",
   ResourceImport: "Ressourcentransport",
   ResourceImportCapacity: "Kapazität für Ressourcentransport",
   ResourceImportImportCapV2: "Max. Menge",
   ResourceImportImportCapV2Tooltip: "Dieses Gebäude beendet den Transport dieser Ressource, wenn die max. Menge erreicht ist",
   ResourceImportImportPerCycleV2: "Pro Zyklus",
   ResourceImportImportPerCycleV2ToolTip: "Die Menge dieser Ressource, die pro Zyklus transportiert wird",
   ResourceImportPartialWarningHTML: "Die Gesamtkapazität des Ressourcentransports hat die maximale Kapazität überschritten: <b>Jeder Ressourcentransport transportiert nur einen Teil pro Zyklus</b>",
   ResourceImportResource: "Ressource",
   ResourceImportSettings: "Ressourcen-Transport",
   ResourceImportStorage: "Lagerung",
   ResourceNeeded: "Zusätzlich %{resource} x%{amount} erforderlich",
   ResourceTransportPreference: "Transportpräferenz",
   RevealDeposit: "Lagerstätte entdecken",
   Revolution: "Revolution",
   RhineGorge: "Rheinschlucht",
   RhineGorgeDesc: "+2 Zufriedenheit für jedes Wunder im Umkreis von 2 Kacheln",
   RichardFeynman: "Richard Feynman",
   RichardFeynmanDesc: "+%{value} Wissenschaft von allen Arbeitnehmern, wenn mehr als 50% der Arbeiter beschäftigt sind und weniger als 50% der beschäftigten Arbeiter im Transportwesen arbeiten.",
   RichardJordanGatling: "Richard Jordan Gatling",
   Rifle: "Gewehr",
   RifleFactory: "Gewehrfabrik",
   Rifling: "Gewehr",
   Rijksmuseum: "Rijksmuseum",
   RijksmuseumDesc: "+5 Zufriedenheit. Bei allen Gebäuden, die Kultur verbrauchen oder produzieren, erhöht sich die Kapazität für Produktions, Lager und Arbeitskräfte um 1.",
   RoadAndWheel: "Straße & Rad",
   RobertNoyce: "Robert Noyce",
   Robocar: "Robocar",
   RobocarFactory: "Robocar Factory",
   Robotics: "Robotik",
   RockefellerCenterChristmasTree: "Rockefeller Center Christmas Tree",
   RockefellerCenterChristmasTreeDesc: "+3 Happiness for each unlocked age. This natural wonder can only be discovered in December",
   Rocket: "Rakete",
   RocketFactory: "Raketenfabrik",
   Rocketry: "Raketentechnik",
   Roman: "Roman",
   RomanForum: "Forum Romanum",
   RudolfDiesel: "Rudolf Diesel",
   Rurik: "Rurik",
   RurikDesc: "+%{value} Zufriedenheit",
   SagradaFamilia: "Sagrada Família",
   SagradaFamiliaDesc: "Alle Gebäude innerhalb von 2 Kacheln Reichweite erhalten +N Produktions-, Arbeitskapazitäts- und Lagermultiplikatoren. N = Max Stufen-Differenz zwischen den benachbarten Gebäuden (1 Kachel Reichweite) der Sagrada Familia.",
   SaintBasilsCathedral: "Basilius-Kathedrale",
   SaintBasilsCathedralDescV2: "Ermöglicht es, Gebäude zum Abbau von Ressourcen in Gebieten zu errichten, die an die Lagerstelle der Ressource angrenzen. Bei allen benachbarten Gebäuden auf Stufe I erhöht sich der Multiplikator für Produktion, Arbeitskräfte und Lagervolumen um 1.",
   Saladin: "Saladin",
   Samsuiluna: "Samsu-iluna",
   Sand: "Sand",
   Sandpit: "Sandgrube",
   SantaClausVillage: "Weihnachtsmanndorf",
   SantaClausVillageDesc:
      "Wenn es vollendet ist, wird eine herausragende Persönlichkeit des aktuellen Zeitalters geboren. Dieses Wunder kann aufgewertet werden und jede weitere Aufwertung bringt eine weitere große Person hervor. Bei der Auswahl einer herausragenden Persönlichkeit aus diesem Wunder stehen 4 Möglichkeiten zur Verfügung. Dieses Wunder kann nur im Dezember gebaut werden",
   SargonOfAkkad: "Sargon von Akkad",
   Satellite: "Satellit",
   SatelliteFactory: "Satellitenfabrik",
   SatoshiNakamoto: "Satoshi Nakamoto",
   Saturnalia: "Saturnalien: Alpen erhöhen den Verbrauchsmultiplikator nicht mehr.",
   SaveAndExit: "Speichern und Verlassen",
   School: "Schule",
   Science: "Wissenschaft",
   ScienceFromBusyWorkers: "Forschung durch beschäftigte Arbeitskräfte",
   ScienceFromIdleWorkers: "Forschung durch Arbeitskräfte ohne Beschäftigung",
   SciencePerBusyWorker: "Forschung pro beschäftigtem Arbeitskräfte",
   SciencePerIdleWorker: "Forschung pro Arbeitskräfte ohne Aufgabe",
   ScrollSensitivity: "Scrollempfindlichkeit",
   ScrollSensitivityDescHTML: "Empfindlichkeit beim Scrollen mit dem Mausrad einstellen. <b>Muss zwischen 0,01 und 100 liegen. Standard ist 1</b>",
   ScrollWheelAdjustLevelTooltip: "Mit dem Mausrad kannst du das Level anpassen wenn dein Mauszeiger über dem Symbol ist",
   SeaTradeCost: "Kosten für Seehandel",
   SeaTradeUpgrade: "Mit Spielern über das Meer handeln. Zoll je Meeresfeld: %{tariff}",
   SelectCivilization: "Zivilisation auswählen",
   SelectedAll: "Alle",
   SelectedCount: "%{count} ausgewählt",
   Semiconductor: "Halbleiter",
   SemiconductorFab: "Halbleiterfabrik",
   SendExplorer: "Entdecker senden",
   SergeiKorolev: "Sergei Korolev",
   SetAsDefault: "Als Standard festlegen",
   SetAsDefaultBuilding: "Als Standard für alle %{building} festlegen",
   Shamanism: "Schamanismus",
   Shelter: "Unterschlupf",
   Shortcut: "Tastaturbelegung",
   ShortcutBuildingPageSellBuildingV2: "Gebäude abreißen",
   ShortcutBuildingPageToggleBuilding: "Produktion wechseln",
   ShortcutBuildingPageToggleBuildingSetAllSimilar: "Produktion wechseln und auf alle anwenden.",
   ShortcutBuildingPageUpgrade1: "Upgrade Knopf 1 (+1)",
   ShortcutBuildingPageUpgrade2: "Upgrade Knopf 2 (+5)",
   ShortcutBuildingPageUpgrade3: "Upgrade Knopf 3 (+10)",
   ShortcutBuildingPageUpgrade4: "Upgrade Knopf 4 (+15)",
   ShortcutBuildingPageUpgrade5: "Upgrade Knopf 5 (+20)",
   ShortcutClear: "Klar",
   ShortcutConflict: "Deine Verknüpfung steht in Konflikt mit %{name}",
   ShortcutNone: "Keine",
   ShortcutPressShortcut: "Tastenkombination drücken...",
   ShortcutSave: "Speichern",
   ShortcutScopeBuildingPage: "Seite Erstellen",
   ShortcutScopeConstructionPage: "Konstruktions/Upgrade Seite",
   ShortcutScopeEmptyTilePage: "Leere Kachelseite",
   ShortcutScopePlayerMapPage: "Handelskarten-Seite",
   ShortcutScopeTechPage: "Tech-Seite",
   ShortcutScopeUnexploredPage: "Unerforschte Seite",
   ShortcutTechPageGoBackToCity: "Geh zurück in die Stadt",
   ShortcutTechPageUnlockTech: "Schalte ausgewählte Technologien frei",
   ShortcutUpgradePageCancelAllUpgrades: "Cancel All Upgrades",
   ShortcutUpgradePageCancelUpgrade: "Verbesserung abbrechen",
   ShortcutUpgradePageDecreaseLevel: "Upgrade-Level verringern",
   ShortcutUpgradePageEndConstruction: "Beenden der Konstruktion",
   ShortcutUpgradePageIncreaseLevel: "Upgrade-Level erhöhen",
   ShowTransportArrow: "Transportpfeile anzeigen",
   ShowTransportArrowDescHTML: "Durch Ausschalten werden die Transportpfeile ausgeblendet. Dies kann die Leistung auf Geräten mit geringer Leistung möglicherweise <i>leicht</i> verbessern. Die Leistungsverbesserung wird <b>nach einem Neustart des Spiels</b> wirksam.",
   ShowUnbuiltOnly: "Zeige nur Gebäude die noch nicht gebaut wurden",
   Shrine: "Schrein",
   SidePanelWidth: "Breite des seitlichen Menüfensters",
   SidePanelWidthDescHTML: "Breitenanpassung für das seitliche Menüfensters. <b>Ein Neustart des Spiels ist erforderlich</b>",
   SiegeRam: "Ramme",
   SiegeWorkshop: "Belagerungswerkstatt",
   Silicon: "Silizium",
   SiliconSmelter: "Siliziumschmelze",
   Skyscraper: "Wolkenkratzer",
   Socialism: "Sozialismus",
   SocialismLevel4DescHTMLV2: "Erzeugt einmal Wissenschaft in Höhe der Kosten der günstigsten <b>Zeitalter der Weltkriege</b> Technologie.",
   SocialismLevel5DescHTMLV2: "Erzeugt einmal Wissenschaft in Höhe der Kosten der günstigsten <b>Kalter Krieg</b> Technologie.",
   SocialismLevelX: "Sozialismus Level %{level}",
   SocialNetwork: "Soziales Netzwerk",
   Socrates: "Sokrates",
   SocratesDesc: "+%{value} Forschung durch beschäftigten Arbeitskräfte",
   Software: "Software",
   SoftwareCompany: "Softwareunternehmen",
   Sound: "Klang",
   SoundEffect: "Sound-Effekt",
   SourceGreatPerson: "Herausragende Persönlichkeit: %{person}",
   SourceGreatPersonPermanent: "Dauerhafte herausragende Persönlichkeit: %{person}",
   SourceIdeology: "Ideology: %{ideology}",
   SourceReligion: "Religion: %{religion}",
   SourceResearch: "Forschung",
   SourceTradition: "Tradition: %{tradition}",
   SpaceCenter: "Space Center",
   Spacecraft: "Raumschiff",
   SpacecraftFactory: "Raumschiff-Fabrik",
   SpaceNeedle: "Weltraumnadel",
   SpaceNeedleDesc: "+1 Zufriedenheit für jedes konstruierte Wunder.",
   SpaceProgram: "Raumfahrtprogramm",
   Sports: "Sport",
   Stable: "Stall",
   Stadium: "Stadion",
   StartFestival: "Let the Festival Begin!",
   Stateship: "Eigenstaatlichkeit",
   StatisticsBuildings: "Gebäude",
   StatisticsBuildingsSearchText: "Tippe einen Gebäudenamen für die Suche ein",
   StatisticsEmpire: "Imperium",
   StatisticsExploration: "Entdeckung",
   StatisticsOffice: "Statistisches Amt",
   StatisticsOfficeDesc: "Bietet Statistiken deines Reiches. Erzeugt Entdecker, die die Karte aufdecken",
   StatisticsResources: "Ressourcen",
   StatisticsResourcesDeficit: "Defizit",
   StatisticsResourcesDeficitDesc: "Produktion",
   StatisticsResourcesRunOut: "Auslaufen",
   StatisticsResourcesSearchText: "Gib einen Ressourcennamen für die Suche ein",
   StatisticsScience: "Forschung",
   StatisticsScienceFromBuildings: "Forschung durch Gebäude",
   StatisticsScienceFromWorkers: "Forschung durch Wunder",
   StatisticsScienceProduction: "Forschungsproduktion",
   StatisticsStalledTransportation: "Verzögerter Transport",
   StatisticsTotalTransportation: "Transport insgesamt",
   StatisticsTransportation: "Verkehr",
   StatisticsTransportationPercentage: "Prozentualer Anteil der Arbeitskräfte im Verkehrssektor",
   StatueOfLiberty: "Freiheitsstatue",
   StatueOfLibertyDesc: "Bei jedem angrenzenden Gebäuden wird der Multiplikator für Produktion, Lagervolumen und Arbeitskräfte um die Anzahl der angrenzenden Gebäude des gleichen Typs erhöht.",
   StatueOfZeus: "Statue des Zeus",
   StatueOfZeusDesc: "Erzeuge zufällige Vorkommen, die auf benachbarten leeren Gebieten aufgedeckt wurden. Bei allen angrenzenden Stufe I Gebäuden erhöht sich der Multiplikator für Produktion und Lagerkapazität um 5.",
   SteamAchievement: "Steam-Errungenschaft",
   SteamAchievementDetails: "Steam-Errungenschaft anzeigen",
   SteamEngine: "Dampfmaschine",
   Steamworks: "Dampfmaschine",
   Steel: "Stahl",
   SteelMill: "Stahlwerk",
   StephenHawking: "Stephen Hawking",
   Stock: "Aktien",
   StockExchange: "Börse",
   StockMarket: "Börse",
   StockpileDesc: "Dieses Gebäude transportiert in jedem Produktionszyklus %{capacity}x Input-Ressourcen, bis das Maximum erreicht ist.",
   StockpileMax: "Max. Vorrat",
   StockpileMaxDesc: "Wenn genug Ressourcen für %{cycle} Produktionszyklen geliefert wurde, wird der Transport eingestellt.",
   StockpileMaxUnlimited: "Unbegrenzt",
   StockpileMaxUnlimitedDesc: "Ressourcen werden so lange transportiert, bis bis das Lager voll ist",
   StockpileSettings: "Lager-Input-Kapazität",
   Stone: "Stein",
   StoneAge: "Steinzeit",
   Stonehenge: "Stonehenge",
   StonehengeDesc: "Bei allen Gebäuden, die Stein verbrauchen oder ihn produzieren erhöht sich der Multiplikator für die Produktion um 1.",
   StoneQuarry: "Steinbruch",
   StoneTool: "Steinwerkzeug",
   StoneTools: "Steinwerkzeuge",
   Storage: "Lager",
   StorageBaseCapacity: "Grundkapazität",
   StorageMultiplier: "Multiplikator des Lagers",
   StorageUsed: "Belegter Speicher",
   StPetersBasilica: "Petersdom",
   StPetersBasilicaDescV2: "Alle Kirchen erhalten +5 Speicher-Multiplikator. Erzeuge Wissenschaft basierend auf der Glaubensproduktion aller Kirchen.",
   Submarine: "U-Boot",
   SubmarineYard: "U-Boot-Werft",
   SuleimanI: "Suleiman I",
   SummerPalace: "Sommerpalast",
   SummerPalaceDesc:
      "Wenn sich auf einem angrenzenden Gebiet ein Gebäude befindet, das Schießpulver verbraucht oder produziert, erzeugt es nicht länger einen Verlust von Zufriedenheit. Bei allen Gebäude, die Schießpulver verbrauchen oder produzieren, erhöht sich die Kapazität für Produktions, Lager und Arbeitskräfte um 1.",
   Supercomputer: "Supercomputer",
   SupercomputerLab: "Supercomputer-Unternehmen",
   SupporterPackRequired: "Supporter Pack erforderlich",
   SupporterThankYou: "CivIdle wird dank der Großzügigkeit der folgenden Supporter-Pack-Besitzer über Wasser gehalten",
   SwissBank: "Swiss Bank",
   SwissBankDescV2: "Convert a chosen resource from adjacent warehouses to Koti (10 million in Sanskrit) - a tradeable resource that is worth 10M value. Each level of Swiss Bank add 1 Koti conversion per cycle, which is affected by Production Multiplier. Swiss Bank can store unlimited amount of Koti",
   Sword: "Schwert",
   SwordForge: "Schwertschmiede",
   SydneyOperaHouse: "Opernhaus von Sydney",
   SydneyOperaHouseDescV2: "Sydney Opera House",
   SyncToANewDevice: "Sync To A New Device",
   Synthetics: "Synthetik",
   TajMahal: "Taj Mahal",
   TajMahalDescV2: "Je eine herausragende Persönlichkeit der Klassik und des Mittelalters werden geboren. +5 Multiplikator für Kapazität, wenn Gebäude über Stufe 20 verbessert werden.",
   TangOfShang: "Tang von Shang",
   TangOfShangDesc: "+%{value} Forschung von unbeschäftigten Arbeitskräfte",
   Tank: "Panzer",
   TankFactory: "Panzerfabrik",
   TechAge: "Zeitalter",
   TechGlobalMultiplier: "Multiplikator für Technologien",
   TechHasBeenUnlocked: "%{tech} wurde freigeschaltet",
   TechProductionPriority: "Gebäudepriorität entsperren - ermöglicht das Festlegen der Produktionspriorität für jedes Gebäude",
   TechResourceTransportPreference: "Schalte die Transportpräferenz für Gebäude frei – ermöglicht die Einstellung, wie ein Gebäude die für seine Produktion benötigten Ressourcen transportiert",
   TechResourceTransportPreferenceAmount: "Menge",
   TechResourceTransportPreferenceAmountTooltip: "Dieses Gebäude bevorzugt den Transport von Ressourcen aus Gebäuden, die über größere Lagermengen verfügen",
   TechResourceTransportPreferenceDefault: "Standart",
   TechResourceTransportPreferenceDefaultTooltip: "Überschreibt nicht die Transportpräferenz für diese Ressource. Stattdessen wird die Transportpräferenz des Gebäudes verwendet",
   TechResourceTransportPreferenceDistance: "Entfernung",
   TechResourceTransportPreferenceDistanceTooltip: "Dieses Gebäude bevorzugt den Transport von Ressourcen aus Gebäuden, die näher beieinander liegen",
   TechResourceTransportPreferenceOverrideTooltip: "Für diese Ressource ist die Transportpräferenz außer Kraft gesetzt: %{mode}",
   TechResourceTransportPreferenceStorage: "Lager",
   TechResourceTransportPreferenceStorageTooltip: "Dieses Gebäude bevorzugt den Transport von Ressourcen aus Gebäuden, die über einen höheren Prozentsatz an genutztem Speicher verfügen",
   TechStockpileMode: "Vorratsmodus freischalten - ermöglicht die Anpassung der Vorräte für jedes Gebäude",
   Teleport: "Teleport",
   TeleportDescHTML: "Ein Teleport wird <b>alle %{time} Sekunden</b> erzeugt. Ein Teleport kann verwendet werden, um <b>ein Gebäude (Wunder ausgenommen)</b> einmal zu bewegen.",
   Television: "Fernsehen",
   TempleOfArtemis: "Tempel der Artemis",
   TempleOfArtemisDesc: "Alle Schwertschmieden und Waffenkammern werden um fünf Stufen verbessert und ihr Multiplikator für Produktion, Arbeitskräfte und Lagerkapazität wird um 1 erhöht.",
   TempleOfHeaven: "Tempel des Himmels",
   TempleOfHeavenDesc: "Bei allen Gebäude, die mindestens auf Stufe 10 sind, wird der Multiplikator für die Arbeitskräfte um 1 erhöht.",
   TempleOfPtah: "Tempel des Ptah",
   TerracottaArmy: "Terrakotta-Armee",
   TerracottaArmyDesc: "Bei allen Eisenbergbau-Lagern erhöht sich der Multiplikator für Produktion, Arbeitskräfte und Lagerkapazität um 1. Zusätzlich erhöht sich bei Eisenschmieden der Multiplikator für Produktion für jedes angrenzende Eisenbergbaulager um 1.",
   Thanksgiving: "Thanksgiving: Wall Street bietet den doppelten Boost für Gebäude und gilt für Mutual Fund, Hedge Fund und Bitcoin Miner. Forschungsfonds erhalten +5 Produktionsmultiplikator",
   Theater: "Theater",
   Theme: "Thema",
   ThemeColor: "Thema-Farbe",
   ThemeColorResearchBackground: "Wissenschaftlicher Hintergrund",
   ThemeColorReset: "Auf Standard zurücksetzen",
   ThemeColorResetBuildingColors: "Gebäudefarben zurücksetzen",
   ThemeColorResetResourceColors: "Ressourcenfarben zurücksetzen",
   ThemeInactiveBuildingAlpha: "Inaktives Gebäude Alpha",
   ThemePremiumTile: "This tile is only available for Supporter Pack owners",
   ThemeResearchHighlightColor: "Forschungs-Highlight-Farbe",
   ThemeResearchLockedColor: "Gesperrte Farbe erforschen",
   ThemeResearchUnlockedColor: "Forschung freigeschaltete Farbe",
   ThemeTransportIndicatorAlpha: "Transport-Indikator Alpha",
   Theocracy: "Theokratie",
   TheoreticalData: "Theoretical Data",
   ThePentagon: "The Pentagon",
   ThePentagonDesc: "After constructed, generate teleports that can be used to move buildings. All buildings within 2 tile range get +1 Production, Worker Capacity and Storage Multiplier",
   TheWhiteHouse: "The White House",
   ThomasEdison: "Thomas Edison",
   ThomasGresham: "Thomas Gresham",
   Tile: "Gebiet",
   TileBonusRefreshIn: "Tile bonus will refresh in <b>%{time}</b>",
   TimBernersLee: "Tim Berners-Lee",
   TimeWarp: "Zeitkrümmung",
   TimeWarpWarning: "BENUTZUNG AUF EIGENE GEFAHR",
   ToggleWonderEffect: "Wunder-Effekt umschalten",
   Tool: "Werkzeug",
   TopkapiPalace: "Topkapı Palace",
   TopkapiPalaceDesc: "All buildings within 2 tile range get +X Storage Multiplier. X = 50% of its Production Multiplier (excluding Dynamic)",
   TotalEmpireValue: "Gesamtwert des Imperiums",
   TotalEmpireValuePerCycle: "Gesamtwert des Imperiums pro Zyklus",
   TotalEmpireValuePerCyclePerGreatPeopleLevel: "Gesamtwert des Imperiums pro Zyklus und Stufe der großartigen Persönlichkeiten",
   TotalEmpireValuePerWallSecond: "Gesamtwert des Imperiums pro Sekunde und Wand",
   TotalEmpireValuePerWallSecondPerGreatPeopleLevel: "Gesamtwert des Imperiums pro Sekunde und Stufe der herausragenden Persönlichkeiten",
   TotalGameTimeThisRun: "Gesamte Spielzeit in diesem Lauf",
   TotalScienceRequired: "Wissenschaft insgesamt erforderlich",
   TotalStorage: "Gesamter Speicherplatz",
   TotalWallTimeThisRun: "Gesamte Wandzeit bei diesem Lauf",
   TotalWallTimeThisRunTooltip: "Die Wandzeit (auch bekannt als verstrichene Echtzeit) misst die tatsächliche Zeit, die für diesen Lauf benötigt wurde. Sie unterscheidet sich von der Spielzeit darin, dass Time Warp in Petra und Offline Produktion die Wandzeit nicht beeinflusst, aber die Spielzeit.",
   TotalWorkers: "Arbeitskräfte",
   TowerBridge: "Tower Bridge",
   TowerBridgeDesc: "After constructed, a great person from unlocked ages is born every 3600 cycles (1h game time)",
   TowerOfBabel: "Turm von Babel",
   TowerOfBabelDesc: "Bietet einen Produktionsmultiplikator von +2 für alle Gebäude, die mindestens ein funktionierendes Gebäude in der Nähe des Wunders haben.",
   TradeFillSound: "'Trade Filled' Sound",
   TradeValue: "Trade Value",
   TraditionCommerce: "Handel",
   TraditionCultivation: "Kultivierung",
   TraditionDescHTML: "Wähle zwischen <b>Anbau, Handel, Expansion und Ehre</b> als Tradition deines Reiches. Du kannst die <b>Tradition nicht mehr wechseln</b>, nachdem du sie gewählt hast. Du kannst innerhalb jeder Tradition weitere Boosts freischalten",
   TraditionExpansion: "Erweiterung",
   TraditionHonor: "Ehre",
   Train: "Zug",
   TranslationPercentage: "%{language} ist %{percentage} übersetzt. Hilf mit, diese Übersetzung auf GitHub zu verbessern",
   TranslatorCredit: "KaterKarlo, szoepf, MalcolmJacquard, xyzzycgn, donnerbaer",
   Translators: "Übersetzerinnen und Übersetzer",
   TransportAllocatedCapacityTooltip: "Bau-Kapazität, die dem Transport dieser Ressource zugewiesen ist",
   TransportationWorkers: "Transportarbeiter",
   TransportCapacity: "Transportkapazität",
   TransportCapacityMultiplier: "Multiplikator für die Transportkapazität",
   TransportManualControlTooltip: "Transportiere diese Ressource zum Bau/Upgrade",
   TransportPlanCache: "Transportplan-Cache",
   TransportPlanCacheDescHTML:
      "In jedem Zyklus berechnet jedes Gebäude den besten Transportplan basierend auf seinen Einstellungen – dieser Prozess erfordert viel CPU-Leistung. Wenn du diese Option aktivierst, wird versucht, das Ergebnis des Transportplans zwischenzuspeichern, wenn es noch gültig ist. Dadurch werden die CPU-Auslastung und der Framerateabfall reduziert. <b>Experimentelle Funktion</b>",
   TribuneUpgradeDescGreatPeopleWarning: "In deinem aktuellen Durchgang hast du herausragende Persönlichkeiten. Du solltest zuerst eine <b>Wiedergeburt</b> durchführen. Wenn du auf Quästor-Rang aufsteigst, wird dein aktueller Lauf zurückgesetzt.",
   TribuneUpgradeDescGreatPeopleWarningTitle: "Bitte erst Auferstehen",
   TribuneUpgradeDescV4:
      "Du kannst das gesamte Spiel als Tribun spielen, wenn du die <b>optionalen</b> Online-Funktionen nicht nutzen möchtest. Um uneingeschränkten Zugriff auf die Online-Funktionen zu erhalten, müsst du auf Quaestor upgraden. <b>Dies ist eine Anti-Bot-Maßnahme, damit das Spiel für alle kostenlos bleibt.</b> <b>Beim Upgrade auf Quaestor</b> kannst du jedoch folgende herausragende Persönlichkeiten übertragen: <ul><li>Bis zu Level <b>3</b> für Bronze-, Eisen- und Klassikzeitalter</li><li>Bis zu Level <b>2</b> für Mittelalter, Renaissance und Industriezeitalter</li><li>Bis zu Level <b>1</b> für Weltkriege, Kalten Krieg und Informationszeitalter</li></ul>Scherben großer Persönlichkeiten über dem Level und den Leveln der <b>Zeitalterweisheit</b> <b>können</b> nicht übertragen werden",
   TurnOffFullBuildings: "Turn Off All %{building} With Full Storage",
   TurnOnTimeWarpDesc: "Kostet %{speed} Warps für jede Sekunde und beschleunige dein Imperium, um mit %{speed}x Geschwindigkeit zu laufen.",
   Tutorial: "Tutorial",
   TutorialPlayerFlag: "Wähle deine Spielerflagge",
   TutorialPlayerHandle: "Wähle deinen Spieler-Namen",
   TV: "Fernseher",
   TVStation: "Fernsehstation",
   UnclaimedGreatPersonPermanent: "Du hast eine <b>herausragende Persönlichkeit</b> nicht beansprucht, <b>die dir in allen weiteren Duchgängen erhalten bleibt</b>. Klicke hier, um das nachzuholen.",
   UnclaimedGreatPersonThisRun: "Du hast in diesem Durchgang mindestens eine <b>herausragende Persönlichkeit nicht beansprucht</b>. klicke hier, um das nachzuholen.",
   UnexploredTile: "Unerforschtes Gebiet",
   UNGeneralAssemblyCurrent: "Aktuelle UN Generalversammlung #%{id}",
   UNGeneralAssemblyMultipliers: "<b>+%{count}</b> Produktion, Arbeiterkapazität und Speichermultiplikatoren für <b>%{buildings}</b>",
   UNGeneralAssemblyNext: "Kommende UN Generalversammlung #%{id}",
   UNGeneralAssemblyVoteEndIn: "Du kannst Deine Stimme jederzeit ändern, bevor die Abstimmung in <b>%{time}</b> endet",
   UniqueBuildings: "Einzigartige Gebäude",
   UniqueTechMultipliers: "Einzigartige Tech-Multiplikatoren",
   UnitedNations: "Vereinte Nationen",
   UnitedNationsDesc: "Alle Gebäude der Stufen IV, V und VI erhalten +1 Produktions-, Arbeitskraft- und Lagermultiplikator. Nimm an der UN-Generalversammlung teil und stimme jede Woche für einen zusätzlichen Schub ab.",
   University: "Universität",
   UnlockableResearch: "Freischaltbare Forschung",
   UnlockBuilding: "Gebäude freischalten",
   UnlockTechProgress: "Fortschritt",
   UnlockXHTML: "<b>%{name}</b> freischalten",
   Upgrade: "Verbesserung",
   UpgradeBuilding: "Gebäude verbessern",
   UpgradeBuildingNotProducingDescV2: "Dieses Gebäude wird verbessert - <b>Produktion ist angehalten bis zum Abschluss</b>",
   UpgradeTo: "Upgrade auf Stufe %{level}",
   Uranium: "Uran",
   UraniumEnrichmentPlant: "Urananreicherung",
   UraniumMine: "Uranmine",
   Urbanization: "Urbanisierung",
   UserAgent: "Benutzer-Agent",
   View: "Ansicht",
   ViewMenu: "Ansicht",
   ViewTechnology: "Technologie-Ansicht",
   Vineyard: "Weinberg",
   VirtualReality: "Virtuelle Realität",
   Voltaire: "Voltaire",
   WallOfBabylon: "Mauer von Babylon",
   WallOfBabylonDesc: "Alle Gebäude erhalten +N Speichermultiplikator. N = Anzahl der freigeschalteten Zeitalter / 2",
   WallStreet: "Wall Street",
   WallStreetDesc:
      "Alle Gebäude, die im Umkreis von 2 Kacheln Münzen, Banknoten, Anleihen, Aktien und Devisen produzieren, erhalten einen Produktionsmultiplikator von +N. N = Zufallswert zwischen 1 und 5, der für jedes Gebäude unterschiedlich ist und sich bei jeder Marktaktualisierung ändert. Verdopple den Effekt von John D. Rockefeller",
   WaltDisney: "Walt Disney",
   Warehouse: "Lager",
   WarehouseAutopilotSettings: "Einstellungen Autopilot",
   WarehouseAutopilotSettingsEnable: "Aktiviere Autopilot",
   WarehouseAutopilotSettingsRespectCapSetting: "Speicher < Cap erforderlich",
   WarehouseAutopilotSettingsRespectCapSettingTooltip: "Der Autopilot transportiert nur Güter, deren Lagervorrat unter dem Cap liegt",
   WarehouseDesc: "Transportiert bestimmte Ressourcen und stellt zusätzlichen Speicher zur Verfügung.",
   WarehouseExtension: "Freischaltung des Lagerhaus-Karawanserei-Erweiterungsmodus. Erlaubt, dass Lagerhäuser, die an Karawansereien grenzen, in den Spielerhandel einbezogen werden.",
   WarehouseSettingsAutopilotDesc: "Dieses Lager wird seine ungenutzte Kapazität nutzen, um Ressourcen aus Gebäuden zu transportieren, der Lagen vollständig gefüllt sind. Derzeitige Leerlaufkapazität",
   WarehouseUpgrade: "Schaltet den Autopilot-Modus des Lagers frei. Kostenloser Transport zwischen einem Lager und den angrenzenden Gebäuden",
   WarehouseUpgradeDesc: "Kostenloser Transport zwischen diesem Lager und den angrenzenden Kacheln",
   Warp: "Verkrümmen",
   WarpSpeed: "Warp Speed",
   Water: "Wasser",
   WellStockedTooltip: "Gut ausgestattete Gebäude sind Gebäude, die über genügend Ressourcen für ihre Produktion verfügen, d.h. Gebäude, die produzieren, deren Lager voll sind oder die aufgrund von Arbeitermangel nicht produzieren",
   WernherVonBraun: "Wernher von Braun",
   Westminster: "Westminster",
   Wheat: "Weizen",
   WheatFarm: "Weizenfarm",
   WildCardGreatPersonDescV2: "Wenn sie konsumiert werden, werden sie zu jeder großen Person desselben Alters",
   WilliamShakespeare: "William Shakespeare",
   Wine: "Wein",
   Winery: "Weingut",
   WishlistSpaceshipIdle: "Wishlist Spaceship Idle",
   Wonder: "Wunder",
   WonderBuilderCapacityDescHTML: "Das <b>Zeitalter</b> und die <b>Technologie</b> durch die ein Wunder freigeschaltet wird beeinflussen die <b>Anzahl Bauarbeiter</b>, die zum Errichten des Wunders benötigt werden.",
   WondersBuilt: "Gebaute Weltwunder",
   WondersUnlocked: "Freigeschaltete Weltwunder",
   WonderUpgradeLevel: "Wunderlevel",
   Wood: "Holz",
   Worker: "Arbeiter",
   WorkerCapacityMultiplier: "Multiplikator für die Arbeitskraftkapazität",
   WorkerHappinessPercentage: "Zufriedenheits-Multiplikator",
   WorkerMultiplier: "Arbeitskräfte",
   WorkerPercentagePerHappiness: "%{value}% Multiplikator für Zufriedenheit",
   Workers: "Arbeiter",
   WorkersAvailableAfterHappinessMultiplier: "Verfügbare Arbeitskräfte nach Erreichen des Multiplikators für Zufriedenheit",
   WorkersAvailableBeforeHappinessMultiplier: "Arbeitskräfte bis zum Erreichen des Multiplikators für Zufriedenheit",
   WorkersBusy: "Beschäftigte Arbeitskräfte",
   WorkerScienceProduction: "Forschung durch Arbeitskräfte",
   WorkersRequiredAfterMultiplier: "Erforderliche Arbeitskräfte",
   WorkersRequiredBeforeMultiplier: "Benötigte Arbeitskräfte",
   WorkersRequiredForProductionMultiplier: "Produktivität je Arbeiter",
   WorkersRequiredForTransportationMultiplier: "Beförderungskapazität je Arbeiter",
   WorkersRequiredInput: "Verkehr",
   WorkersRequiredOutput: "Produktion",
   WorldWarAge: "Zeitalter der Weltkriege",
   WorldWideWeb: "World Wide Web",
   WritersGuild: "Schriftstellergilde",
   Writing: "Schreiben",
   WuZetian: "Kaiserin Wu Zetian",
   WuZetianDesc: "+%{value} Multiplikator für die Transportkapazität",
   Xuanzang: "Xuanzang",
   YangtzeRiver: "Yangtze River",
   YangtzeRiverDesc: "Alle Gebäude, die Wasser verbrauchen, erhalten +1 Produktions-, Arbeitskraft- und Lagermultiplikator. Verdopple den Effekt von Zheng He (Große Person). Jede Stufe der Permanenten Kaiserin Wu Zetian (Große Person) verleiht allen Gebäuden +1 Speicher-Multiplikator.",
   YearOfTheSnake: "Year of the Snake",
   YearOfTheSnakeDesc:
      "After completed, when entering a new age, instead of getting one great person of each unlocked age, get the same amount of great people in the current age. All buildings within 2-tile range get +1 Production Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Production Multiplier to buildings within 2-tile range. This wonder can only be constructed during the lunar new year period (1.20 ~ 2.10)",
   YellowCraneTower: "Yellow Crane Tower",
   YellowCraneTowerDesc: "+1 Wahlmöglichkeit bei der Auswahl großer Leute. Alle Gebäude im Umkreis von 1 Kachel erhalten +1 Produktions-, Arbeitskraft- und Lagermultiplikator. Wenn es neben dem Jangtse-Fluss gebaut wird, erhöht sich die Reichweite auf 2 Kacheln.",
   YuriGagarin: "Yuri Gagarin",
   ZagrosMountains: "Zagros Mountains",
   ZagrosMountainsDesc: "Alle angrenzenden Gebäude, die weniger als 5 Produktionsmultiplikatoren haben, erhalten +2 Produktionsmultiplikatoren. Verdopple den Effekt von Nebukadnezar II (Große Persönlichkeit)",
   ZahaHadid: "Zaha Hadid",
   ZahaHadidDesc: "+%{value} Multiplikator der Bauarbeiterkapazität",
   Zenobia: "Zenobia",
   ZenobiaDesc: "+%{value}h Petra Warp Speicher",
   ZhengHe: "Zheng He",
   ZigguratOfUr: "Zikkurat von Ur",
   ZigguratOfUrDescV2:
      "Alle 10 Zufriendenheit (gedeckelt) bringt +1 Produktionsmultiplikator für alle Gebäude, die keine Arbeiter produzieren und in früheren Zeitaltern freigeschaltet wurden (max = Anzahl der freigeschalteten Zeitalter / 2). Wunder (inkl. Natürliche) bringen nicht mehr +1 Zufriendenheit. Der Effekt kann abgeschaltet werden",
   Zoroaster: "Zoroaster",
   Zugspitze: "Zugspitze",
   ZugspitzeDesc: "Für jedes freigeschaltete Zeitalter erhältst du einen Punkt, der dazu verwendet werden kann, jeder herausragende Person, die in diesem Lauf geboren wird, eine zusätzliche Stufe zu verleihen.",
};
