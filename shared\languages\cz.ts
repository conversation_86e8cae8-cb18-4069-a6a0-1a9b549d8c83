export const CZ = {
   About: "<PERSON> s<PERSON><PERSON><PERSON>ě CivIdle",
   AbuSimbel: "<PERSON> Simbe<PERSON>",
   AbuSimbelDesc: "Zdvojnásobuje efekt Ramesse II. Všechny přilehlé divy získávají +1 Spokojenost.",
   AccountActiveTrade: "Aktivní obchod",
   AccountChatBadge: "Chatovací odznak",
   AccountCustomColor: "Vlastní barva",
   AccountCustomColorDefault: "Výchozí barva",
   AccountGreatPeopleLevelRequirement: "Požadovaný level Osobnosti",
   AccountLevel: "Hodnost účtu",
   AccountLevelAedile: "Aedile",
   AccountLevelConsul: "Konzul",
   AccountLevelMod: "Moderator",
   AccountLevelPlayTime: "Aktivní doba hraní online > %{requiredTime} (Vaše doba hraní je %{actualTime}).",
   AccountLevelPraetor: "<PERSON>raetor",
   AccountLevelQuaestor: "<PERSON><PERSON><PERSON>",
   AccountLevelSupporterPack: "Vlastní balíček přízniv<PERSON>ů",
   AccountLevelTribune: "Tribuna",
   AccountLevelUpgradeConditionAnyHTML: "Pro upgrade účtu stačí splnit <b>jedno z následujících kritérií</b>:",
   AccountPlayTimeRequirement: "Požadovaná doba hraní",
   AccountRankUp: "Povýšit hodnost účtu",
   AccountRankUpDesc: "All your progress will be carried over to your new rank",
   AccountRankUpTip: "Congratulations, your account is eligible for a higher rank - click here to upgrade!",
   AccountSupporter: "Majitel balíčku podporovatelů",
   AccountTradePriceRange: "Rozsah ceny obchodu",
   AccountTradeTileReservationTime: "Rezervace obchodních dlaždic",
   AccountTradeTileReservationTimeDesc: "Toto je doba, po kterou pro vás bude rezervována obchodní dlaždice vašeho hráče od vaší poslední návštěvy online. Po uplynutí doby rezervace bude vaše dlaždice k dispozici ostatním hráčům.",
   AccountTradeValuePerMinute: "Hodnota obchodu za minutu",
   AccountTypeShowDetails: "Zobrazit podrobnosti o účtu",
   AccountUpgradeButton: "Povýšení na hodnost kvestora",
   AccountUpgradeConfirm: "Upgrade účtu",
   AccountUpgradeConfirmDescV2: "Při aktualizaci účtu <b>obnovení aktuálního běhu</b> a přenášet trvalé osobnosti v rámci povolených úrovní. Toto <b>nemůže</b> být zrušeno, jste si jisti, že chcete pokračovat?",
   Acknowledge: "Acknowledge",
   Acropolis: "Akropolis",
   ActorsGuild: "Cech herců",
   AdaLovelace: "Ada Lovelace",
   AdamSmith: "Adam Smith",
   AdjustBuildingCapacity: "Výrobní kapacita",
   AdvisorElectricityContent:
      "Power Plants provide two new systems to you. The first, 'Power' is indicated by the lightning bolt tiles adjacent to the power plant. Some buildings (starting with Radio in World Wars) have a 'requires power' indicator in their list of inputs. <b>This means they must be built on a lightning bolt tile to function</b>. Buildings that require power and have it, will also transmit power to the tiles adjacent to that building, so you can power them from each other as long as at least one is touching a power plant.<br><br>The other system 'electrification' can be applied to <b>any building anywhere</b> on the map as long as it doesn't produce science or workers. This uses up the power generated by the power plant to increase both the consumption and production of the building. More levels of electrification require larger and larger amounts of power. Electrifying buildings that also have 'requires power' is more efficient than electrifying the ones that don't.",
   AdvisorElectricityTitle: "Energie a Elektrifikace",
   AdvisorGreatPeopleContent:
      "Each time you enter a new age of technology, you will be able to select a Great Person from that age, and each previous age. These Great People give global bonuses that can increase production, science, happiness, and many other things.<br><br>These bonuses are permanent for the rest of the rebirth. When you rebirth, all of your Great People become permanent, and their bonus lasts forever.<br><br>Picking the same one in a later run will stack your permanent and in-run bonus, and when you rebirth with duplicates, the extras are stored and can be used to upgrade the permanent bonus. That is accessed in the <b>Manage Permanent Great People</b> menu in your Home Building.",
   AdvisorGreatPeopleTitle: "Osobnosti",
   AdvisorHappinessContent:
      "Spokojenost is the core mechanic in CivIdle that limits expansion. You gain spokojenost by unlocking new technology, advancing to new ages, building wonders, from Great People who provide it, and a few other ways you can discover as you learn. <b>Each new building costs 1 happiness</b>. For each point above/below 0 happiness, you get a 2% bonus or penalty to your total workers (Capping at -50 and +50 Happiness). You can see a detailed breakdown of your happiness in your <b>Home Building's Happiness section</b>.",
   AdvisorHappinessTitle: "Keep Your People Happy",
   AdvisorOkay: "Chápu, děkuju!",
   AdvisorScienceContent:
      "Your busy workers generate science, which allows you to unlock new technology and advance your civilization. You can access the research menu a number of ways. By clicking on the science meter, by accessing your unlockable technologies in your Home Building, or by using the 'View' menu. These will all bring you to the tech tree, showing you all the technologies, as well as how much science is required for each. If you have enough science to learn a new technology, simply click on it and press 'unlock' in the sidebar menu. <b>Each new tier and age of technology requires more and more science, but you will unlock new and better ways to gain science as well.</b>",
   AdvisorScienceTitle: "Vědecký objev!",
   AdvisorSkipAllTutorials: "Přeskočit všechny ůvodní návody",
   AdvisorStorageContent:
      "While buildings have a decent amount of storage, they can fill up especially if left idle for a long time. <b>When buildings are full, they can no longer produce</b>. This isn't always an issue, since you clearly have a large stockpile since the building is full. But keeping things producing is generally better.<br><br>One way to address full storage is via a warehouse. When you build a warehouse, you get a menu of every product you've discovered, and you can set the warehouse to pull any products in any amounts as long as the total for all products is within what the warehouse can pull based on its level and storage multiplier.<br><br>An easy way to set up a warehouse is to check off each product you want to import into the warehouse, and use the 'redistribute among selected' buttons to split your import rate and storage equally. If you want buildings to also be able to pull out of the warehouse, make sure to turn on the 'export below max amount' option as well.",
   AdvisorStorageTitle: "Skladování a sklady",
   AdvisorTraditionContent:
      "Some wonders (Chogha Zanbil, Luxor Temple, Big Ben) provide access to a new set of options, allowing you to customize the path of your rebirth. Each one allows you to choose from 1 of 4 options for your civilization's tradtion, religion and ideology respectively.<br><br>Once you choose one, that choice is locked in for that rebirth, though you can pick others in future rebirths. Once chosen, each one can also be upgraded a number of times by providing the necessary resources. The rewards in each tier are cumulative, so Tier 1 giving +1 production to X and Tier 2 giving +1 production to X means at Tier 2 you will have +2 production to X in total.",
   AdvisorTraditionTitle: "Choosing Paths and Upgradeable Wonders",
   AdvisorWonderContent:
      "Wonders are special buildings that provide global effects which can have a significant impact on your gameplay. In addition to their listed functions, all Wonders give +1 Spokojenost as well. You need to be careful though, as <b>Wonders require a LOT of materials, and have a higher than normal Builder Capacity as well</b>. This means that they can easily clear out your stockpiles of needed inputs, leaving your other buildings starving. <b>You can turn each input on and off freely</b>, allowing you to build it in stages while you stockpile enough materials to keep everything running.",
   AdvisorWonderTitle: "Div Světa",
   AdvisorWorkerContent:
      "Every time a building produces or transports goods, this requires workers. If you don't have enough workers available, some buildings will fail to run that cycle. The obvious fix for this is to increase your total available workers by building or upgrading structures that make workers (Hut/House/Apartment/Condo).<br><br><b>Be aware though, that buildings turn off while upgrading, and can't provide any of their resources, which includes workers, so you might want to only upgrade one housing building at a time.</b> A good goal for the early stages of the game is to keep aboput 70% of your workers busy. If more than 70% are busy, upgrade/build housing. If fewer than 70% are busy, expand production.",
   AdvisorWorkerTitle: "Správa pracovníků",
   Aeschylus: "Aischylos",
   Agamemnon: "Agamemnon",
   AgeWisdom: "Moudrost Věků",
   AgeWisdomDescHTML: "Each level of Age Wisdom provides <b>an equivalent level</b> of eligible Permanent Great People of that age - it can be upgraded with eligible Permanent Great People shards",
   AgeWisdomGreatPeopleShardsNeeded: "You need %{amount} more great people shards for the next Age Wisdom upgrade",
   AgeWisdomGreatPeopleShardsSatisfied: "You have enough great people shards for the next Age Wisdom upgrade",
   AgeWisdomNeedMoreGreatPeopleShards: "Need More Great People Shards",
   AgeWisdomNotEligible: "This Great Person is not eligible for Age Wisdom",
   AgeWisdomSource: "%{age} Moudrost: %{person}",
   AgeWisdomUpgradeWarningHTMLV3: "Age Wisdom <b>does not carry over</b> when upgrading from Tribune to Quaestor",
   AGreatPersonIsBorn: "Zrodila se Osobnost",
   AircraftCarrier: "Letadlová loď",
   AircraftCarrierYard: "Loděnice pro letadlové lodě",
   Airplane: "Letadlo",
   AirplaneFactory: "Továrna na letadla",
   Akitu: "Akitu: Ziggurat Of Ur and Euphrates River apply to buildings unlocked in the current age",
   AlanTuring: "Alan Turing",
   AlanTuringDesc: "+%{value} Věda od nečinných pracovníků",
   AlbertEinstein: "Albert Einstein",
   Alcohol: "Alkohol",
   AldersonDisk: "Alderson Disk",
   AldersonDiskDesc: "+25 Spokojenost. Tenhle div může být vylepšen a každé vylepšení přidá +5 Spokojenost",
   Alloy: "Slitina",
   Alps: "Alpy",
   AlpsDesc: "Každá 10. úroveň budovy získá +1 výrobní kapacitu (+1 násobitel spotřeby, +1 násobitel výroby).",
   Aluminum: "Hliník",
   AluminumSmelter: "Hliníková huť",
   AmeliaEarhart: "Amelia Earhart",
   American: "American",
   AndrewCarnegie: "Andrew Carnegie",
   AngkorWat: "Angkor Wat",
   AngkorWatDesc: "Všechny přilehlé budovy získají násobitel +1 k pracovní kapacitě. Poskytují 1000 dělníků",
   AntiCheatFailure: "Hodnost vašeho účtu byla omezena z důvodu <b>neprojití kontrolou proti podvodům</b>. Pokud se chcete odvolat, kontaktujte vývojáře",
   AoiMatsuri: "Aoi Matsuri: Hora Fuji generuje dvojnásob deformace času",
   Apartment: "Apartmán",
   Aphrodite: "Afrodita",
   AphroditeDescV2: "+1 Builder Capacity Multiplier for each level when upgrading buildings over Level 20. All unlocked Classical Age permanent great people get +1 level this run",
   ApolloProgram: "Apollo Program",
   ApolloProgramDesc: "Všechny raketové továrny získají +2 multiplikátor produkce, kapacity pracovníků a skladovacích prostor. Satelitní továrny, továrny na kosmické lodě a sila na jaderné střely dostanou za každou sousední raketovou továrnu výrobní násobek +1.",
   ApplyToAll: "Použít pro všechny",
   ApplyToAllBuilding: "Použít na všechny %{building}",
   ApplyToBuildingInTile: "Použít na všechny %{building} v rámci %{tile} políček",
   ApplyToBuildingsToastHTML: "Úspěšně aplikováno na <b>%{count} %{building}</b>",
   Aqueduct: "Akvadukt",
   ArcDeTriomphe: "Vítězný oblouk",
   ArcDeTriompheDescV2: "Every 1 happiness (capped) provides +1 builder capacity to all buildings",
   Archimedes: "Archimedes",
   Architecture: "Architektura",
   Aristophanes: "Aristophanes",
   AristophanesDesc: "+%{value} Spokojenost",
   Aristotle: "Aristotle",
   Arithmetic: "Aritmetika",
   Armor: "Zbroj",
   Armory: "Zbrojnice",
   ArtificialIntelligence: "Umělá inteligence",
   Artillery: "Artillery",
   ArtilleryFactory: "Dělostřelecká továrna",
   AshokaTheGreat: "Ashoka the Great",
   Ashurbanipal: "Aššurbanipal",
   Assembly: "Montáž",
   Astronomy: "Astronomie",
   AtomicBomb: "Atomová bomba",
   AtomicFacility: "Atomové zařízení",
   AtomicTheory: "Atomová teorie",
   Atomium: "Atomium",
   AtomiumDescV2: "Všechny budovy, které produkují Vědu ve vzdálenosti 2 políček dostávají +5 multiplikátor k produkci. Generuje vědu rovnající se vědě vyprodukované ve vzdálenosti 2 políček. Po dokončení vygeneruje jednorázové množství vědy odpovídající nákladům na nejnákladnější odemčenou technologii.",
   Autocracy: "Autokracie",
   Aviation: "Aviation",
   Babylonian: "Babylonian",
   BackToCity: "Zpět do města",
   BackupRecovery: "Záložní obnovení",
   Bakery: "Pekárna",
   Ballistics: "Balistika",
   Bank: "Banka",
   Banking: "Bankovnictví",
   BankingAdditionalUpgrade: "Všechny budovy, které jsou na úrovni 10 nebo vyšší, získají +1 multiplikátor ke skladování.",
   Banknote: "Bankovky",
   BaseCapacity: "Základní kapacita",
   BaseConsumption: "Základní spotřeba",
   BaseMultiplier: "Základní násobitel",
   BaseProduction: "Základní produkce",
   BastilleDay: "Bastille Day: Double the effect of Centre Pompidou and Arc de Triomphe. Double the Culture generation from Mont Saint-Michel",
   BatchModeTooltip: "%{count} jsou aktuálně vybrány budovy. Upgrade se použije na všechny vybrané budovy",
   BatchSelectAllSameType: "Všechny stejný typ",
   BatchSelectAnyType1Tile: "Jakýkoli typ v 1 políčku",
   BatchSelectAnyType2Tile: "Jakýkoli typ ve 2 políčkách",
   BatchSelectAnyType3Tile: "Jakýkoli typ ve 3 políčkách",
   BatchSelectSameType1Tile: "Stejný typ v 1 políčku",
   BatchSelectSameType2Tile: "Stejný typ ve 2 políčkách",
   BatchSelectSameType3Tile: "Stejný typ ve 3 políčkách",
   BatchSelectSameTypeSameLevel: "Stejný typ Stejný level",
   BatchSelectThisBuilding: "Tato budova",
   BatchStateSelectActive: "Active",
   BatchStateSelectAll: "All",
   BatchStateSelectTurnedFullStorage: "Full Storage",
   BatchStateSelectTurnedOff: "Turned Off",
   BatchUpgrade: "Množstevní aktualizace",
   Battleship: "Bitevní loď",
   BattleshipBuilder: "Stavitel bitevních lodí",
   BigBen: "Big Ben",
   BigBenDesc: "+2 Věda z zaneprázdněných pracovníků. Vyberte si ideologii Vaší říše, odemkněte více posílení s každou další volbou.",
   Biplane: "Dvouplošník",
   BiplaneFactory: "Továrna na dvouplošníky",
   Bitcoin: "Bitcoin",
   BitcoinMiner: "Bitcoin Miner",
   BlackForest: "Černý les",
   BlackForestDesc: "Odhalí všechna dřevěná políčka na mapě. Vytvoří dřevo na přilehlých políčcích. Všechny budovy, které spotřebovávají Dřevo nebo Prkna, získají multiplikátor produkce +5.",
   Blacksmith: "Kovář",
   Blockchain: "Blockchain",
   BlueMosque: "Blue Mosque",
   BlueMosqueDesc: "All wonders provide +1 Production, Worker Capacity and Storage Multiplier to adjacent buildings. When constructed next to Hagia Sophia, provide extra +1 Production, Worker Capacity and Storage Multiplier",
   BobHope: "Bob Hope",
   BobHopeDesc: "+%{value} Spokojenost",
   Bond: "Dluhopisy",
   BondMarket: "Trh s dluhopisy",
   Book: "Kniha",
   BoostCyclesLeft: "Boost Cycles Left",
   BoostDescription: "+%{value} %{multipliers} pro %{buildings}",
   Borobudur: "Borobudur",
   BorobudurDesc: "Borobudur",
   BranCastle: "Hrad Bran",
   BranCastleDesc: "Hrad Bran",
   BrandenburgGate: "Braniborská brána",
   BrandenburgGateDesc: "Všechny uhelné doly a ropné vrty získávají +1 násobek produkce, skladování a kapacity pracovníků. Ropné rafinerie získávají +1 násobek produkce, skladování a kapacity pracovníků za každou sousední destičku s ropou.",
   Bread: "Chléb",
   Brewery: "Pivovar",
   Brick: "Cihly",
   Brickworks: "Cihelna",
   BritishMuseum: "British Museum",
   BritishMuseumChooseWonder: "Choose a Wonder",
   BritishMuseumDesc: "After constructed, can transform into to a unique wonder from other civilizations",
   BritishMuseumTransform: "Transform",
   Broadway: "Broadway",
   BroadwayCurrentlySelected: "Currently selected",
   BroadwayDesc: "A great person of the current age and a great person of the previous age are born. Select a great person and double his/her effect",
   BronzeAge: "Doba bronzová",
   BronzeTech: "Bronz",
   BuddhismLevelX: "Buddhismus %{level}",
   Build: "Stavba",
   BuilderCapacity: "Budovatelská kapacita",
   BuildingColor: "Barva budovy",
   BuildingColorMatchBuilding: "Zkopírovat barvu z budovy",
   BuildingColorMatchBuildingTooltip: "Zkopíruje barvu výrobních budov do jejich zdrojů. Pokud je k dispozici více než jedna možnost, vybere náhodně",
   BuildingDefaults: "Výchozí nastavení budovy",
   BuildingDefaultsCount: "%{count} vlastnosti jsou přepsány ve výchozím nastavení budovy",
   BuildingDefaultsRemove: "Vymazání všech přepisů vlastností",
   BuildingEmpireValue: "Hodnota stavebního impéria / Hodnota impéria zdrojů",
   BuildingMultipliers: "Zvýšení",
   BuildingName: "Jméno budovy",
   BuildingNoMultiplier: "%{building} není ovlivněn žádnými multiplikátory (výroba, kapacita pracovníků, skladování atd.).",
   BuildingSearchText: "Zadejte název budovy nebo zdroje pro vyhledávání",
   BuildingTier: "Úroveň budovy",
   Cable: "Kabel",
   CableFactory: "Továrna na kabely",
   Calendar: "Kalendář",
   CambridgeUniversity: "Cambridge University",
   CambridgeUniversityDesc: "+1 Age Wisdom level for Renaissance and ages after",
   CambridgeUniversitySource: "Cambridge University (%{age})",
   Cancel: "Zrušit",
   CancelAllUpgradeDesc: "Cancel all %{building} upgrades",
   CancelUpgrade: "Zrušit aktualizaci",
   CancelUpgradeDesc: "Všechny prostředky, které již byly přeneseny, zůstanou v úložišti.",
   Cannon: "Dělo",
   CannonWorkshop: "Dělová dílna",
   CannotEarnPermanentGreatPeopleDesc: "Protože se jedná o zkušební běh, nelze získat permanentní osobnosti.",
   Capitalism: "Kapitalismus",
   Cappadocia: "Cappadocia",
   CappadociaDesc: "All buildings within 3 tile range get +1 Production, Worker Capacity and Storage Multiplier for every level above Level 30",
   Car: "Auto",
   Caravansary: "Karavanárna",
   CaravansaryDesc: "Obchodujte se surovinami s ostatními hráči a zajistěte si další skladovací prostory",
   Caravel: "Karavela",
   CaravelBuilder: "Stavitel karavel",
   CarFactory: "Továrna na automobily",
   CarlFriedrichGauss: "Carl Friedrich Gauss",
   CarlFriedrichGaussDesc: "+%{idle} Science from Idle Workers. +%{busy} Science from Busy Workers",
   CarlSagan: "Carl Sagan",
   Census: "Sčítání lidu",
   CentrePompidou: "Centre Pompidou",
   CentrePompidouDesc:
      "Once constructed, all buildings get +1 Production and +2 Storage Multiplier. The wonder will persist if the current run reaches Information Age and the next run is a different civilization. The wonder gets +1 level at rebirth for each run that reaches Information Age with a unique civilization. Each level provides +1 Production and +2 Storage Multiplier. The value of this wonder is excluded from total empire value and British Museum cannot transform into this wonder",
   CentrePompidouWarningHTML: "Centre Pompidou will disappear if you rebirth as <b>%{civ}</b>",
   CerneAbbasGiant: "Cerne Abbas Giant",
   CerneAbbasGiantDesc: "A great person of the current age is born when a wonder is constructed",
   ChangePlayerHandle: "Změnit",
   ChangePlayerHandleCancel: "Zrušit",
   ChangePlayerHandledDesc: "Vyberte si jedinečnou rukojeť hráče o délce 5 ~ 16 znaků. Vaše hráčská rukojeť může obsahovat pouze písmena a číslice",
   Chariot: "Vůz",
   ChariotWorkshop: "Vozová dílna",
   Charlemagne: "Karel Veliký",
   CharlesDarwin: "Charles Darwin",
   CharlesDarwinDesc: "+%{value} Věda od zaneprázdněných pracovníků",
   CharlesMartinHall: "Charles Martin Hall",
   CharlesParsons: "Charles Parsons",
   CharlieChaplin: "Charlie Chaplin",
   CharlieChaplinDesc: "+%{value} Spokojenost",
   Chat: "Chat",
   ChatChannel: "Chatovací kanál",
   ChatChannelLanguage: "Jazyk",
   ChatHideLatestMessage: "Skrýt obsah poslední zprávy",
   ChatNoMessage: "Žádné zprávy z chatu",
   ChatReconnect: "Odpojen, znovu se připojuje...",
   ChatSend: "Odeslat",
   CheckInAndExit: "Check In And Exit",
   CheckInCloudSave: "Check In Save",
   CheckOutCloudSave: "Check Out Save",
   Cheese: "Sýry",
   CheeseMaker: "Výrobce sýrů",
   Chemistry: "Chemie",
   ChesterWNimitz: "Chester W. Nimitz",
   ChichenItza: "Chichén Itzá",
   ChichenItzaDesc: "Všechny přilehlé budovy získají násobitel +1 k produkci, skladování a kapacitě dělníků.",
   Chinese: "Čínský národ",
   ChoghaZanbil: "Chogha Zanbil",
   ChoghaZanbilDescV2: "Choose an empire tradition, unlock more boost with each choice",
   ChooseGreatPersonChoicesLeft: "Máte %{count} zbývající možnosti",
   ChristianityLevelX: "Christianity %{level}",
   Church: "Církev",
   CircusMaximus: "Circus Maximus",
   CircusMaximusDescV2: "+5 Spokojenost. Všechny cechy hudebníků, spisovatelů a malířů získávají +1 násobek produkce a skladování.",
   CityState: "Město Stát",
   CityViewMap: "Město",
   CivGPT: "CivGPT",
   CivIdle: "CivIdle",
   CivIdleInfo: "Hrdě prezentováno společností Fish Pond Studio",
   Civilization: "Civilization",
   CivilService: "Státní služba",
   CivOasis: "CivOasis",
   CivTok: "CivTok",
   ClaimedGreatPeople: "Zvolené Osobnosti",
   ClaimedGreatPeopleTooltip: "Při znovuzrození máte %{total} Osobností, %{claimed} z nich je již zvolených.",
   ClassicalAge: "Antika",
   ClearAfterUpdate: "Smazat všechny obchody po aktualizaci trhu",
   ClearSelected: "Smazat vybrané",
   ClearSelection: "Smazat",
   ClearTransportPlanCache: "Clear Transport Plan Cache",
   Cleopatra: "Kleopatra",
   CloneFactory: "Fabrika na klony",
   CloneFactoryDesc: "Zklonuje jakékoliv suroviny",
   CloneFactoryInputDescHTML: "Clone Factory can only clone <b>%{res}</b> directly transported from <b>%{buildings}</b>",
   CloneLab: "Klonovací Laboratoř",
   CloneLabDesc: "Zkonvertuje jakoukoliv surovinu do Vědy",
   CloneLabScienceMultiplierHTML: "Production multipliers that <b>only apply to science production buildings</b> (e.g. production multipliers from Atomium) <b>do not apply</b> to Clone Lab",
   Cloth: "Látky",
   CloudComputing: "Cloudová výpočetní technika",
   CloudSaveRefresh: "Obnovit",
   CloudSaveReturnToGame: "Vrátit se do hry",
   CNTower: "CN Tower",
   CNTowerDesc: "Všechna filmová studia, rozhlasové stanice a televizní stanice jsou osvobozeny od daně z příjmu -1. Všechny budovy odemčené ve světových válkách a studené válce získávají +N násobek produkce, kapacity pracovníků a skladů. N = rozdíl mezi úrovní a stářím budovy.",
   Coal: "Uhlí",
   CoalMine: "Uhelný důl",
   CoalPowerPlant: "Uhelná elektrárna",
   Coin: "Mince",
   CoinMint: "Mincovna",
   ColdWarAge: "Studená válka",
   CologneCathedral: "Cologne Cathedral",
   CologneCathedralDesc:
      "When constructed, generate one-time science equivalent to the cost of the most expensive technology in the current age. All buildings that produce science (excluding Clone Lab) get +1 Production Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Production Multiplier to all buildings that produce science (excluding Clone Lab)",
   Colonialism: "Kolonialismus",
   Colosseum: "Koloseum",
   ColosseumDescV2: "Chariot Workshops are exempt from -1 spokojenost. Consumes 10 chariots and produce 10 spokojenost. Each unlocked age gives 2 extra spokojenost",
   ColossusOfRhodes: "Rhodský kolos",
   ColossusOfRhodesDesc: "Všechny přilehlé budovy, které neprodukují dělníky, získají +1 Štěstí",
   Combustion: "Spalování",
   Commerce4UpgradeHTMLV2: "When unlocked, all <b>adjacent banks</b> get free upgrade to <b>level 30</b>",
   CommerceLevelX: "Commerce %{level}",
   Communism: "Komunismus",
   CommunismLevel4DescHTML: "A great person of <b>Industrial Age</b> and a great person of <b>World Wars Age</b> are born",
   CommunismLevel5DescHTML: "A great person of <b>Cold War Age</b> is born. When entering a new age, get <b>2 additional</b> great people of that age",
   CommunismLevelX: "Level Komunismu %{level}",
   Computer: "Počítač",
   ComputerFactory: "Továrna na počítače",
   ComputerLab: "Laboratoř na počítače",
   Concrete: "Beton",
   ConcretePlant: "Cementárna",
   Condo: "Condo",
   ConfirmDestroyResourceContent: "Chystáte se zničit %{amount} %{resource}. To nelze vzít zpět",
   ConfirmNo: "Ne",
   ConfirmYes: "Ano",
   Confucius: "Konfucius",
   ConfuciusDescV2: "+%{value} Science from All Workers if more than 50% of workers are busy and less than 50% of busy workers work in transportation",
   ConnectToADevice: "Connect To A Device",
   Conservatism: "Konzervatismus",
   ConservatismLevelX: "Level Konzervatismu %{level}",
   Constitution: "Ústava",
   Construction: "Stavebnictví",
   ConstructionBuilderBaseCapacity: "Kapacita základny",
   ConstructionBuilderCapacity: "Kapacita stavitele",
   ConstructionBuilderMultiplier: "Multiplikátor kapacity",
   ConstructionBuilderMultiplierFull: "Multiplikátor kapacity stavitele",
   ConstructionCost: "Náklady na výstavbu: %{cost}",
   ConstructionDelivered: "Doručeno",
   ConstructionPriority: "Priorita výstavby",
   ConstructionProgress: "Pokrok",
   ConstructionResource: "Surovina",
   Consume: "Spotřebuje",
   ConsumeResource: "Spotřeba: %{resource}",
   ConsumptionMultiplier: "Multiplikátor spotřeby",
   ContentInDevelopment: "Obsah ve vývoji",
   ContentInDevelopmentDesc: "Tento herní obsah je stále ve vývoji a bude k dispozici v budoucí aktualizaci hry, zůstaňte naladěni!",
   Copper: "Měď",
   CopperMiningCamp: "Lom na těžbu mědi",
   CosimoDeMedici: "Cosimo Medicejský",
   Cotton: "Bavlna",
   CottonMill: "Bavlnářský mlýn",
   CottonPlantation: "Bavlněná plantáž",
   Counting: "Počítání",
   Courthouse: "Soudní budova",
   CristoRedentor: "Cristo Redentor",
   CristoRedentorDesc: "Cristo Redentor",
   CrossPlatformAccount: "Platform Account",
   CrossPlatformConnect: "Připojit",
   CrossPlatformSave: "Cross Platform Save",
   CrossPlatformSaveLastCheckIn: "Last Check In",
   CrossPlatformSaveStatus: "Aktuální Status",
   CrossPlatformSaveStatusCheckedIn: "Checked In",
   CrossPlatformSaveStatusCheckedOut: "Checked Out on %{platform}",
   CrossPlatformSaveStatusCheckedOutTooltip: "Your cross platform save has been checked out on another platform, you have to check in on that platform before you can check out on this platform",
   Cultivation4UpgradeHTML: "A great person of <b>Renaissance Age</b> is born",
   CultivationLevelX: "Cultivation %{level}",
   Culture: "Kultura",
   Culus: "Cülus: Double the effect of Cappadocia. Mount Ararat's effect becomes based on square root of Effective Great People Level, instead of cubic root",
   CurrentLanguage: "Čeština",
   CurrentPlatform: "Current Platform",
   CursorBigOldFashioned: "3D (Big)",
   CursorOldFashioned: "3D",
   CursorStyle: "Styl kurzoru",
   CursorStyleDescHTML: "Změnit styl kurzoru. <b>Vyžaduje restart hry, aby se změny projevily </b>",
   CursorSystem: "Systém",
   Cycle: "Cycle",
   CyrusII: "Kýros II.",
   DairyFarm: "Farma na mléko",
   DefaultBuildingLevel: "Výchozí úroveň budování",
   DefaultConstructionPriority: "Výchozí priorita výstavby",
   DefaultProductionPriority: "Výchozí priorita výroby",
   DefaultStockpileMax: "Výchozí maximální zásoba",
   DefaultStockpileSettings: "Výchozí vstupní kapacita zásoby",
   DeficitResources: "Deficitní suroviny",
   Democracy: "Demokracie",
   DemolishAllBuilding: "Zbourat všechny %{building} v %{tile} políček",
   DemolishAllBuildingConfirmContent: "Jste si jistí se zbouráním %{count} %{name}?",
   DemolishAllBuildingConfirmTitle: "Zbourat %{count} Budovu(-vy)?",
   DemolishBuilding: "Zbourat budovu",
   DennisRitchie: "Dennis Ritchie",
   Deposit: "Ložisko",
   DepositTileCountDesc: "%{count} políčko %{deposit} lze nalézt v %{city}",
   Dido: "Dido",
   Diplomacy: "Diplomacie",
   DistanceInfinity: "Neomezené",
   DistanceInTiles: "Vzdálenost (v políčkách)",
   DolmabahcePalace: "Dolmabahçe Palace",
   Drilling: "Vrtání",
   DukeOfZhou: "Vévoda z Čou",
   DuneOfPilat: "Dune of Pilat",
   DuneOfPilatDesc: "In each age, double the age wisdom for the previous age",
   DynamicMultiplierTooltip: "Tento multiplikátor je dynamický - nebude ovlivňovat pracovníky a sklady",
   Dynamite: "Dynamit",
   DynamiteWorkshop: "Dílna na dynamit",
   DysonSphere: "Dysonova sféra",
   DysonSphereDesc: "All buildings get +5 Production Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Production Multiplier to all buildings",
   EasterBunny: "Easter Bunny",
   EasterBunnyDesc: "Once constructed, 10% of the extra Great People at Rebirth from this run will carry forward to the next run and are born after building the Easter Bunny in the new run. This wonder can only be constructed during April",
   EastIndiaCompany: "East India Company",
   EastIndiaCompanyDescV2:
      "This wonder accumulates the total value of your completed player trade transactions. For every 2,000 trade value, all buildings adjacent to caravansaries get a +0.5 Production Multiplier for 1 cycle. This wonder can be upgraded and each upgrade provides an additional +0.5 Production Multiplier. A trade transaction counts when you either fulfill another player's trade request or when your own trade request is fulfilled. Multiple boosts stack by extending the duration",
   Education: "Vzdělání",
   EffectiveGreatPeopleLevel: "Effective Great People Level",
   EffectiveGreatPeopleLevelDesc: "Effective great people level is the sum of all permanent great people level and age wisdom level. It measures the effect boost provided by great people and age wisdom",
   Egyptian: "Egypťan",
   EiffelTower: "Eiffelova věž",
   EiffelTowerDesc: "Všechny přilehlé ocelárny získají +N Výrobu, Skladování a Násobek pracovníků. N = počet sousedících oceláren",
   Elbphilharmonie: "Elbphilharmonie",
   ElbphilharmonieDesc: "All buildings within 3 tile range get +1 Production Multiplier for each adjacent working building that has different tier",
   Electricity: "Elektřina",
   Electrification: "Elektrifikace",
   ElectrificationPowerRequired: "Požadovaná energie",
   ElectrificationStatusActive: "Aktivní",
   ElectrificationStatusDesc: "Both buildings that require power and buildings that do not require power can be electrified. However, buildings that require power provides higher electrification efficiency",
   ElectrificationStatusNoPowerV2: "Nedostatek energie",
   ElectrificationStatusNotActive: "Není aktivní",
   ElectrificationStatusV2: "Status Elektrifikace",
   ElectrificationUpgrade: "Odemkni elektrifikaci. Umožní budovám spotřebovávat energii pro zvýšení produkce",
   Electrolysis: "Elektrolýza",
   ElvisPresley: "Elvis Presley",
   ElyseePalace: "Élysée Palace",
   EmailDeveloper: "Zaslat e-mail vývojáři",
   Embassy: "Ambasáda",
   EmperorWuOfHan: "Císař Wu z Han",
   EmpireValue: "Hodnota impéria",
   EmpireValueByHour: "Hodinová hodnota impéria",
   EmpireValueFromBuilding: "Hodnota impéria z budov",
   EmpireValueFromBuildingsStat: "Z budov",
   EmpireValueFromResources: "Ze zdrojů",
   EmpireValueFromResourcesStat: "Ze zdrojů",
   EmpireValueIncrease: "Zvýšení hodnoty impéria",
   EmptyTilePageBuildLastBuilding: "Postavit poslední budovu",
   EndConstruction: "Konec výstavby",
   EndConstructionDescHTML: "Při ukončení stavby se všechny již použité zdroje <b>nevrátí</b>.",
   Engine: "Motor",
   Engineering: "Strojírenství",
   English: "English",
   Enlightenment: "Osvícenství",
   Enrichment: "Obohacení",
   EnricoFermi: "Enrico Fermi",
   EstimatedTimeLeft: "Odhadovaný zbývající čas",
   EuphratesRiver: "Řeka Eufrat",
   EuphratesRiverDesc:
      "Every 10% of busy workers that in production (not transporting) provides +1 Production Multiplier to all buildings that do not produce workers (max = number of unlocked ages / 2). When the Hanging Garden is built next to it, the Hanging Garden gets +1 effect for each age after the Hanging Garden is unlocked. When discovered, spawn water on all adjacent tiles that do not have deposits",
   ExpansionLevelX: "Expanze %{level}",
   Exploration: "Průzkum",
   Explorer: "Průzkumník",
   ExplorerRangeUpgradeDesc: "Zvýšení the explorer's range to %{range}",
   ExploreThisTile: "Odeslat průzkumníka",
   ExploreThisTileHTML: "Průzkumník prozkoumá <b>tuto destičku a její sousední destičky</b>. Průzkumníci jsou generováni v %{name}. Zbývá vám %{count} průzkumníků.",
   ExtraGreatPeople: "%{count} Extra Osobnosti",
   ExtraGreatPeopleAtReborn: "Další Osobnosti při znovuzrození",
   ExtraTileInfoType: "Extra Info Políčka",
   ExtraTileInfoTypeDesc: "Vybrat informaci zobrazovanou pod každým políčkem",
   ExtraTileInfoTypeEmpireValue: "Hodnota impéria",
   ExtraTileInfoTypeNone: "Žádná",
   ExtraTileInfoTypeStoragePercentage: "Procento skladu",
   Faith: "Víra",
   Farming: "Zemědělství",
   FavoriteBuildingAdd: "Přidat do oblíbených",
   FavoriteBuildingEmptyToast: "Nemáte žádné oblíbené budovy",
   FavoriteBuildingRemove: "Odebrat z oblíbených",
   FeatureRequireQuaestorOrAbove: "This feature requires Quaestor rank or above",
   Festival: "Festival",
   FestivalCycle: "Cyklus Festivalu",
   FestivalTechTooltipV2: "Positive Spokojenost (max. 50) is converted into festival points. For every %{point} festival points, your empire enters a festival cycle, granting a significant map-specific boost. The festival on this map is %{desc}",
   FestivalTechV2: "Unlock festival - positive Spokojenost (max. 50) is converted into festival points. For every %{point} festival points, your empire enters a festival cycle, granting a significant map-specific boost",
   Feudalism: "Feudalismus",
   Fibonacci: "Fibonacci",
   FibonacciDescV2: "+%{idle} Věda od nečinných pracovníků. +%{busy} Věda od zaneprázdněných pracovníků. Náklady na trvalé vylepšení Fibonacciho se řídí Fibonacciho posloupností.",
   FighterJet: "Stíhací letoun",
   FighterJetPlant: "Závod na výrobu stíhaček",
   FilterByAge: "Filtrovat Věkem",
   FinancialArbitrage: "Finanční arbitráž",
   FinancialLeverage: "Finanční páka",
   Fire: "Oheň",
   Firearm: "Střelné zbraně",
   FirstTimeGuideNext: "Další",
   FirstTimeTutorialWelcome: "Vítejte do CivIdle",
   FirstTimeTutorialWelcome1HTML:
      "Vítejte ve hře CivIdle. V této hře budete řídit své vlastní impérium: <b>spravovat výrobu, odemykat technologie, obchodovat s prostředky s ostatními hráči, vytvářet významné osobnosti a budovat světové divy.</b>.<br><br>Drag your mouse to move around. Use the scroll wheel to zoom in or out. Click an empty tile to build new buildings, click a building to inspect it.<br><br>Certain buildings like Stone Quarry and Logging Camp need to be built on top of the resource tile. I recommend placing a Hut, which provides worker, next to the fog - the building will take some time to build. After the completion, it will reveal the fog nearby.",
   FirstTimeTutorialWelcome2HTML:
      "Buildings can be upgraded - it costs resources and takes time. When a buildings is being upgraded, <b>it will no longer produce</b>. This includes buildings that provide workers, <b>so never upgrade all your buildings at the same time!</b><br><br>As your empire grows, you will get more science and unlock new technologies. I will tell you more about it when we get there but you can go to View -> Research to take a quick look<br><br>",
   FirstTimeTutorialWelcome3HTML: "Now you know all the basics of the game, you can start building your empire. But before I let you go, you should <b>choose yourself a player handle</b> and say hi in the in-game chat. We have an amazingly helpful community: if you get lost, don't be afraid to ask!",
   Fish: "Ryby",
   FishPond: "Rybník s rybami",
   FlorenceNightingale: "Florence Nightingalová",
   FlorenceNightingaleDesc: "+%{value} Spokojenost",
   Flour: "Mouka",
   FlourMill: "Mlýn na mouku",
   FontSizeScale: "Měřítko velikosti písma",
   FontSizeScaleDescHTML: "Change the font size scale of the game's UI. <b>Setting the scale greater than 1x might break some UI layouts</b>",
   ForbiddenCity: "Zakázané město",
   ForbiddenCityDesc: "Všichni výrobci papíru, cechy spisovatelů a tiskárny získají +1 násobek produkce, násobek kapacity pracovníků a násobek skladování.",
   Forex: "Devize",
   ForexMarket: "Devizový trh",
   FrankLloydWright: "Frank Lloyd Wright",
   FrankLloydWrightDesc: "+%{value} Builder Capacity Multiplier",
   FrankWhittle: "Frank Whittle",
   FreeThisWeek: "Zdarma tento týden",
   FreeThisWeekDescHTMLV2: "<b>Every week</b>, one of the premium civilizations is free to play. This week's free civilization is <b>%{city}</b>",
   French: "French",
   Frigate: "Fregata",
   FrigateBuilder: "Stavitel fregat",
   Furniture: "Nábytek",
   FurnitureWorkshop: "Nábytkářská dílna",
   Future: "Future",
   GabrielGarciaMarquez: "Gabriel García Márquez",
   GabrielGarciaMarquezDesc: "+%{value} Spokojenost",
   GalileoGalilei: "Galileo Galilei",
   GalileoGalileiDesc: "+%{value} Věda od nečinných pracovníků",
   Galleon: "Galleon",
   GalleonBuilder: "Stavitel galeon",
   Gameplay: "Hra",
   Garment: "Oděv",
   GarmentWorkshop: "Oděvní dílna",
   GasPipeline: "Plynovod",
   GasPowerPlant: "Gas Power Plant",
   GatlingGun: "Gatlingův kulomet",
   GatlingGunFactory: "Továrna na kulomety",
   Genetics: "Genetika",
   Geography: "Geografie",
   GeorgeCMarshall: "George C. Marshall",
   GeorgeWashington: "George Washington",
   GeorgiusAgricola: "Georgius Agricola",
   German: "Německý národ",
   Glass: "Sklo",
   Glassworks: "Sklárna",
   GlobalBuildingDefault: "Global Builing Default",
   Globalization: "Globalizace",
   GoBack: "Zpět",
   Gold: "Zlato",
   GoldenGateBridge: "Most Golden Gate",
   GoldenGateBridgeDesc: "Most Golden Gate",
   GoldenPavilion: "Chrám zlatého pavilonu",
   GoldenPavilionDesc: "All buildings within 3 tile range get +1 Production Multiplier for each adjacent building that produces any of its consumed resources (excluding Clone Lab and Clone Factory and the building cannot be turned off)",
   GoldMiningCamp: "Lom na těžbu zlata",
   GordonMoore: "Gordon Moore",
   GrandBazaar: "Velký Bazar",
   GrandBazaarDesc: "Ovládejte všechny trhy na jednom místě!. Všechny sousední budovy získají násobek +5 ke skladování.",
   GrandBazaarFilters: "Filtry",
   GrandBazaarFilterWarningHTML: "Před zobrazením všech tržních obchodů je nutné vybrat filtr.",
   GrandBazaarFilterYouGet: "Získáš",
   GrandBazaarFilterYouPay: "Zaplatíš",
   GrandBazaarSeach: "Search",
   GrandBazaarSearchGet: "Get",
   GrandBazaarSearchPay: "Pay",
   GrandBazaarTabActive: "Aktivní",
   GrandBazaarTabTrades: "Trades",
   GrandCanyon: "Grand Canyon",
   GrandCanyonDesc: "Buildings unlocked in the current age get +2 Production Multiplier. Double the effect of J.P. Morgan",
   GraphicsDriver: "Grafický ovladač: %{driver}",
   GreatDagonPagoda: "Velká pagoda Dagon",
   GreatDagonPagodaDescV2: "All pagodas are exempt from -1 spokojenost. Generate science based on faith production of all pagodas",
   GreatMosqueOfSamarra: "Velká mešita v Sámarře",
   GreatMosqueOfSamarraDescV2: "+1 rozsah vidění budovy. Odhalte 5 náhodných dlaždic neprozkoumaných ložisek a na každé z nich postavte budovu pro těžbu surovin 10. úrovně.",
   GreatPeople: "Osobnosti",
   GreatPeopleEffect: "Efekt",
   GreatPeopleFilter: "Type name or age to filter great people",
   GreatPeopleName: "Název",
   GreatPeoplePermanentColumn: "Permanentní",
   GreatPeoplePermanentShort: "Perma",
   GreatPeoplePickPerRoll: "Great People Pick Per Roll",
   GreatPeopleThisRun: "Osobnosti z tohoto běhu",
   GreatPeopleThisRunColumn: "Tento běh",
   GreatPeopleThisRunShort: "Tento běh",
   GreatPersonLevelRequired: "Požadovaný Permanentní Level Osobností",
   GreatPersonLevelRequiredDescV2: "%{city} civilization requires %{required} permanent great people levels. You currently have %{current}",
   GreatPersonPromotionPromote: "Povýšit",
   GreatPersonThisRunEffectiveLevel: "V současné době máte %{count} %{person} z tohoto běhu. Další %{person} bude mít 1/%{effect} efektu.",
   GreatPersonWildCardBirth: "Narození",
   GreatSphinx: "Velká Sfinga",
   GreatSphinxDesc: "All Tier II or above buildings within 2 tiles get +N Consumption, Production Multiplier. N = Number of its adjacent buildings of the same type",
   GreatWall: "Velká zeď",
   GreatWallDesc: "All buildings within 1 tile range get +N Production, Worker Capacity and Storage Multiplier. N = the number of the different ages between the current age and the age where the building is first unlocked. When constructed next to Forbidden City, the range increases to 2 tile",
   GreedyTransport: "Construction/Upgrade Greedy Transport",
   GreedyTransportDescHTML: "This will make buildings keep transporting resources even if it has enough resources for the current upgrade, which can make upgrading multiple levels <b>faster</b> but end up transport <b>more resources than needed</b>",
   Greek: "Řecký národ",
   GrottaAzzurra: "Grotta Azzurra",
   GrottaAzzurraDescV2: "When discovered, all your Tier I buildings get +5 Level and +1 Production, Worker Capacity and Storage Multiplier",
   Gunpowder: "Střelný prach",
   GunpowderMill: "Mlýn na střelný prach",
   GuyFawkesNightV2: "Guy Fawkes Night: East India Company provides double the Production Multiplier to buildings adjacent to caravansaries. Tower Bridge generates great people 20% faster",
   HagiaSophia: "Hagia Sofia",
   HagiaSophiaDescV2: "+5 Spokojenost. Buildings with 0% Production Capacity are exempt from -1 happiness. During the game bootstrap, provide extra happiness to avoid production halt",
   HallOfFame: "Hall of Fame",
   HallOfSupremeHarmony: "Hall of Supreme Harmony",
   Hammurabi: "Hammurabi",
   HangingGarden: "Visutá zahrada",
   HangingGardenDesc: "Násobitel kapacity stavitele +1. Sousední akvadukty získávají násobitel +1 k produkci, skladování a kapacitě dělníků.",
   Happiness: "Spokojenost",
   HappinessFromBuilding: "Spokojenost z budov (kromě divů)",
   HappinessFromBuildingTypes: "Spokojenost z dobře zásobených typů budov",
   HappinessFromHighestTierBuilding: "Spokojenost z pracovní budovy nejvyšší úrovně",
   HappinessFromUnlockedAge: "Spokojenost z odemčeného věku",
   HappinessFromUnlockedTech: "Spokojenost z odemčené techniky",
   HappinessFromWonders: "Spokojenost z divů (včetně přírodních)",
   HappinessUncapped: "Spokojenost (neomezené)",
   HarryMarkowitz: "Harry Markowitz",
   HarunAlRashid: "Harún al-Rašíd",
   Hatshepsut: "Hatshepsut",
   HatshepsutTemple: "Chrám Hatšepsut",
   HatshepsutTempleDesc: "Odhalí všechny vodní destičky na mapě. Pšeničné farmy získají +1 násobitel produkce za každou vodní destičku sousedící s farmou.",
   Headquarter: "Sídlo společnosti",
   HedgeFund: "Hedgeový fond",
   HelpMenu: "Nápověda",
   HenryFord: "Henry Ford",
   Herding: "Pastevectví",
   Herodotus: "Hérodotos",
   HighlightBuilding: "Highlight %{building}",
   HimejiCastle: "Hrad Himedži",
   HimejiCastleDesc: "Všichni stavitelé karavel, galeon a fregat získají násobek +1 k výrobě, násobek kapacity pracovníků a násobek skladování.",
   Hollywood: "Hollywood",
   HollywoodDesc: "+5 Spokojenost. +1 Spokojenost za každou dobře zásobovanou budovu, která produkuje nebo spotřebovává kultura ve vzdálenosti 2 políček.",
   HolyEmpire: "Svatá říše",
   Homer: "Homer",
   Honor4UpgradeHTML: "Double the effect of <b>Zheng He</b> (Great Person)",
   HonorLevelX: "Honor %{level}",
   Horse: "Kůň",
   HorsebackRiding: "Jízda na koni",
   House: "Dům",
   Housing: "Bydlení",
   Hut: "Chata",
   HydroDam: "Vodní přehrada",
   Hydroelectricity: "Vodní energie",
   HymanGRickover: "Hyman G. Rickover",
   IdeologyDescHTML: "Choose from <b>Liberalism, Conservatism, Socialism or Communism</b> as your empire ideology. You <b>cannot switch ideology</b> after it is chosen. You can unlock more boost within each ideology",
   IMPei: "I. M. Pei",
   IMPeiDesc: "+%{value} Builder Capacity Multiplier",
   Imperialism: "Imperialismus",
   ImperialPalace: "Imperial Palace",
   IndustrialAge: "Průmyslová",
   InformationAge: "Informační věk",
   InputResourceForCloning: "Input Resource For Cloning",
   InternationalSpaceStation: "Mezinárodní vesmírná stanice",
   InternationalSpaceStationDesc: "All buildings get +5 Storage Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Storage Multiplier to all buildings",
   Internet: "Internet",
   InternetServiceProvider: "Poskytovatel internetových služeb",
   InverseSelection: "Inverze",
   Iron: "Železo",
   IronAge: "Doba železná",
   Ironclad: "Ironclad",
   IroncladBuilder: "Stavitel železných plátů",
   IronForge: "Železná kovárna",
   IronMiningCamp: "Lom na těžbu železa",
   IronTech: "Železo",
   IsaacNewton: "Isaac Newton",
   IsaacNewtonDescV2: "+%{value} Science from All Workers if more than 50% of workers are busy and less than 50% of busy workers work in transportation",
   IsambardKingdomBrunel: "Isambard Kingdom Brunel",
   IsidoreOfMiletus: "Isidore of Miletus",
   IsidoreOfMiletusDesc: "+%{value} Multiplikátor kapacity stavitelů",
   Islam5UpgradeHTML: "When unlocked, generate one-time science equivalent to the cost of the most expensive <b>Industrial</b> technology",
   IslamLevelX: "Islám %{level}",
   ItsukushimaShrine: "Svatyně Itsukushima",
   ItsukushimaShrineDescV2: "When all technologies within an age are unlocked, generate one-time science equivalent to the cost of the cheapest technology in the next age",
   JamesWatson: "James Watson",
   JamesWatsonDesc: "+%{value} Science From Busy Workers",
   JamesWatt: "James Watt",
   Japanese: "Japonský národ",
   JetPropulsion: "Tryskový pohon",
   JohannesGutenberg: "Johannes Gutenberg",
   JohannesKepler: "Johannes Kepler",
   JohnCarmack: "John Carmack",
   JohnDRockefeller: "John D. Rockefeller",
   JohnMcCarthy: "John McCarthy",
   JohnVonNeumann: "John von Neumann",
   JohnVonNeumannDesc: "+%{value} Věda z zaněprázdněných pracovníků",
   JoinDiscord: "Připojte se k Discordu",
   JosephPulitzer: "Joseph Pulitzer",
   Journalism: "Žurnalistika",
   JPMorgan: "J.P. Morgan",
   JRobertOppenheimer: "J. Robert Oppenheimer",
   JuliusCaesar: "Julius Caesar",
   Justinian: "Justinián",
   Kanagawa: "Kanagawa",
   KanagawaDesc: "All great people of the current age get an additional level for this run (excluding Zenobia)",
   KarlMarx: "Karl Marx",
   Knight: "Rytíř",
   KnightCamp: "Rytířský tábor",
   Koti: "Koti",
   KotiInStorage: "Koti In Storage",
   KotiProduction: "Koti Production",
   LandTrade: "Obchod s pozemky",
   Language: "Jazyk",
   Lapland: "Lapland",
   LaplandDesc: "When discovered, reveal the whole map. All buildings within 2-tile range get +5 Production Multiplier. This natural wonder can only be discovered in December",
   LargeHadronCollider: "Velký hadronový urychlovač",
   LargeHadronColliderDescV2: "All Information Age great people get +2 level for this run. This wonder can be upgraded and each additional upgrade provides +1 level to all Information Age great people for this run",
   Law: "Právo",
   Lens: "Objektiv",
   LensWorkshop: "Dílna čoček",
   LeonardoDaVinci: "Leonardo da Vinci",
   Level: "Úroveň",
   LevelX: "Úroveň %{level}",
   Liberalism: "Liberalism",
   LiberalismLevel3DescHTML: "Free transport <b>from</b> and <b>to</b> warehouses",
   LiberalismLevel5DescHTML: "<b>Double</b> the electrification effect",
   LiberalismLevelX: "Liberalism Level %{level}",
   Library: "Knihovna",
   LighthouseOfAlexandria: "Alexandrijský maják",
   LighthouseOfAlexandriaDesc: "Všechny přilehlé budovy získají násobitel +5 za skladování",
   LinusPauling: "Linus Pauling",
   LinusPaulingDesc: "+%{value} Science From Idle Workers",
   Literature: "Literatura",
   LiveData: "Live Value",
   LocomotiveFactory: "Továrna na lokomotivy",
   Logging: "Přihlašování",
   LoggingCamp: "Lom na těžbu dřeva",
   LouisSullivan: "Louis Sullivan",
   LouisSullivanDesc: "+%{value} Builder Capacity Multiplier",
   Louvre: "Louvre",
   LouvreDesc: "For every 10 Extra Great People at Rebirth, one great person from all unlocked ages is born",
   Lumber: "Prkno",
   LumberMill: "Dřevařský závod",
   LunarNewYear: "Lunar New Year: Great Wall provides double the boost to buildings. Porcelain Tower provides +1 level to all great people from this run",
   LuxorTemple: "Luxorský chrám",
   LuxorTempleDescV2: "+1 Science From Busy Workers. Choose an empire religion, unlock more boost with each choice",
   Machinery: "Stroje",
   Magazine: "Časopis",
   MagazinePublisher: "Vydavatelství časopisů",
   Maglev: "Maglev",
   MaglevFactory: "Maglev Factory",
   MahatmaGandhi: "Mahatma Gandhi",
   ManageAgeWisdom: "Spravovat Moudrost Věků",
   ManagedImport: "Spravovat Import",
   ManagedImportDescV2: "This building will automatically import resources produced within %{range} tile range. Resource transports for this building cannot be manually changed. Max transport distance will be ignored",
   ManageGreatPeople: "Spravovat osobnosti",
   ManagePermanentGreatPeople: "Spravovat permanentní osobnosti",
   ManageSave: "Spravovat Uložení",
   ManageWonders: "Spravovat Divy Světa",
   Manhattan: "Manhattan",
   ManhattanProject: "Manhattan Project",
   ManhattanProjectDesc: "Manhattan Project",
   Marble: "Mramor",
   Marbleworks: "Mramorovna",
   MarcoPolo: "Marco Polo",
   MarieCurie: "Marie Curie",
   MarinaBaySands: "Marina Bay Sands",
   MarinaBaySandsDesc: "All buildings get +5 Worker Capacity Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Worker Capacity Multiplier to all buildings",
   Market: "Trh",
   MarketDesc: "Vyměňte surovinu za jinou, dostupné suroviny se aktualizují každou hodinu",
   MarketRefreshMessage: "Trades in %{count} markets has been refreshed",
   MarketSell: "Prodej",
   MarketSettings: "Nastavení trhu",
   MarketValueDesc: "%{value} v porovnání s průměrnou cenou",
   MarketYouGet: "Dostanete",
   MarketYouPay: "Platíte vy",
   MartinLuther: "Martin Luther",
   MaryamMirzakhani: "Maryam Mirzakhani",
   MaryamMirzakhaniDesc: "+%{value} Science From Idle Workers",
   Masonry: "Zednické práce",
   MatrioshkaBrain: "Matrioshka Brain",
   MatrioshkaBrainDescV2: "Allow Science to be counted when calculating empire value (5 Science = 1 Empire Value). +5 Science Per Busy and Idle Worker. This wonder can be upgraded and each additional upgrade provides +1 Science Per Busy and Idle Worker and +1 Production Multiplier for buildings that produce Science",
   MausoleumAtHalicarnassus: "Mauzoleum v Halikarnassu",
   MausoleumAtHalicarnassusDescV2: "Transports from or to buildings within 2 tile range do not cost workers",
   MaxExplorers: "Max Explorers",
   MaxTransportDistance: "Maximální přepravní vzdálenost",
   Meat: "Maso",
   Metallurgy: "Hutnictví",
   Michelangelo: "Michelangelo",
   MiddleAge: "Středověk",
   MilitaryTactics: "Military Tactics",
   Milk: "Mléko",
   Moai: "Moai",
   MoaiDesc: "Moai",
   MobileOverride: "Mobile Override",
   MogaoCaves: "Jeskyně Mogao",
   MogaoCavesDescV3: "+1 spokojenost for every 10% of busy workers. All adjacent buildings that produce faith are exempt from -1 spokojenost",
   MonetarySystem: "Měnový systém",
   MontSaintMichel: "Mont Saint-Michel",
   MontSaintMichelDesc: "Generate Culture from Idle Workers. Provide +1 Storage Multiplier to all buildings within 2-tile range. This wonder can be upgraded using the generated Culture and each level provides addtional +1 Storage Multiplier",
   Mosque: "Mešita",
   MotionPicture: "Pohyblivý obraz",
   MountArarat: "Mount Ararat",
   MountAraratDesc: "All buildings within 2 tile range get +X Production, Worker Capacity and Storage Multiplier. X = cubic root of Effective Great People Level",
   MountFuji: "Hora Fuji",
   MountFujiDescV2: "When Petra is built next to it, Petra gets +8h Warp storage. When the game is running, generate 20 warp every minute in Petra (not accelerated by Petra itself, not generating when the game is offline)",
   MountSinai: "Hora Sinai",
   MountSinaiDesc: "When discovered, a great person of the current age is born. All buildings that produce faith get +5 Storage Multiplier",
   MountTai: "Hora Tai",
   MountTaiDesc: "All buildings that produce science get +1 Production Multiplier. Double the effect of Confucious (Great Person). When discovered, generate one-time science equivalent to the cost of the most expensive unlocked technology",
   MoveBuilding: "Posunout budovu",
   MoveBuildingFail: "Vybraná políčko není platné.",
   MoveBuildingNoTeleport: "Nemáte dostatek teleportu",
   MoveBuildingSelectTile: "Zvolte políčko...",
   MoveBuildingSelectTileToastHTML: "Zvolte <b>volné objevené políčko</b> na mapě jako váš cíl",
   Movie: "Film",
   MovieStudio: "Filmové Studio",
   Museum: "Muzeum",
   Music: "Hudba",
   MusiciansGuild: "Cech hudebníků",
   MutualAssuredDestruction: "Vzájemně Zaručené Zničení",
   MutualFund: "Podílový fond",
   Name: "Název",
   Nanotechnology: "Nanotechnologie",
   NapoleonBonaparte: "Napoleon Bonaparte",
   NaturalGas: "Zemní plyn",
   NaturalGasWell: "Vrt na zemní plyn",
   NaturalWonderName: "Přírodní zázrak: %{name}",
   NaturalWonders: "Přírodní divy",
   Navigation: "Navigace",
   NebuchadnezzarII: "Nabuchodonozor II.",
   Neuschwanstein: "Neuschwanstein",
   NeuschwansteinDesc: "+10 násobitel kapacity stavitele při stavbě divů",
   Newspaper: "Noviny",
   NextExplorersIn: "Další průzkumnící za",
   NextMarketUpdateIn: "Příští aktualizace trhu za",
   NiagaraFalls: "Niagarské vodopády",
   NiagaraFallsDescV2: "All warehouses, markets and caravansaries get +N storage multiplier. N = number of unlocked ages. Albert Einstein provides +1 Production Multiplier to Research Fund (not affected by other boosts like Broadway)",
   NielsBohr: "Niels Bohr",
   NielsBohrDescV2: "+%{value} Science from All Workers if more than 50% of workers are busy and less than 50% of busy workers work in transportation",
   NileRiver: "Nile River",
   NileRiverDesc: "Double the effect of Hatshepsut. All wheat farms get +1 Production and Storage Multiplier. All adjacent wheat farms get +5 Production and Storage Multiplier",
   NoPowerRequired: "Tato budova nepotřebuje energii",
   NothingHere: "Zde není nic",
   NotProducingBuildings: "Budovy, které neprodukují",
   NuclearFission: "Jaderné štěpení",
   NuclearFuelRod: "Jaderné palivové články",
   NuclearMissile: "Jaderná střela",
   NuclearMissileSilo: "Silo pro jaderné střely",
   NuclearPowerPlant: "Jaderná elektrárna",
   NuclearReactor: "Jaderný reaktor",
   NuclearSubmarine: "Jaderná ponorka",
   NuclearSubmarineYard: "Loděnice pro jaderné ponorky",
   OdaNobunaga: "Oda Nobunaga",
   OfflineErrorMessage: "V současné době jste offline, tato operace vyžaduje připojení k internetu.",
   OfflineProduction: "Offline výroba",
   OfflineProductionTime: "Offline Production Time",
   OfflineProductionTimeDescHTML: "For the <b>first %{time} offline time</b>, you can choose either offline production or time warp - you can set the split here. The <b>rest of the offline time</b> can only be converted to time warp",
   OfflineTime: "Čas offline",
   Oil: "Ropa",
   OilPress: "Lis na olej",
   OilRefinery: "Ropná rafinerie",
   OilWell: "Ropný vrt",
   Ok: "OK",
   Oktoberfest: "Oktoberfest: Zdvojnásobuje efekt Zugspitze",
   Olive: "Olivový olej",
   OlivePlantation: "Olivová plantáž",
   Olympics: "Olympijské hry",
   OnlyAvailableWhenPlaying: "Only available when playing %{city}",
   OpenLogFolder: "Otevřít složku s logy",
   OpenSaveBackupFolder: "Otevření záložní složky",
   OpenSaveFolder: "Otevřít úložní složku",
   Opera: "Opera",
   OperationNotAllowedError: "Tato operace není povolena",
   Opet: "Opet: Velká Sfinga již nezvyšuje multiplikátor spotřeby",
   OpticalFiber: "Optické vlákno",
   OpticalFiberPlant: "Fabrika na Optické vlákno",
   Optics: "Optika",
   OptionsMenu: "Možnosti",
   OptionsUseModernUIV2: "Použít vyhlazené písmo",
   OsakaCastle: "Osaka Castle",
   OsakaCastleDesc: "Provide power to all tiles within 2 tile range. Allow electrification of science producing buildings (including Clone Lab)",
   OtherPlatform: "Jiné platformy",
   Ottoman: "Ottoman",
   OttoVonBismarck: "Otto von Bismarck",
   OxfordUniversity: "Oxfordská univerzita",
   OxfordUniversityDescV3: "+10% produkce vědy pro budovy, které produkují vědu. Po dokončení vygenerují jednorázové množství vědy odpovídající nákladům na nejnákladnější odemčenou technologii.",
   PabloPicasso: "Pablo Picasso",
   Pagoda: "Pagoda",
   PaintersGuild: "Cech malířů",
   Painting: "Malířství",
   PalmJumeirah: "Palm Jumeirah",
   PalmJumeirahDesc: "+10 Builder Capacity. This wonder can be upgraded and each additional upgrade provides +2 Builder Capacity",
   Pamukkale: "Pamukkale",
   PamukkaleDesc: "When discovered, convert each one of the Permanent Great People Shards (except for Promotion and Wildcard) to the same Great Person From This Run",
   Panathenaea: "Panathenaea: Poseidon provides +1 Production Multiplier to all buildings",
   Pantheon: "Pantheon",
   PantheonDescV2: "All buildings within 2 tile range get +1 Worker Capaicity and Storage Multiplier. Generate science based on faith production of all shrines",
   Paper: "Papír",
   PaperMaker: "Výrobce papíru",
   Parliament: "Parlament",
   Parthenon: "Parthenon",
   ParthenonDescV2: "Two great people of Classical Age are born and you get 4 choices for each. Musician's Guilds and Painter's Guilds get +1 Production, Worker Capacity and Storage Multiplier and are exempt from -1 Happiness",
   Passcode: "Passcode",
   PasscodeToastHTML: "<b>%{code}</b> is your passcode and it's valid for 30 minutes",
   PatchNotes: "Poznámky k záplatám",
   Peace: "Mír",
   Peacekeeper: "Peacekeeper",
   Penthouse: "Penthouse",
   PercentageOfProductionWorkers: "Percentage of Production Workers",
   Performance: "Performance",
   PermanentGreatPeople: "Permanentní Osobnosti",
   PermanentGreatPeopleAcquired: "Permanent Great People Acquired",
   PermanentGreatPeopleUpgradeUndo: "Undo permanent great people upgrade: this will convert upgraded level back to shards - you will get %{amount} shards",
   Persepolis: "Persepolis",
   PersepolisDesc: "Všechny měděné těžební tábory, dřevařské tábory a kamenolomy získají +1 násobek produkce, násobek kapacity pracovníků a násobek skladování",
   PeterHiggs: "Peter Higgs",
   PeterHiggsDesc: "+%{value} Věda z zaneprázdněných pracovníků",
   Petra: "Petra",
   PetraDesc: "Generování časové deformace, když jste offline, kterou můžete použít k urychlení svého impéria",
   PetraOfflineTimeReconciliation: "You have been credited %{count} warp after server offline time reconciliation",
   Petrol: "Benzín",
   PhiloFarnsworth: "Philo Farnsworth",
   Philosophy: "Filozofie",
   Physics: "Fyzika",
   PierreDeCoubertin: "Pierre de Coubertin",
   Pizza: "Pizza",
   Pizzeria: "Pizzerie",
   PlanetaryRover: "Planetární rover",
   Plastics: "Plast",
   PlasticsFactory: "Plastics Factory",
   PlatformAndroid: "Android",
   PlatformiOS: "iOS",
   PlatformSteam: "Steam",
   PlatformSyncInstructionHTML: "If you want to sync your progress on this device to a new device, click <b>Sync To A New Device</b> and get a one-time passcode. On your new device, click <b>Connect To A Device</b> and type in the one-time passcode",
   Plato: "Plato",
   PlayerHandle: "Přezdívka hráče",
   PlayerHandleOffline: "V současné době jste offline",
   PlayerMapClaimThisTile: "Zabrat toto políčko",
   PlayerMapClaimTileCondition2: "Nebyli jste zabanováni anti-cheatem",
   PlayerMapClaimTileCondition3: "Odemkli jste požadovanou technologii: %{tech}",
   PlayerMapClaimTileCondition4: "Nevyžádali jste si políčko nebo jste překročili cooldown pro přesunutí políčka",
   PlayerMapClaimTileCooldownLeft: "Zbývající doba platnosti: %{time}",
   PlayerMapClaimTileNoLongerReserved: "Toto políčko již není rezervované. Můžete vystěhovat <b>%{name}</b> a nárokovat si toto políčko pro sebe.",
   PlayerMapEstablishedSince: "Založeno od",
   PlayerMapLastSeenAt: "Naposledy viděno",
   PlayerMapMapTileBonus: "Trade Tile Bonus",
   PlayerMapMenu: "Obchod",
   PlayerMapOccupyThisTile: "Occupy This Tile",
   PlayerMapOccupyTileCondition1: "This tile is adjacent to your home or occupied tiles",
   PlayerMapPageGoBackToCity: "Vrátit se do města",
   PlayerMapSetYourTariff: "Nastavte svůj tarif",
   PlayerMapTariff: "Tarif",
   PlayerMapTariffApply: "Použijte tarifní sazbu",
   PlayerMapTariffDesc: "Každý obchod, který projde přes vaši dlaždici, vám zaplatí tarif. Je to rovnováha: pokud zvýšíte tarif, tím více získáte z každého obchodu, ale přes vaši dlaždici projde méně obchodů.",
   PlayerMapTileAvailableTilePoint: "Available Tile Point",
   PlayerMapTileFromOccupying: "From Owned/Occupied Tiles",
   PlayerMapTileFromOccupyingTooltipHTML: "An owned/occupied tile generates <b>%{point}</b> tile point per hour (up to %{max} days from the first claimed tile)",
   PlayerMapTileFromRank: "From Account Rank",
   PlayerMapTileTilePoint: "Tile Point",
   PlayerMapTileUsedTilePoint: "Used Tile Point",
   PlayerMapTileUsedTilePointTooltipHTML: "You need <b>1 tile point</b> to own/occupy a tile",
   PlayerMapTradesFrom: "Obchody od %{name}",
   PlayerMapUnclaimedTile: "Nezabrané políčko",
   PlayerMapYourTile: "Vaše políčko",
   PlayerTrade: "Obchod s hráči",
   PlayerTradeAddSuccess: "Obchod byl úspěšně přidán",
   PlayerTradeAddTradeCancel: "Zrušit",
   PlayerTradeAmount: "Částka",
   PlayerTradeCancelDescHTML: "You will get <b>%{res}</b> back after cancelling this trade: <b>%{percent}</b> charged for refund and <b>%{discard}</b> discarded due to storage overflow<br><b>Are you sure you want to cancel?</b>",
   PlayerTradeCancelTrade: "Zrušit obchod",
   PlayerTradeClaim: "Nárok",
   PlayerTradeClaimAll: "Nárok na všechny",
   PlayerTradeClaimAllFailedMessageV2: "Failed to claim any trades - is the storage full?",
   PlayerTradeClaimAllMessageV2: "You have claimed: <b>%{resources}</b>",
   PlayerTradeClaimAvailable: "%{count} obchod(y) byl(y) naplněn(y) k dispozici pro reklamaci",
   PlayerTradeClaimTileFirst: "Nejdříve zaberte políčko na obchodní mapě",
   PlayerTradeClaimTileFirstWarning: "Obchodovat s ostatními hráči můžete až poté, co si na obchodní mapě nárokují svou dlaždici.",
   PlayerTradeClearAll: "Clear All Fills",
   PlayerTradeClearFilter: "Clear Filters",
   PlayerTradeDisabledBeta: "You can only create player trades once the beta version is released",
   PlayerTradeFill: "Vyplňte",
   PlayerTradeFill50: "Fill 50%",
   PlayerTradeFill95: "Fill 95%",
   PlayerTradeFillAmount: "Částka plnění",
   PlayerTradeFillAmountMaxV2: "Naplnění maxima",
   PlayerTradeFillBy: "Vyplnit podle",
   PlayerTradeFillPercentage: "Fill Percentage",
   PlayerTradeFillSuccessV2: "<b>%{success}/%{total}</b> trades have been filled. You paid <b>%{fillAmount} %{fillResource}</b> and received <b>%{receivedAmount} %{receivedResource}</b>",
   PlayerTradeFillTradeButton: "Vyplnit obchod",
   PlayerTradeFillTradeTitle: "Vyplnit obchod",
   PlayerTradeFilters: "Filters",
   PlayerTradeFiltersApply: "Apply",
   PlayerTradeFiltersClear: "Clear",
   PlayerTradeFilterWhatIHave: "Filter By What I Have",
   PlayerTradeFrom: "Z",
   PlayerTradeIOffer: "Nabízím",
   PlayerTradeIWant: "Chci",
   PlayerTradeMaxAll: "Max All Fills",
   PlayerTradeMaxTradeAmountFilter: "Max Množství",
   PlayerTradeMaxTradeExceeded: "Překročili jste maximální počet aktivních obchodů pro hodnost vašeho účtu.",
   PlayerTradeNewTrade: "Nový obchod",
   PlayerTradeNoFillBecauseOfResources: "No trade has been filled due to insufficient resources",
   PlayerTradeNoValidRoute: "Nelze najít platnou obchodní cestu mezi vámi a %{name}",
   PlayerTradeOffer: "Nabídka",
   PlayerTradePlaceTrade: "Umístit obchod",
   PlayerTradePlayerNameFilter: "Jméno hráče",
   PlayerTradeResource: "Zdroje",
   PlayerTradeStorageRequired: "Požadované skladování",
   PlayerTradeTabImport: "Import",
   PlayerTradeTabPendingTrades: "Pending Trades",
   PlayerTradeTabTrades: "Obchody",
   PlayerTradeTariffTooltip: "Vybrané z obchodního cla",
   PlayerTradeWant: "Chci",
   PlayerTradeYouGetGross: "Dostanete (před tarifem): %{res}",
   PlayerTradeYouGetNet: " %{res}",
   PlayerTradeYouPay: "Platíte: %{res}",
   Poem: "Báseň",
   PoetrySchool: "Poetry School",
   Politics: "Politika",
   PolytheismLevelX: "Polytheism %{level}",
   PorcelainTower: "Porcelain Tower",
   PorcelainTowerDesc: "+5 Happiness. When constructed, all your extra great people at rebirth will become available for this run (they are rolled following the same rule as permanent great people)",
   PorcelainTowerMaxPickPerRoll: "Prefer Max Pick Per Roll",
   PorcelainTowerMaxPickPerRollDescHTML: "When choosing great people after Porcelain Tower completed, prefer max pick per roll for the available amount",
   Poseidon: "Poseidon",
   PoseidonDescV2: "All adjacent buildings get free upgrades to Level 25 and +N Production, Worker Capacity and Storage Multiplier. N = Tier of the building",
   PoultryFarm: "Poultry Farm",
   Power: "Výkon",
   PowerAvailable: "Dostupná energie",
   PowerUsed: "Spotřebovaný výkon",
   PreciousMetal: "Drahé kovy",
   Printing: "Tisk",
   PrintingHouse: "Tiskárna",
   PrintingPress: "Tiskárna",
   PrivateOwnership: "Soukromé vlastnictví",
   Produce: "Produkovat",
   ProduceResource: "Produkovat: %{resource}",
   ProductionMultiplier: "Multiplikátor produkce",
   ProductionPriority: "Priorita výroby",
   ProductionPriorityDescV4: "Priority determins the order that buildings transport and produce - a bigger number means a building transports and produces before other buildings",
   ProductionWorkers: "Production Workers",
   Progress: "Pokrok",
   ProgressTowardsNextGreatPerson: "Postup k další velké osobě při znovuzrození",
   ProgressTowardsTheNextGreatPerson: "Progress Towards the Next Great Person",
   PromotionGreatPersonDescV2: "When consumed, promote any permanent great people of the same age to the next age",
   ProphetsMosque: "Prorokova mešita",
   ProphetsMosqueDesc: "Double the effect of Harun al-Rashid. Generate science based on faith production of all mosques",
   Province: "Provincie",
   ProvinceAegyptus: "Aegyptus",
   ProvinceAfrica: "Afrika",
   ProvinceAsia: "Asie",
   ProvinceBithynia: "Bithynie",
   ProvinceCantabri: "Cantabri",
   ProvinceCappadocia: "Kappadokie",
   ProvinceCilicia: "Kilikie",
   ProvinceCommagene: "Kommagene",
   ProvinceCreta: "Kréta",
   ProvinceCyprus: "Kypr",
   ProvinceCyrene: "Kyréna",
   ProvinceGalatia: "Galacie",
   ProvinceGallia: "Gallia",
   ProvinceGalliaCisalpina: "Gallia Cisalpina",
   ProvinceGalliaTransalpina: "Gallia Transalpina",
   ProvinceHispania: "Hispania",
   ProvinceIllyricum: "Illyricum",
   ProvinceItalia: "Italia",
   ProvinceJudia: "Judie",
   ProvinceLycia: "Lýkie",
   ProvinceMacedonia: "Makedonie",
   ProvinceMauretania: "Mauretánie",
   ProvinceNumidia: "Numidie",
   ProvincePontus: "Pontus",
   ProvinceSardiniaAndCorsica: "Sardinie a Korsika",
   ProvinceSicillia: "Sicillia",
   ProvinceSophene: "Sofie",
   ProvinceSyria: "Sýrie",
   PublishingHouse: "Nakladatelství",
   PyramidOfGiza: "Pyramida v Gíze",
   PyramidOfGizaDesc: "Všechny budovy, které produkují dělníky, získají +1 výrobní násobek",
   QinShiHuang: "Čchin Š'-chuang",
   Radio: "Rádio",
   RadioStation: "Rádiová Stanice",
   Railway: "Železnice",
   RamessesII: "Ramses II.",
   RamessesIIDesc: "+%{value} Násobitel kapacity stavitelů",
   RandomColorScheme: "Random Color Scheme",
   RapidFire: "Rychlá palba",
   ReadFullPatchNotes: "Read Patch Notes",
   RebirthHistory: "Rebirth History",
   RebirthTime: "Rebirth Time",
   Reborn: "Znovuzrození",
   RebornModalDescV3: "You will start a new empire but all your great people <b>from this run</b> becomes permanent shards, which can be used to upgrade your <b>permanent great people level</b>. You will also get extra great people shards based on your <b>total empire value</b>",
   RebornOfflineWarning: "V současné době jste offline. Znovuzrození můžete provést pouze tehdy, když jste připojeni k serveru",
   RebornTradeWarning: "Máte obchody, které jsou aktivní nebo o které lze požádat. <b>Znovuzrození je vymaže</b> - měli byste nejprve zvážit jejich zrušení nebo reklamaci.",
   RedistributeAmongSelected: "Přerozdělit mezi vybrané",
   RedistributeAmongSelectedCap: "Cap",
   RedistributeAmongSelectedImport: "Import",
   Refinery: "Rafinerie",
   Reichstag: "Reichstag",
   Religion: "Náboženství",
   ReligionBuddhism: "Buddhismus",
   ReligionChristianity: "Křesťanství",
   ReligionDescHTML: "Choose from <b>Christianity, Islam, Buddhism or Polytheism</b> as your empire religion. You <b>cannot switch religion</b> after it is chosen. You can unlock more boost within each religion",
   ReligionIslam: "Islám",
   ReligionPolytheism: "Polytheismus",
   Renaissance: "Renesance",
   RenaissanceAge: "Renesance",
   ReneDescartes: "René Descartes",
   RequiredDeposit: "Požadovaná záloha",
   RequiredWorkersTooltipV2: "Required number of workers for production is equal to the sum of all resources consumed and produced after multipliers (excluding dynamic multipliers)",
   RequirePower: "Požadovaná Energie",
   RequirePowerDesc: "This building needs to be built on a tile with power and can extend the power to its adjacent tiles",
   Research: "Výzkum",
   ResearchFund: "Výzkumný fond",
   ResearchLab: "Výzkumná laboratoř",
   ResearchMenu: "Výzkum",
   ResourceAmount: "Částka",
   ResourceBar: "Panel zdrojů",
   ResourceBarExcludeStorageFullHTML: "Exclude buildings that have <b>full storage</b> from Not Producing Buildings",
   ResourceBarExcludeTurnedOffOrNoActiveTransportHTML: "Exclude buildings that are <b>turned off</b> from Not Producing Buildings",
   ResourceBarShowUncappedHappiness: "Zobrazit neomezenou spokojenost",
   ResourceCloneTooltip: "The production multiplier only applies to the cloned resource (i.e. the extra copy)",
   ResourceColor: "Barva surovin",
   ResourceExportBelowCap: "Vývoz pod horní hranici",
   ResourceExportBelowCapTooltip: "Povolit ostatním budovám transportovat surovinu z této budovy, i když je její množství nižší než limit.",
   ResourceExportToSameType: "Export do stejného typu",
   ResourceExportToSameTypeTooltip: "Umožnit ostatním budovám stejného typu transportovat surovinu z této budovy.",
   ResourceFromBuilding: "%{resource} od %{building}",
   ResourceImport: "Přeprava zdrojů",
   ResourceImportCapacity: "Kapacita přepravy zdrojů",
   ResourceImportImportCapV2: "Maximální množství",
   ResourceImportImportCapV2Tooltip: "This building will stop transporting thhis resource when the max amount is reached",
   ResourceImportImportPerCycleV2: "Za Cyklus",
   ResourceImportImportPerCycleV2ToolTip: "The amount of this resource that is transported per cycle",
   ResourceImportPartialWarningHTML: "The total resource transport capacity has exceeds the maximum capacity: <b>each resource transport will only transport partially per cycle</b>",
   ResourceImportResource: "Surovina",
   ResourceImportSettings: "Přeprava zdrojů: %{res}",
   ResourceImportStorage: "Úložiště",
   ResourceNeeded: "Extra %{resource} x%{amount} Needed",
   ResourceTransportPreference: "Preference přepravy",
   RevealDeposit: "Odhalení",
   Revolution: "Revoluce",
   RhineGorge: "Rhine Gorge",
   RhineGorgeDesc: "+2 Happiness for each wonder within 2 tile range",
   RichardFeynman: "Richard Feynman",
   RichardFeynmanDesc: "+%{value} Science from All Workers if more than 50% of workers are busy and less than 50% of busy workers work in transportation",
   RichardJordanGatling: "Richard Jordan Gatling",
   Rifle: "Pušky",
   RifleFactory: "Továrna na pušky",
   Rifling: "Drážkování",
   Rijksmuseum: "Rijksmuseum",
   RijksmuseumDesc: "+5 spokojenost. Všechny budovy, které spotřebovávají nebo vyrábějí kulturu, získávají +1 k produkci, skladování a kapacitě pracovníků.",
   RoadAndWheel: "Cesta a kolo",
   RobertNoyce: "Robert Noyce",
   Robocar: "Roboauto",
   RobocarFactory: "Továrna na Roboauta",
   Robotics: "Robotika",
   RockefellerCenterChristmasTree: "Rockefeller Center Christmas Tree",
   RockefellerCenterChristmasTreeDesc: "+3 Spokojenost za každou odemčenou dobu. This natural wonder can only be discovered in December",
   Rocket: "Rocket",
   RocketFactory: "Rocket Factory",
   Rocketry: "Raketová technika",
   Roman: "Římský národ",
   RomanForum: "Římské fórum",
   RudolfDiesel: "Rudolf Diesel",
   Rurik: "Rurik",
   RurikDesc: "+%{value} Spokojenost",
   SagradaFamilia: "Sagrada Família",
   SagradaFamiliaDesc: "Sagrada Família",
   SaintBasilsCathedral: "Katedrála sv. Basila",
   SaintBasilsCathedralDescV2: "Povolení staveb pro těžbu surovin v sousedství ložiska. Všechny budovy I. úrovně získají násobitel +1 k produkci, násobitel kapacity pracovníků a násobitel skladování.",
   Saladin: "Saladin",
   Samsuiluna: "Samsu-iluna",
   Sand: "Písek",
   Sandpit: "Pískovna",
   SantaClausVillage: "Santa Claus Village",
   SantaClausVillageDesc: "When completed, a great person of the current age is born. This wonder can be upgraded and each additional upgrade provides an extra great person. When choosing great people from this wonder, 4 choices are provided. This wonder can only be constructed in December",
   SargonOfAkkad: "Sargon Akkadský",
   Satellite: "Satellite",
   SatelliteFactory: "Satellite Factory",
   SatoshiNakamoto: "Satoshi Nakamoto",
   Saturnalia: "Saturnalia: Alps no longer increases Consumption Multiplier",
   SaveAndExit: "Uložit a ukončit",
   School: "Škola",
   Science: "Věda",
   ScienceFromBusyWorkers: "Věda z vytížených pracovníků",
   ScienceFromIdleWorkers: "Věda od nečinných pracovníků",
   SciencePerBusyWorker: "Na jednoho vytíženého pracovníka",
   SciencePerIdleWorker: "Na nečinného pracovníka",
   ScrollSensitivity: "Scroll Sensitivity",
   ScrollSensitivityDescHTML: "Adjust sensitivity when scrolling mousewheel. <b>Must be between 0.01 to 100. Default is 1</b>",
   ScrollWheelAdjustLevelTooltip: "Když se kurzor nachází nad touto položkou, můžete úroveň nastavit pomocí kolečka.",
   SeaTradeCost: "Náklady na námořní obchod",
   SeaTradeUpgrade: "Obchodování s hráči za mořem. Tarif pro každou destičku moře: %{tariff}",
   SelectCivilization: "Select Civilization",
   SelectedAll: "Vybrat vše",
   SelectedCount: "%{count} Vybrané",
   Semiconductor: "Polovodiče",
   SemiconductorFab: "Semiconductor Fab",
   SendExplorer: "Send Explorer",
   SergeiKorolev: "Sergei Korolev",
   SetAsDefault: "Nastavit jako výchozí",
   SetAsDefaultBuilding: "Nastavit jako výchozí pro všechny %{building}",
   Shamanism: "Šamanismus",
   Shelter: "Úkryt",
   Shortcut: "Zkratka",
   ShortcutBuildingPageSellBuildingV2: "Demolish Building",
   ShortcutBuildingPageToggleBuilding: "Přepínání výroby",
   ShortcutBuildingPageToggleBuildingSetAllSimilar: "Přepnout výrobu a použít na všechny",
   ShortcutBuildingPageUpgrade1: "Upgrade Tlačítko 1 (+1)",
   ShortcutBuildingPageUpgrade2: "Upgrade Tlačítko 2 (+5)",
   ShortcutBuildingPageUpgrade3: "Upgrade Tlačítko 3 (+10)",
   ShortcutBuildingPageUpgrade4: "Upgrade Tlačítko 4 (+15)",
   ShortcutBuildingPageUpgrade5: "Upgrade Tlačítko 5 (+20)",
   ShortcutClear: "Vymazat",
   ShortcutConflict: "Vaše klávesová zkratka je v konfliktu s %{name}.",
   ShortcutNone: "Žádné",
   ShortcutPressShortcut: "Stiskněte klávesovou zkratku...",
   ShortcutSave: "Uložit",
   ShortcutScopeBuildingPage: "Stránka s budovami",
   ShortcutScopeConstructionPage: "Consctruction/Upgrade Page",
   ShortcutScopeEmptyTilePage: "Stránka s prázdnými dlaždicemi",
   ShortcutScopePlayerMapPage: "Stránka obchodní mapy",
   ShortcutScopeTechPage: "Technická stránka",
   ShortcutScopeUnexploredPage: "Unexplored Page",
   ShortcutTechPageGoBackToCity: "Vrátit se do města",
   ShortcutTechPageUnlockTech: "Odemknout vybranou techniku",
   ShortcutUpgradePageCancelAllUpgrades: "Cancel All Upgrades",
   ShortcutUpgradePageCancelUpgrade: "Zrušit Upgrade",
   ShortcutUpgradePageDecreaseLevel: "Snížit úroveň vylepšení",
   ShortcutUpgradePageEndConstruction: "Ukončit konstrukci",
   ShortcutUpgradePageIncreaseLevel: "Zvýšit úroveň vylepšení",
   ShowTransportArrow: "Show Transport Arrow",
   ShowTransportArrowDescHTML: "Turning this off will hide transport arrows. It might <i>slightly</i> improve performance on low end devices. Performance improvement takes effect <b>after restarting your game</b>",
   ShowUnbuiltOnly: "Zobrazit pouze budovy, které ještě nebyly postaveny.",
   Shrine: "Svatyně",
   SidePanelWidth: "Side Panel Width",
   SidePanelWidthDescHTML: "Změnit šířku bočního panelu. <b>Aby se projevilo, je nutné restartovat hru.</b>",
   SiegeRam: "Obléhací beranidlo",
   SiegeWorkshop: "Obléhací dílna",
   Silicon: "Křemík",
   SiliconSmelter: "Tavicí pec na křemík",
   Skyscraper: "Mrakodrap",
   Socialism: "Socialismus",
   SocialismLevel4DescHTMLV2: "Generate one-time science equivalent to the cost of the cheapest <b>World Wars Age</b> technology",
   SocialismLevel5DescHTMLV2: "Generate one-time science equivalent to the cost of the cheapest <b>Cold War Age</b> technology",
   SocialismLevelX: "Level Socialismu %{level}",
   SocialNetwork: "Sociální síť",
   Socrates: "Sokrates",
   SocratesDesc: "+%{value} Věda od zaneprázdněných dělníků",
   Software: "Software",
   SoftwareCompany: "Software Firma",
   Sound: "Zvuk",
   SoundEffect: "Zvukový efekt",
   SourceGreatPerson: "Osobnost: %{person}",
   SourceGreatPersonPermanent: "Permanentní Osobnost: %{person}",
   SourceIdeology: "Ideologie: %{ideology}",
   SourceReligion: "Religie: %{religion}",
   SourceResearch: "Výzkum: %{tech}",
   SourceTradition: "Tradice: %{tradition}",
   SpaceCenter: "Vesmírné centrum",
   Spacecraft: "Vesmírná loď",
   SpacecraftFactory: "Továrna na Vesmírné loďe",
   SpaceNeedle: "Space Needle",
   SpaceNeedleDesc: "+1 Happiness for each wonder constructed",
   SpaceProgram: "Vesmírný program",
   Sports: "Sport",
   Stable: "Stáj",
   Stadium: "Stadion",
   StartFestival: "Let the Festival Begin!",
   Stateship: "Státnost",
   StatisticsBuildings: "Budovy",
   StatisticsBuildingsSearchText: "Type a building name to search",
   StatisticsEmpire: "Impérium",
   StatisticsExploration: "Průzkum",
   StatisticsOffice: "Statistický úřad",
   StatisticsOfficeDesc: "Uveďte statistiky svého impéria. Generování průzkumníků pro průzkum mapy",
   StatisticsResources: "Zdroje",
   StatisticsResourcesDeficit: "Deficit",
   StatisticsResourcesDeficitDesc: "Produkce: %{output} - Spotřeba: %{input}",
   StatisticsResourcesRunOut: "Vyčerpáno",
   StatisticsResourcesSearchText: "Type a resource name to search",
   StatisticsScience: "Věda",
   StatisticsScienceFromBuildings: "Věda z budov",
   StatisticsScienceFromWorkers: "Věda od pracovníků",
   StatisticsScienceProduction: "Vědecká produkce",
   StatisticsStalledTransportation: "Zastavená doprava",
   StatisticsTotalTransportation: "Doprava celkem",
   StatisticsTransportation: "Doprava",
   StatisticsTransportationPercentage: "Procento pracovníků v dopravě",
   StatueOfLiberty: "Socha Svobody",
   StatueOfLibertyDesc: "Všechny sousední budovy získají +N násobitel produkce, skladovací kapacity a kapacity dělníků. N = počet sousedních budov stejného typu.",
   StatueOfZeus: "Diova socha",
   StatueOfZeusDesc: "Vyvolá náhodná naleziště, která byla odhalena na sousedních prázdných destičkách. Všechny sousední budovy I. úrovně získají násobek +5 k produkci a skladování.",
   SteamAchievement: "Steam Achievement",
   SteamAchievementDetails: "Zobrazit Steam Achievement",
   SteamEngine: "Parní stroj",
   Steamworks: "Parní huť",
   Steel: "Ocel",
   SteelMill: "Ocelárna",
   StephenHawking: "Stephen Hawking",
   Stock: "Akcie",
   StockExchange: "Burza cenných papírů",
   StockMarket: "Burza cenných papírů",
   StockpileDesc: "Tato budova bude přepravovat %{capacity}x vstupních surovin za výrobní cyklus, dokud nebude dosaženo maxima.",
   StockpileMax: "Maximální zásoba",
   StockpileMaxDesc: "Tato budova přestane přepravovat suroviny, jakmile jich bude dostatek na %{cycle} výrobních cyklů.",
   StockpileMaxUnlimited: "Neomezené",
   StockpileMaxUnlimitedDesc: "Tato budova nikdy nepřestane přepravovat suroviny, pouze dokud se zásoby nenaplní",
   StockpileSettings: "Vstupní kapacita zásoby",
   Stone: "Kámen",
   StoneAge: "Doba kamenná",
   Stonehenge: "Stonehenge",
   StonehengeDesc: "Všechny budovy, které spotřebovávají nebo produkují kámen, získávají +1 produkční násobek.",
   StoneQuarry: "Kamenolom",
   StoneTool: "Kamenné nářadí",
   StoneTools: "Kamenné nástroje",
   Storage: "Skladování",
   StorageBaseCapacity: "Základní kapacita",
   StorageMultiplier: "Multiplikátor skladování",
   StorageUsed: "Použité úložiště",
   StPetersBasilica: "Bazilika svatého Petra",
   StPetersBasilicaDescV2: "All churches get +5 Storage Multiplier. Generate science based on faith production of all churches",
   Submarine: "Ponorka",
   SubmarineYard: "Submarine Yard",
   SuleimanI: "Suleiman I",
   SummerPalace: "Letní palác",
   SummerPalaceDesc: "Všechny sousední budovy, které spotřebovávají nebo vyrábějí střelný prach, jsou osvobozeny od -1 štěstí. Všechny budovy, které spotřebovávají nebo vyrábějí střelný prach, získávají +1 k produkci, skladování a kapacitě dělníků.",
   Supercomputer: "Superpočítač",
   SupercomputerLab: "Laboratoř na Superpočítače",
   SupporterPackRequired: "Supporter Pack Required",
   SupporterThankYou: "CivIdle is kept afloat thanks to the generousity of the following supporter pack owners",
   SwissBank: "Swiss Bank",
   SwissBankDescV2: "Convert a chosen resource from adjacent warehouses to Koti (10 million in Sanskrit) - a tradeable resource that is worth 10M value. Each level of Swiss Bank add 1 Koti conversion per cycle, which is affected by Production Multiplier. Swiss Bank can store unlimited amount of Koti",
   Sword: "Meč",
   SwordForge: "Kovárna mečů",
   SydneyOperaHouse: "Opera v Sydney",
   SydneyOperaHouseDescV2: "Sydney Opera House",
   SyncToANewDevice: "Sync To A New Device",
   Synthetics: "Syntetika",
   TajMahal: "Tádž Mahal",
   TajMahalDescV2: "A great person of Classical Age and a great person of Middle Age are born. +5 Builder Capacity Multiplier when upgrading buildings over Level 20",
   TangOfShang: "Tang ze Shang",
   TangOfShangDesc: "+%{value} Věda od nečinných dělníků",
   Tank: "Tank",
   TankFactory: "Továrna na tanky",
   TechAge: "Věk",
   TechGlobalMultiplier: "Zvýšení",
   TechHasBeenUnlocked: "%{tech} bylo odemčeno",
   TechProductionPriority: "Odemknout prioritu budov - umožňuje nastavit prioritu výroby pro každou budovu.",
   TechResourceTransportPreference: "Odemknout předvolbu dopravy budovy - umožňuje nastavit, jakým způsobem bude budova dopravovat zdroje potřebné pro její výrobu.",
   TechResourceTransportPreferenceAmount: "Množství",
   TechResourceTransportPreferenceAmountTooltip: "Tato budova bude preferovat přepravu surovin z budov, které mají větší množství ve skladu",
   TechResourceTransportPreferenceDefault: "Výchozí",
   TechResourceTransportPreferenceDefaultTooltip: "Nepřepisuje preference přepravy pro tuto surovinu, místo toho použije preference přepravy budovy",
   TechResourceTransportPreferenceDistance: "Vzdálenost",
   TechResourceTransportPreferenceDistanceTooltip: "Tato budova bude preferovat přepravu surovin z budov, které jsou blíže ve vzdálenosti",
   TechResourceTransportPreferenceOverrideTooltip: "Tento zdroj má nadefinovanou preferenci přepravy: %{mode}",
   TechResourceTransportPreferenceStorage: "Skladování",
   TechResourceTransportPreferenceStorageTooltip: "Tato budova bude preferovat přepravu zdrojů z budov, které mají větší procento využitého skladu",
   TechStockpileMode: "Odemknout režim zásob - umožňuje nastavit zásoby pro každou budovu.",
   Teleport: "Teleport",
   TeleportDescHTML: "A teleport is generated <b>every %{time} seconds</b>. A teleport can be used to <b>move a building (wonders excluded)</b> once",
   Television: "Televize",
   TempleOfArtemis: "Artemidin chrám",
   TempleOfArtemisDesc: "Všechny kovárny mečů a zbrojnice získají po dokončení +5 úrovní. Všechny kovárny mečů a zbrojnice získají +1 násobek produkce, násobek kapacity dělníků a násobek skladování.",
   TempleOfHeaven: "Chrám nebes",
   TempleOfHeavenDesc: "Všechny budovy, které jsou na úrovni 10 nebo vyšší, získají násobitel +1 Kapacita dělníků.",
   TempleOfPtah: "Chrám Ptah",
   TerracottaArmy: "Terakotová armáda",
   TerracottaArmyDesc: "Všechny tábory pro těžbu železa získají +1 násobek produkce, násobek kapacity dělníků a násobek skladování. Železné kovárny získají +1 násobitel produkce za každý sousední tábor těžby železa.",
   Thanksgiving: "Thanksgiving: Wall Street provides double the boost to buildings and applies to Mutual Fund, Hedge Fund and Bitcoin Miner. Research Funds get +5 Production Multiplier",
   Theater: "Divadlo",
   Theme: "Téma",
   ThemeColor: "Barva motivu",
   ThemeColorResearchBackground: "Pozadí výzkumu",
   ThemeColorReset: "Obnovení výchozího nastavení",
   ThemeColorResetBuildingColors: "Obnovení barev budov",
   ThemeColorResetResourceColors: "Obnovit barvy zdrojů",
   ThemeInactiveBuildingAlpha: "Alfa neaktivní budovy",
   ThemePremiumTile: "This tile is only available for Supporter Pack owners",
   ThemeResearchHighlightColor: "Barva zvýraznění výzkumu",
   ThemeResearchLockedColor: "Barva uzamčeného výzkumu",
   ThemeResearchUnlockedColor: "Barva odemčeného výzkumu",
   ThemeTransportIndicatorAlpha: "Alfa indikátoru dopravy",
   Theocracy: "Teokracie",
   TheoreticalData: "Theoretical Data",
   ThePentagon: "Pentagon",
   ThePentagonDesc: "After constructed, generate teleports that can be used to move buildings. All buildings within 2 tile range get +1 Production, Worker Capacity and Storage Multiplier",
   TheWhiteHouse: "Bílý Dům",
   ThomasEdison: "Thomas Edison",
   ThomasGresham: "Thomas Gresham",
   Tile: "Políčko",
   TileBonusRefreshIn: "Tile bonus will refresh in <b>%{time}</b>",
   TimBernersLee: "Tim Berners-Lee",
   TimeWarp: "Časová deformace",
   TimeWarpWarning: "Zrychlení na vyšší rychlost, než jakou zvládne váš počítač, může vést ke ztrátě dat: POUŽÍVÁNÍ NA VLASTNÍ NEBEZPEČÍ",
   ToggleWonderEffect: "Přepnout efekt divu světa",
   Tool: "Nářadí",
   TopkapiPalace: "Topkapı Palace",
   TopkapiPalaceDesc: "All buildings within 2 tile range get +X Storage Multiplier. X = 50% of its Production Multiplier (excluding Dynamic)",
   TotalEmpireValue: "Celková hodnota říše",
   TotalEmpireValuePerCycle: "Celková hodnota říše za cyklus",
   TotalEmpireValuePerCyclePerGreatPeopleLevel: "Total Empire Value Per Cycle Per Great People Level",
   TotalEmpireValuePerWallSecond: "Total Empire Value Wall Second",
   TotalEmpireValuePerWallSecondPerGreatPeopleLevel: "Total Empire Value Per Wall Second Per Great People Level",
   TotalGameTimeThisRun: "Total Game Time This Run",
   TotalScienceRequired: "Total Science Required",
   TotalStorage: "Skladování celkem",
   TotalWallTimeThisRun: "Total Wall Time This Run",
   TotalWallTimeThisRunTooltip: "Wall time (aka. elapsed real time) measures the actual time taken for this run. The differs from the game time in that Time Warp in Petra and Offline Production does not affect wall time but it does affect game time",
   TotalWorkers: "Celkem pracovníků",
   TowerBridge: "Tower Bridge",
   TowerBridgeDesc: "After constructed, a great person from unlocked ages is born every 3600 cycles (1h game time)",
   TowerOfBabel: "Tower of Babel",
   TowerOfBabelDesc: "Provides +2 Production Multiplier to all buildings that has at least one working building located adjacent to the wonder",
   TradeFillSound: "'Trade Filled' Sound",
   TradeValue: "Trade Value",
   TraditionCommerce: "Obchod",
   TraditionCultivation: "Cultivation",
   TraditionDescHTML: "Choose from <b>Cultivation, Commerce, Expansion and Honor</b> as your empire tradition. You <b>cannot switch tradition</b> after it is chosen. You can unlock more boost within each tradition",
   TraditionExpansion: "Expanze",
   TraditionHonor: "Čest",
   Train: "Vlak",
   TranslationPercentage: "%{language} je v překladu %{percentage}. Pomozte vylepšit tento překlad na GitHubu",
   TranslatorCredit: "MikeCZ",
   Translators: "Překladatelé",
   TransportAllocatedCapacityTooltip: "Kapacita stavitele přidělená na přepravu této suroviny",
   TransportationWorkers: "Transportation Workers",
   TransportCapacity: "Přepravní kapacita",
   TransportCapacityMultiplier: "Multiplikátor přepravní kapacity",
   TransportManualControlTooltip: "Přepraví tuto surovinu ke stavbě/vylepšení",
   TransportPlanCache: "Mezipaměť plánu přepravy",
   TransportPlanCacheDescHTML:
      "Every cycle, each building calculates the best transport plan based on its settings - this process requires high CPU power. Enabling this will attempt to cache the result of the transport plan if it is still valid and therefore reduce CPU usage and frame rate drop. <b>Experimental Feature</b>",
   TribuneUpgradeDescGreatPeopleWarning: "Váš současný běh má osobnosti. Měli byste <b>se nejprve znovu narodit</b>. Povýšení na hodnost kvestora vynuluje váš aktuální běh.",
   TribuneUpgradeDescGreatPeopleWarningTitle: "Please Rebirth First",
   TribuneUpgradeDescV4:
      "You can play the full game as Tribune if you do not plan to participate in the <b>optional</b> online features. To acquire unrestricted access to the online features, you will need to upgrade to Quaestor. <b>This is an anti-bot measure to keep the game free for everyone.</b> However, <b>when upgrading to Quaestor</b> you can carry over great people: <ul><li>Up to Level <b>3</b> for Bronze, Iron and Classical Age</li><li>Up to Level <b>2</b> for Middle Age, Renaissance and Industrial Age</li><li>Up to Level <b>1</b> for World Wars, Cold War and Information Age</li></ul>Great People Shards above the level and <b>Age Wisdom</b> levels <b>cannot</b> be carried over",
   TurnOffFullBuildings: "Turn Off All %{building} With Full Storage",
   TurnOnTimeWarpDesc: "Stojí %{speed} warpů za každou sekundu a zrychlí vaši říši na %{speed}x rychlost.",
   Tutorial: "Výukový program",
   TutorialPlayerFlag: "Zvolte si vlajku svého hráče",
   TutorialPlayerHandle: "Vyberte si svou hráčskou rukojeť",
   TV: "TV",
   TVStation: "TV Stanice",
   UnclaimedGreatPersonPermanent: "Máte nevyzvednuté <b>Permanentní osobnosti</b>, klikněte zde pro uplatnění nároku.",
   UnclaimedGreatPersonThisRun: "V tomto běhu jste si nevyzvedli <b>Velcí lidé</b>, klikněte zde pro vyzvednutí.",
   UnexploredTile: "Neprobádané dlaždice",
   UNGeneralAssemblyCurrent: "Current UN General Assembly #%{id}",
   UNGeneralAssemblyMultipliers: "<b>+%{count}</b> Production, Worker Capacity and Storage Multpliers for <b>%{buildings}</b>",
   UNGeneralAssemblyNext: "Upcoming UN General Assembly #%{id}",
   UNGeneralAssemblyVoteEndIn: "You can change your vote any time before the voting ends in <b>%{time}</b>",
   UniqueBuildings: "Unikátní budovy",
   UniqueTechMultipliers: "Unique Tech Multipliers",
   UnitedNations: "Organizace spojených národů (OSN)",
   UnitedNationsDesc: "All Tier IV and V and VI buildings get +1 Production, Worker Capacity and Storage Multiplier. Participate in UN General Assembly and vote for an additional boost each week",
   University: "Univerzita",
   UnlockableResearch: "Odemykatelný výzkum",
   UnlockBuilding: "Odemknout",
   UnlockTechProgress: "Pokrok",
   UnlockXHTML: "Odemknout <b>%{name}</b>",
   Upgrade: "Vylepšit",
   UpgradeBuilding: "Vylepšení",
   UpgradeBuildingNotProducingDescV2: "This building is being upgraded - <b>production will halt until upgrade is complete</b>",
   UpgradeTo: "Vylepšit na Level %{level}",
   Uranium: "Uran",
   UraniumEnrichmentPlant: "Závod na obohacování uranu",
   UraniumMine: "Uranový důl",
   Urbanization: "Urbanizace",
   UserAgent: "Uživatelský agent: %{driver}",
   View: "Zobrazit",
   ViewMenu: "Zobrazit",
   ViewTechnology: "Zobrazit",
   Vineyard: "Vinice",
   VirtualReality: "Virtuální realita",
   Voltaire: "Voltaire",
   WallOfBabylon: "Wall of Babylon",
   WallOfBabylonDesc: "All buildings get +N Storage Multiplier. N = number of unlocked ages / 2",
   WallStreet: "Wall Street",
   WallStreetDesc: "All buildings that produce coin, banknote, bond, stock and forex within 2 tile range get +N production multiplier. N = Random value between 1 to 5 which is different per building and changes with every market refresh. Double the effect of John D. Rockefeller",
   WaltDisney: "Walt Disney",
   Warehouse: "Skladiště",
   WarehouseAutopilotSettings: "Nastavení autopilota",
   WarehouseAutopilotSettingsEnable: "Povolení autopilota",
   WarehouseAutopilotSettingsRespectCapSetting: "Požadavek na Skladování < Limit",
   WarehouseAutopilotSettingsRespectCapSettingTooltip: "Autopilot bude přepravovat pouze suroviny, jejichž množství v úložišti je nižší než limit.",
   WarehouseDesc: "Přeprava specifických zdrojů a další skladování",
   WarehouseExtension: "Unlock warehouse caravansary extension mode. Allow warehouses adjacent to caravansaries to be included in player trading",
   WarehouseSettingsAutopilotDesc: "Tento sklad bude využívat svou nečinnou kapacitu k přepravě surovin z budov, které mají plný sklad. Aktuální volná kapacita: %{capacity}",
   WarehouseUpgrade: "Odemkněte režim autopilota skladu. Volná přeprava mezi skladem a přilehlými budovami.",
   WarehouseUpgradeDesc: "Volná přeprava mezi tímto skladem a jeho sousedními dlaždicemi.",
   Warp: "Deformace času",
   WarpSpeed: "Rychlost deformace času",
   Water: "Voda",
   WellStockedTooltip: "Well-stocked buildings are buildings that have enough resources for its production, which include buildings that are producing, that have full storage or not producing due to lack of workers",
   WernherVonBraun: "Wernher von Braun",
   Westminster: "Westminster",
   Wheat: "Pšenice",
   WheatFarm: "Pšeničná farma",
   WildCardGreatPersonDescV2: "When consumed, become any great person of the same age",
   WilliamShakespeare: "William Shakespeare",
   Wine: "Víno",
   Winery: "Vinařství",
   WishlistSpaceshipIdle: "Wishlist Spaceship Idle",
   Wonder: "Zázrak",
   WonderBuilderCapacityDescHTML: "<b>Kapacita stavitele</b> při stavbě divů je ovlivněna <b>věkem</b> a <b>technologií</b>, která div odemyká.",
   WondersBuilt: "Postavené divy světa",
   WondersUnlocked: "Odemčené divy světa",
   WonderUpgradeLevel: "Wonder Level",
   Wood: "Dřevo",
   Worker: "Pracovník",
   WorkerCapacityMultiplier: "Multiplikátor kapacity pracovníků",
   WorkerHappinessPercentage: "Multiplikátor spokenosti",
   WorkerMultiplier: "Kapacita pracovníků",
   WorkerPercentagePerHappiness: "%{value}% Multiplikátor pro každou spokojenost",
   Workers: "Pracovníci",
   WorkersAvailableAfterHappinessMultiplier: "Pracovníci po multiplikátor spokojenosti",
   WorkersAvailableBeforeHappinessMultiplier: "Pracovníci před multiplikátorem spokojenosti",
   WorkersBusy: "Zaneprázdnění pracovníci",
   WorkerScienceProduction: "Pracovník Věda Produkce",
   WorkersRequiredAfterMultiplier: "Požadovaní pracovníci",
   WorkersRequiredBeforeMultiplier: "Požadovaná kapacita pracovníků",
   WorkersRequiredForProductionMultiplier: "Výrobní kapacita na pracovníka",
   WorkersRequiredForTransportationMultiplier: "Přepravní kapacita na pracovníka",
   WorkersRequiredInput: "Doprava",
   WorkersRequiredOutput: "Výroba",
   WorldWarAge: "Světové války",
   WorldWideWeb: "World Wide Web",
   WritersGuild: "Cech spisovatelů",
   Writing: "Psaní",
   WuZetian: "Císařovna Wu Zetian",
   WuZetianDesc: "+%{value} Násobitel přepravní kapacity",
   Xuanzang: "Xuanzang",
   YangtzeRiver: "Řeka Jang-c’-ťiang",
   YangtzeRiverDesc: "Všechny budovy, které spotřebovávají vodu, získají násobitel +1 k produkci, kapacitě pracovníků a skladování. Dvojnásobný účinek Zheng He (Velká osobnost). Každá úroveň stálé císařovny Wu Zetian (Velká osoba) přináší všem budovám násobitel +1 k násobiteli skladování.",
   YearOfTheSnake: "Year of the Snake",
   YearOfTheSnakeDesc:
      "After completed, when entering a new age, instead of getting one great person of each unlocked age, get the same amount of great people in the current age. All buildings within 2-tile range get +1 Production Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Production Multiplier to buildings within 2-tile range. This wonder can only be constructed during the lunar new year period (1.20 ~ 2.10)",
   YellowCraneTower: "Žlutá jeřábová věž",
   YellowCraneTowerDesc: "+1 volba při výběru osobností. Všechny budovy v dosahu 1 dlaždice získají +1 násobek produkce, kapacity pracovníků a skladů. Při stavbě vedle řeky Jang-c'-ťiang se dosah zvyšuje na 2 dlaždice.",
   YuriGagarin: "Yuri Gagarin",
   ZagrosMountains: "Zagros Mountains",
   ZagrosMountainsDesc: "All adjacent buildings that have less than 5 Production Multiplier get +2 Production Multiplier. Double the effect of Nebuchadnezzar II (Great Person)",
   ZahaHadid: "Zaha Hadid",
   ZahaHadidDesc: "+%{value} Builder Capacity Multiplier",
   Zenobia: "Zenobia",
   ZenobiaDesc: "+%{value}h Petra Sklad deformace času",
   ZhengHe: "Zheng He",
   ZigguratOfUr: "Ziggurat of Ur",
   ZigguratOfUrDescV2: "Every 10 happiness (capped) provides +1 Production Multiplier to all buildings that do not produce workers and are unlocked in previous ages (max = number of unlocked ages / 2). Wonders (incl. Natural) no longer provide +1 Happiness. The effect can be turned off",
   Zoroaster: "Zoroaster",
   Zugspitze: "Zugspitze",
   ZugspitzeDesc: "For each unlocked age, get one point that can be used to provide one extra level to any Great Person that is born from this run",
};
