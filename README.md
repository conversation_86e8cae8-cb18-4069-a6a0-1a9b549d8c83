# CivIdle

CivIdle is an idle/incremental game that allows you to lead your own civilization through thousands of years from ancient times to the modern era. Expand your territory, explore a vast tech tree, build various wonders, and trade with global players: empire must grow and numbers must go up!

- [Play on Steam](https://store.steampowered.com/app/2181940/CivIdle/)

# Get Involved

## Localization

All localizations are contribute by the community. If you'd like to help, visit [here for more info](https://github.com/fishpondstudio/CivIdle/tree/main/shared/languages).

## Build

Prerequisites:
- Install Node.JS (18.x is recommended)
- Install [VSCode](https://code.visualstudio.com/download) and [BiomeJS](https://biomejs.dev/reference/vscode/) extension
- Install [pnpm](https://pnpm.io/installation)

Setting up dependencies:
- Clone this repository and run `pnpm install` in the terminal

Running the Project:
- Run 'pnpm run dev' in the terminal
- Browse to 'http://localhost:3000/' 

# License

- Game's source code is licensed under **GNU General Public License v3.0**
- Third party libraries are licensed under their corresponding licenses
- Game's artworks and assets are included in this repository for development convenience. However, due to the complications of the original licenses, please **do not redistribute them**