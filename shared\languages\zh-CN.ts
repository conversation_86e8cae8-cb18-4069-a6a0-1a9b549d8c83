export const ZH_CN = {
   About: "关于放置文明",
   AbuSimbel: "阿布辛贝神庙",
   AbuSimbelDesc: "拉美西斯二世效果翻倍（此生伟人与永恒伟人的效果翻倍，时代智慧除外）。相邻阿布辛贝神庙的奇观额外获得 +1 幸福感",
   AccountActiveTrade: "可留存订单",
   AccountChatBadge: "聊天徽章",
   AccountCustomColor: "自定义颜色",
   AccountCustomColorDefault: "默认",
   AccountGreatPeopleLevelRequirement: "需求生效永恒伟人等级",
   AccountLevel: "账号等级",
   AccountLevelAedile: "市政官",
   AccountLevelConsul: "执政官",
   AccountLevelMod: "主持人",
   AccountLevelPlayTime: "活跃在线游玩时间 > %{requiredTime} （你的游玩时间为 %{actualTime}）",
   AccountLevelPraetor: "裁判官",
   AccountLevelQuaestor: "财务官",
   AccountLevelSupporterPack: "拥有支持者包",
   AccountLevelTribune: "保民官",
   AccountLevelUpgradeConditionAnyHTML: "若欲升级帐户，只需满足<b>下列</b>标准之一：",
   AccountPlayTimeRequirement: "需求在线游玩时间",
   AccountRankUp: "升级账号等级",
   AccountRankUpDesc: "你的所有进程将被保留",
   AccountRankUpTip: "祝贺！你的账号达到更高等级要求——点此升级！",
   AccountSupporter: "支持者",
   AccountTradePriceRange: "贸易价格范围",
   AccountTradeTileReservationTime: "贸易地块留存时间",
   AccountTradeTileReservationTimeDesc: "你的玩家贸易地块保留的时间从你上次在线之后开始计时。在保留期间结束后，其他玩家可以获取你的宣称地块。",
   AccountTradeValuePerMinute: "每分钟贸易值",
   AccountTypeShowDetails: "展示账号细节",
   AccountUpgradeButton: "升至财务官等级",
   AccountUpgradeConfirm: "账号升级",
   AccountUpgradeConfirmDescV2: "升级帐户将会<b>重置当前帝国</b>并将保留规则允许内的永恒伟人等级。这个操作<b>不能</b>撤回，请确认是否继续?",
   Acknowledge: "收到",
   Acropolis: "雅典卫城",
   ActorsGuild: "演员协会",
   AdaLovelace: "阿达·洛芙莱斯",
   AdamSmith: "亚当·斯密",
   AdjustBuildingCapacity: "生产能力",
   AdvisorElectricityContent:
      "产出电能的建筑为你提供了两个新系统。首先，发电厂相邻地块的闪电标识表示“电能”。一些建筑（如世界大战的解锁建筑广播站）在具体的科技与建筑界面中，有“需要电能”的提示。<b>这意味着它们必须建在含闪电标识的地块上，才能正常起效。</b>需要且已被提供电能的建筑，也能将电能扩散至其相邻地块，所以只要至少有一座需要电能的建筑与产出电能的建筑相邻，你就可以使需要电能的建筑相互提供电能。<br><br>另一个系统“电气化”可被应用于地图上<b>任何地块的任何建筑</b>，前提是它不产出科学也不生产劳动者。这将使用产出电能的建筑产出的电能，从而增加建筑的消耗与产出。越高的电气化等级，需求的电能量会越来越多。相较于其它建筑，“需要电能”的建筑其电气化效率更高。",
   AdvisorElectricityTitle: "电能与电气化",
   AdvisorGreatPeopleContent:
      "每当你进入一个科技新时代，你将能选择一位当前时代伟人，之前每个时代也都能再选择一位。这些伟人带来全局加成，诸如增加生产、科学、幸福感以及许多其它方面。<br><br>这些加成在重生前一直保有。当你选择重生后，所有这些伟人将成为永恒伟人，他们的加成也将永久存在。<br><br>在一轮中选择一位相同的伟人，将叠加加成。若你重生并拥有重复伟人，重复项将被存储并可被用来升级永恒伟人。这项功能在你总部建筑内的<b>管理永恒伟人</b>界面中使用。",
   AdvisorGreatPeopleTitle: "伟人",
   AdvisorHappinessContent:
      "幸福感是放置文明中限制扩张的核心机制。你可以通过解锁新科技、进入新时代、建造奇观、获取提供幸福感的伟人以及你在学习中发现的一些其它方式来获得幸福感。<b>每座新建筑消耗 1 幸福感。</b>超过/低于 0 幸福感，每点幸福感将给你的劳动者总数带来 2% 的加成或减益（生效幸福感区间为 [ -50 , 50 ] ）。你可以在<b>总部建筑的幸福感部分</b>查看幸福感的详细分类。",
   AdvisorHappinessTitle: "维持劳动者幸福",
   AdvisorOkay: "明白了，谢谢！",
   AdvisorScienceContent:
      "你的忙碌劳动者产出科学，这将允许你解锁新的科技以及推动你的文明发展。你有许多方式进入研究界面，比如点击科学计量表（资源栏）、查看你的总部建筑中你的可解锁科技或者使用“视图”菜单，这些都将展开以科技树为主体的研究界面。研究界面为你展示了所有科技，以及每项科技解锁需求的科学等。如果你有足够的科学用来学习一项新科技，只需单击目标科技再单击侧边栏的“解锁”即可。<b>每一新阶与时代之科技需求的科学不断增加，但你也将解锁新的、更好的方式以获得科学。</b>",
   AdvisorScienceTitle: "科学发现！",
   AdvisorSkipAllTutorials: "跳过所有教程",
   AdvisorStorageContent:
      "尽管建筑有许多存储空间，但仍会填满，若长时间闲置尤为如此。<b>建筑存满，不再生产。</b>这并不总是问题，毕竟建筑存储已满意味着拥有大量库存，只不过通常而言，保持持续生产更好。<br><br>通过仓库是一种调节方式。当你建造完成一座仓库，你将获得一份当前已发现的所有产物的名单，你可以设定仓库调用任意数量的任何产出，前提是所有产出的总量在仓库的调用范围内，而这一范围基于仓库等级与存储乘数。<br><br>设定一座仓库的简单方法，那就是勾选你想运入仓库的每种产出，然后使用“在选定对象中重新分配”来均分运输量与限额。如果你希望建筑也能从仓库调用资源，那也请确保打开“低于限额仍输出”功能。",
   AdvisorStorageTitle: "存储与仓库",
   AdvisorTraditionContent:
      "一些奇观（恰高·占比尔、卢克索神庙、大本钟）提供一组新的选项，使你可以定制你的重生之路。每座奇观允许你进行一次四选一，以此来选择文明的传统、宗教以及意识形态。<br><br>一旦你选择了其中一项，在重生前都将不能更改，但你可以在未来的重生后选择其它选项。选定后，每座奇观都将能在提供必要资源后进行多次升级。每阶的加成是累计的，所以 1 阶 X 获得 +1 生产乘数以及 2 阶 X 获得 +1 生产乘数意味着在 2 阶对于 X 你将有总计 +2 生产乘数。",
   AdvisorTraditionTitle: "具有选择与升级的奇观",
   AdvisorWonderContent:
      "奇观是特殊建筑，它们可以提供全局效果，这将在你的游戏中产生深远影响。除了它们列出的功能，所有奇观还都能提供 +1 幸福感。你需要深思熟虑，因为<b>建造奇观需要大量资源，并且其建造者能力也高于正常水平。</b>这意味着它们可以轻易耗尽你库存中的需求资源，使得你的其它建筑缺少原料。<b>你可以自由设定每种资源是否运输</b>，这使你可以在你的库存资源能确保一切运转的状态下建造奇观。",
   AdvisorWonderTitle: "世界奇观",
   AdvisorWorkerContent:
      "建筑每次生产、运输资源都需要劳动者。如果你的可获劳动者不够，一些建筑将会暂停。对此而言，最显著的解决方法就是增加你的劳动者总数，通过建造、升级生产劳动者的建筑（小棚屋/房屋/公寓/住宅楼）。<br><br><b>但请注意，升级中的建筑是关闭状态，并且不提供任何资源，这也包括劳动者，所以也许你更偏向同时只升级一座住宅类建筑。</b>对于游戏早期，一个较好的目标是保持 70% 的忙碌劳动者。如果高于 70% 的忙碌劳动者占比，升级/建造住宅类建筑；如果低于 70% 的忙碌劳动者占比，扩大生产。",
   AdvisorWorkerTitle: "劳动者管理",
   Aeschylus: "埃斯库罗斯",
   Agamemnon: "阿伽门农",
   AgeWisdom: "时代智慧",
   AgeWisdomDescHTML: "每一等级的时代智慧将提供<b>同等级</b>、同时代且具备资质的永恒伟人——它可使用具备资质的永恒伟人之碎片升级",
   AgeWisdomGreatPeopleShardsNeeded: "你需要额外 %{amount} 块伟人碎片，才能进行下一次时代智慧升级",
   AgeWisdomGreatPeopleShardsSatisfied: "你已拥有足够的伟人碎片用于下一次时代智慧升级",
   AgeWisdomNeedMoreGreatPeopleShards: "需要更多伟人碎片",
   AgeWisdomNotEligible: "该伟人无时代智慧资质",
   AgeWisdomSource: "%{age}智慧：%{person}",
   AgeWisdomUpgradeWarningHTMLV3: "当等级为保民官，等级升至财务官将<b>无法继承</b>时代智慧",
   AGreatPersonIsBorn: "一位伟人诞生了",
   AircraftCarrier: "航空母舰",
   AircraftCarrierYard: "航母船坞",
   Airplane: "飞机",
   AirplaneFactory: "飞机工厂",
   Akitu: "阿基图：乌尔大塔庙与幼发拉底河可作用于当前时代已解锁建筑",
   AlanTuring: "艾伦·图灵",
   AlanTuringDesc: "闲置劳动者产出科学 +%{value}",
   AlbertEinstein: "阿尔伯特·爱因斯坦",
   Alcohol: "酒",
   AldersonDisk: "奥尔德森碟",
   AldersonDiskDesc: "+25 幸福感。该奇观可被升级，并且每一次额外升级，提供 +5 幸福感",
   Alloy: "合金",
   Ally: "同盟国",
   Alps: "阿尔卑斯山",
   AlpsDesc: "当此奇观被发现后，所有建筑每 10 等级，获得 +1 乘数（ +1 消费乘数， +1 生产乘数）",
   Aluminum: "铝",
   AluminumSmelter: "铝厂",
   AmeliaEarhart: "阿梅莉亚·埃尔哈特",
   American: "美国",
   AndrewCarnegie: "安德鲁·卡内基",
   AngkorWat: "吴哥窟",
   AngkorWatDesc: "与该奇观相邻的建筑，获得 +1 劳动者能力乘数。该奇观提供 1000 劳动者",
   AntiCheatFailure: "你的账户等级已受限。因为<b>未能通过反作弊</b>检查。若想就此进行申诉，请联系开发者",
   AoiMatsuri: "葵祭：富士山生成双倍扭曲时间",
   Apartment: "公寓",
   Aphrodite: "阿佛洛狄忒女神",
   AphroditeDescV2: "当此奇观被发现后，当升级超过等级 20 的建筑时，超过的每个等级获得 +2 建造者能力乘数。所有已解锁的古典时代永恒伟人，获得 +1 等级的本轮效果",
   ApolloProgram: "阿波罗计划",
   ApolloProgramDesc: "所有火箭工厂，获得 +2 生产、劳动者能力以及存储乘数。对于卫星工厂、航天器工厂以及核导弹发射井，其相邻每座运转的火箭工厂，都会使其获得 +1 生产乘数",
   ApplyToAll: "应用于全部",
   ApplyToAllBuilding: "应用于全部 %{building}",
   ApplyToBuildingInTile: "应用于全部 %{building} （仅周围 %{tile} 地块）",
   ApplyToBuildingsToastHTML: "成功应用于 <b>%{count} %{building}</b>",
   Aqueduct: "引水渠",
   ArcDeTriomphe: "凯旋门",
   ArcDeTriompheDescV2: "每正向 1 幸福（最大值为 50）为所有建筑提供 +1 建造者能力乘数。",
   Archimedes: "阿基米德",
   Architecture: "建筑学",
   Aristophanes: "阿里斯多芬尼斯",
   AristophanesDesc: "幸福感 +%{value} ",
   Aristotle: "亚里士多德",
   Arithmetic: "算术",
   Armor: "盔甲",
   Armory: "军械库",
   ArtificialIntelligence: "人工智能",
   Artillery: "火炮",
   ArtilleryFactory: "火炮工厂",
   AshokaTheGreat: "阿育王",
   Ashurbanipal: "亚述巴尼拔",
   Assembly: "装配",
   Astronomy: "天文学",
   AtomicBomb: "原子弹",
   AtomicFacility: "原子设施",
   AtomicTheory: "原子理论",
   Atomium: "原子塔",
   AtomiumDescV2: "周围 2 地块范围内所有产出科学的建筑，获得 +5 生产乘数。其产生的科学，等同于周围 2 地块范围内的科学总产出量。建造完成时，生成一批科学，生成量等同于已解锁最昂贵科技所需",
   Autocracy: "专制",
   Aviation: "航空",
   Babylonian: "巴比伦",
   BackToCity: "返回城市",
   BackupRecovery: "备份恢复",
   Bakery: "烘焙坊",
   Ballistics: "弹道学",
   Bank: "银行",
   Banking: "银行业",
   BankingAdditionalUpgrade: "所有等级 10 及以上的建筑，获得 +1 存储乘数",
   Banknote: "钞票",
   BaseCapacity: "基础能力（运输量）",
   BaseConsumption: "基础消耗",
   BaseMultiplier: "基础乘数",
   BaseProduction: "基础产出",
   BastilleDay: "国庆日：蓬皮杜文艺中心和凯旋门作用效果为 2 倍。圣米歇尔山产出 2 倍文化。",
   BatchModeTooltip: "目前已选中 %{count} 建筑。将升级所有选中建筑",
   BatchSelectAllSameType: "所有相同类型",
   BatchSelectAnyType1Tile: "相邻 1 格任何类型",
   BatchSelectAnyType2Tile: "相邻 2 格任何类型",
   BatchSelectAnyType3Tile: "相邻 3 格任何类型",
   BatchSelectSameType1Tile: "相邻 1 格相同类型",
   BatchSelectSameType2Tile: "相邻 2 格相同类型",
   BatchSelectSameType3Tile: "相邻 3 格相同类型",
   BatchSelectSameTypeSameLevel: "相同类型相同等级",
   BatchSelectThisBuilding: "仅该建筑",
   BatchStateSelectActive: "运转的",
   BatchStateSelectAll: "所有",
   BatchStateSelectTurnedFullStorage: "存储已满",
   BatchStateSelectTurnedOff: "关闭的",
   BatchUpgrade: "批量升级",
   Battleship: "战列舰",
   BattleshipBuilder: "战列舰船坞",
   BecomeAllyTooltip: "要成为邻国的同盟国，请选择与他们相同的旗帜（地球旗帜除外）",
   BigBen: "大本钟",
   BigBenDesc: "+2 忙碌劳动者科学产出。选取一种帝国意识形态，每条选项都将解锁更多增强",
   Biplane: "双翼飞机",
   BiplaneFactory: "双翼飞机工厂",
   Bitcoin: "比特币",
   BitcoinMiner: "比特币矿工",
   BlackForest: "黑森林",
   BlackForestDesc: "当此奇观被发现时，揭示地图上的所有含原木资源地块并在与该奇观相邻无沉积资源的地块生成原木资源。所有消耗原木或木材的建筑，获得 +5 生产乘数",
   Blacksmith: "工匠铺",
   Blockchain: "区块链",
   BlueMosque: "蓝色清真寺",
   BlueMosqueDesc: "该奇观使所有奇观（自然奇观除外）为相邻的所有建筑提供 +1 生产、劳动者能力以及存储乘数。当圣索菲亚大教堂建造完成在旁边时该奇观双倍作用效果",
   BobHope: "鲍勃·霍普",
   BobHopeDesc: "幸福感 +%{value} ",
   Bond: "债券",
   BondMarket: "债券市场",
   Book: "书",
   BoostCyclesLeft: "剩余加成生效周期",
   BoostDescription: "%{buildings} +%{value} %{multipliers}",
   Borobudur: "婆罗浮屠",
   BorobudurDesc: "翻译时未实装",
   BranCastle: "布朗城堡",
   BranCastleDesc: "翻译时未实装",
   BrandenburgGate: "勃兰登堡门",
   BrandenburgGateDesc: "所有煤矿与油井，获得 +1 生产、存储以及劳动者能力乘数。与炼油厂相邻的每个含石油资源地块，使炼油厂获得 +1 生产、存储以及劳动者能力乘数",
   Bread: "面包",
   Brewery: "酿酒厂",
   Brick: "砖",
   Brickworks: "砖窑",
   BritishMuseum: "大英博物馆",
   BritishMuseumChooseWonder: "选择一个奇观",
   BritishMuseumDesc: "建造完成后，可以变更为其他文明的可建造奇观（只有一次机会）",
   BritishMuseumTransform: "变更",
   Broadway: "百老汇",
   BroadwayCurrentlySelected: "当前选择的",
   BroadwayDesc: "诞生一位当前时代伟人与一位上时代伟人。选择一位伟人使其效果翻倍可随时变更（此生伟人与永恒伟人的效果翻倍，时代智慧除外）",
   BronzeAge: "青铜时代",
   BronzeTech: "青铜",
   BuddhismLevelX: "佛教 %{level}",
   Build: "建造",
   BuilderCapacity: "建造者能力",
   BuildingColor: "建筑色彩",
   BuildingColorMatchBuilding: "与建筑色彩一致",
   BuildingColorMatchBuildingTooltip: "将资源色彩与产出该资源建筑的色彩同步。若多种建筑产出该资源，将从中随机选择一种。",
   BuildingDefaults: "建筑默认值",
   BuildingDefaultsCount: "%{count} 设定与默认值不同",
   BuildingDefaultsRemove: "清除所有建筑设定",
   BuildingEmpireValue: "建筑帝国价值 / 资源帝国价值",
   BuildingMultipliers: "增强",
   BuildingName: "建筑名",
   BuildingNoMultiplier: "%{building} <b>不受任何</b> 乘数（生产、劳动者能力、存储等）的影响",
   BuildingSearchText: "输入建筑或资源名称进行搜索",
   BuildingTier: "阶",
   Cable: "电缆",
   CableFactory: "电缆厂",
   Calendar: "历法",
   CambridgeUniversity: "剑桥大学",
   CambridgeUniversityDesc: "文艺复兴及之后的时代智慧额外获得 +1 等级",
   CambridgeUniversitySource: "剑桥大学 (%{age})",
   Cancel: "取消",
   CancelAllUpgradeDesc: "取消所有 %{building} 升级",
   CancelUpgrade: "取消升级",
   CancelUpgradeDesc: "所有已被运送的资源将被保留在此建筑中。",
   Cannon: "大炮",
   CannonWorkshop: "大炮工坊",
   CannotEarnPermanentGreatPeopleDesc: "因为这是一次试运营，永恒伟人不可被获取。",
   Capitalism: "资本主义",
   Cappadocia: "卡帕多西亚",
   CappadociaDesc: "周围 3 地块范围内所有的建筑，当升级超过等级 30 的建筑时，超过的每个等级获得 +1 生产、劳动者能力以及存储乘数",
   Car: "汽车",
   Caravansary: "商队旅馆",
   CaravansaryDesc: "与其他玩家进行资源贸易，同时提供额外存储空间。",
   Caravel: "轻快帆船",
   CaravelBuilder: "轻快帆船船坞",
   CarFactory: "汽车工厂",
   CarlFriedrichGauss: "卡尔·弗里德里希·高斯",
   CarlFriedrichGaussDesc: "+%{idle} 闲置劳动者科学产出。 +%{busy} 忙碌劳动者科学产出",
   CarlSagan: "卡尔·萨根",
   Census: "劳动者统计",
   CentrePompidou: "蓬皮杜文艺中心",
   CentrePompidouDesc:
      "建造完成后，所有建筑获得 +1 生产乘数和 +2 存储乘数。如果当前的游玩达到信息时代，下一次游玩是不同的文明，那么该奇观将持续存在。对于每次游玩该奇观未记录的文明达到信息时代后，在重生时记录重生前的文明，每拥有一个记录在册的文明额外提供 +1 生产乘数和 +2 存储乘数。该奇观的价值不计入帝国的总价值中，大英博物馆无法转换为该奇观。",
   CentrePompidouWarningHTML: "蓬皮杜文艺中心将消失，如果你重生在 <b>%{civ}</b>",
   CerneAbbasGiant: "塞那阿巴斯巨人像",
   CerneAbbasGiantDesc: "当此奇观被发现后，每建造完成一个奇观时诞生一个当前时代的伟人",
   ChangePlayerHandle: "确认更改",
   ChangePlayerHandleCancel: "取消",
   ChangePlayerHandledDesc: "你的玩家昵称只能包含 5 ~ 16 个字母与数字，并且要求独一无二",
   Chariot: "战车",
   ChariotWorkshop: "战车工坊",
   Charlemagne: "查理曼大帝",
   CharlesDarwin: "查尔斯·达尔文",
   CharlesDarwinDesc: "忙碌劳动者产出科学 +%{value}",
   CharlesMartinHall: "查尔斯·马丁·霍尔",
   CharlesParsons: "查尔斯·帕森斯",
   CharlieChaplin: "查理·卓别林",
   CharlieChaplinDesc: "幸福感 +%{value} ",
   Chat: "聊天",
   ChatChannel: "聊天频道",
   ChatChannelLanguage: "语言",
   ChatHideLatestMessage: "隐藏最新消息内容",
   ChatNoMessage: "没有消息",
   ChatReconnect: "连接已断开，正在重新连接……",
   ChatSend: "发送",
   CheckInAndExit: "登录并退出(Check In And Exit)",
   CheckInCloudSave: "登录存档(Check In Save)",
   CheckOutCloudSave: "登出存档(Check Out Save)",
   Cheese: "奶酪",
   CheeseMaker: "奶酪作坊",
   Chemistry: "化学",
   ChesterWNimitz: "切斯特·威廉·尼米兹",
   ChichenItza: "奇琴伊察",
   ChichenItzaDesc: "与该奇观相邻的建筑，获得 +1 生产、劳动者能力以及存储乘数",
   Chinese: "中华",
   ChoghaZanbil: "恰高·占比尔",
   ChoghaZanbilDescV2: "选取一项帝国传统，每条选项都将解锁更多增强",
   ChooseGreatPersonChoicesLeft: "还存留 %{count} 项选择",
   ChristianityLevelX: "基督教 %{level}",
   Church: "教堂",
   CircusMaximus: "马克西穆斯角斗场",
   CircusMaximusDescV2: "+5 幸福感。所有音乐家协会、作家协会和画家协会获得 +1 生产和存储乘数",
   CityState: "城邦国家",
   CityViewMap: "城市",
   CivGPT: "CivGPT（人工智能）",
   CivIdle: "放置文明",
   CivIdleInfo: "由 Fish Pond 工作室荣誉出品",
   Civilization: "文明",
   CivilService: "公务员制度",
   CivOasis: "Civ绿洲",
   CivTok: "CivTok（抖音）",
   ClaimedGreatPeople: "已迎接的额外伟人",
   ClaimedGreatPeopleTooltip: "你重生时有 %{total} 位额外伟人，其中 %{claimed} 位已迎接",
   ClassicalAge: "古典时代",
   ClearAfterUpdate: "在市场更新后，清除所有交易",
   ClearSelected: "清除选中",
   ClearSelection: "取消选择",
   ClearTransportPlanCache: "清除运输方案缓存",
   Cleopatra: "克利奥帕特拉",
   CloneFactory: "克隆工厂",
   CloneFactoryDesc: "克隆任一资源",
   CloneFactoryInputDescHTML: "克隆工厂进行 <b>%{res}</b> 的克隆，只能从其非克隆产出建筑 <b>%{buildings}</b> 中直接运送母本。",
   CloneLab: "克隆实验室",
   CloneLabDesc: "将任一资源转化为科学",
   CloneLabScienceMultiplierHTML: "对于<b>只适用于产出科学之建筑</b>的生产乘数（如来自原子塔的生产乘数），<b>不适用于</b>克隆实验室。",
   Cloth: "织物",
   CloudComputing: "云计算",
   CloudSaveRefresh: "更新",
   CloudSaveReturnToGame: "返回游戏",
   CNTower: "加拿大国家电视塔",
   CNTowerDesc: "所有电影制片厂、广播站以及电视台，免去 -1 幸福感。所有世界大战与冷战时代的解锁建筑，获得 +N 生产、劳动者能力以及存储乘数。 N 为建筑自身时代与阶之差",
   Coal: "煤",
   CoalMine: "煤矿",
   CoalPowerPlant: "燃煤电厂",
   Coin: "硬币",
   CoinMint: "铸币局",
   ColdWarAge: "冷战",
   CologneCathedral: "科隆大教堂",
   CologneCathedralDesc: "建造完成时，生成一批科学，生成量等同于当前时代最昂贵科技所需。所有产出科学的建筑（不包括克隆实验室），获得 +1 生产乘数。该奇观可被升级，并且每一次额外升级，为所有产出科学的建筑（不包括克隆实验室）提供 +1 生产乘数",
   Colonialism: "殖民主义",
   ColorBlue: "蓝色",
   ColorCinereous: "灰色",
   ColorChocolate: "巧克力",
   ColorCyan: "青色",
   ColorDarkBlue: "深蓝",
   ColorFlamingo: "火烈鸟",
   ColorGlaucous: "灰蓝",
   ColorGreen: "绿色",
   ColorNone: "默认",
   ColorPink: "粉红色",
   ColorPurple: "紫色",
   ColorOrange: "橙色",
   ColorRed: "红色",
   ColorSeaGreen: "海洋绿",
   ColorTurquoise: "宝石绿",
   Colosseum: "斗兽场",
   ColosseumDescV2: "战车工坊免去 -1 幸福感。消耗 10 战车并产出 10 幸福感。每个已解锁时代给予 2 额外幸福感",
   ColossusOfRhodes: "罗德岛巨像",
   ColossusOfRhodesDesc: "每有与该奇观相邻不生产劳动者的建筑（包括奇观），获得 +1 幸福感",
   Combustion: "内燃机",
   Commerce4UpgradeHTMLV2: "解锁时，与该奇观相邻的<b>银行</b>获得一次免费升级至<b>等级 30</b>（仅解锁前相邻建造完成的银行生效）",
   CommerceLevelX: "商业 %{level}",
   Communism: "共产主义",
   CommunismLevel4DescHTML: "诞生一位<b>工业时代</b>伟人与一位<b>世界大战时期</b>伟人",
   CommunismLevel5DescHTML: "诞生一位<b>冷战时期</b>伟人。进入一个新时代时，诞生<b>2 位额外</b>当代伟人",
   CommunismLevelX: "共产主义 等级 %{level}",
   Computer: "计算机",
   ComputerFactory: "计算机工厂",
   ComputerLab: "计算机实验室",
   Concrete: "混凝土",
   ConcretePlant: "混凝土厂",
   Condo: "住宅楼",
   ConfirmDestroyResourceContent: "你正打算销毁 %{amount} %{resource}，这项操作不能撤回",
   ConfirmNo: "取消",
   ConfirmYes: "确认",
   Confucius: "孔子",
   ConfuciusDescV2: "所有劳动者产出科学 +%{value} （忙碌劳动者占比高于 50% 且运输作业劳动者占比低于 50% 时生效）",
   ConnectToADevice: "连接一台设备",
   Conservatism: "保守主义",
   ConservatismLevelX: "保守主义 等级 %{level}",
   Constitution: "宪法",
   Construction: "结构",
   ConstructionBuilderBaseCapacity: "基础能力",
   ConstructionBuilderCapacity: "建造者能力",
   ConstructionBuilderMultiplier: "建造者能力乘数",
   ConstructionBuilderMultiplierFull: "建造者能力乘数",
   ConstructionCost: "建造消耗：%{cost}",
   ConstructionDelivered: "已运送",
   ConstructionPriority: "建造优先级",
   ConstructionProgress: "进程",
   ConstructionResource: "资源",
   Consume: "消耗",
   ConsumeResource: "消耗：%{resource}",
   ConsumptionMultiplier: "消费乘数",
   ContentInDevelopment: "内容开发中",
   ContentInDevelopmentDesc: "此游戏内容仍在开发中，可在将来的游戏更新中获取，敬请期待！",
   Copper: "铜",
   CopperMiningCamp: "铜矿",
   CosimoDeMedici: "科西默·德·美第奇",
   Cotton: "棉花",
   CottonMill: "纺织厂",
   CottonPlantation: "植棉场",
   Counting: "计算",
   Courthouse: "法院",
   CristoRedentor: "救世基督像",
   CristoRedentorDesc: "周围 2 地块范围内的所有建筑，免去 -1 幸福感",
   CrossPlatformAccount: "平台账号",
   CrossPlatformConnect: "连接",
   CrossPlatformSave: "跨平台存档",
   CrossPlatformSaveLastCheckIn: "上次登录(Last Check In)",
   CrossPlatformSaveStatus: "当前状态",
   CrossPlatformSaveStatusCheckedIn: "登录",
   CrossPlatformSaveStatusCheckedOut: "登出于 %{platform}",
   CrossPlatformSaveStatusCheckedOutTooltip: "你的跨平台存档已在其它平台登出。你必须登录所登出平台，才能在本平台登出",
   Cultivation4UpgradeHTML: "诞生一位<b>文艺复兴时期</b>伟人",
   CultivationLevelX: "教化 %{level}",
   Culture: "文化",
   Culus: "登基大典：卡帕多西亚的效果加倍。亚拉拉特山的效果将基于生效永恒伟人等级的平方根，而不是立方根。",
   CurrentLanguage: "简体中文",
   CurrentPlatform: "当前平台",
   CursorBigOldFashioned: "3D （大）",
   CursorOldFashioned: "3D",
   CursorStyle: "光标类型",
   CursorStyleDescHTML: "改变光标类型。<b>需重启游戏以生效</b>",
   CursorSystem: "系统",
   Cycle: "周期",
   CyrusII: "居鲁士二世",
   DairyFarm: "奶牛场",
   DefaultBuildingLevel: "默认新建建筑等级",
   DefaultConstructionPriority: "默认建造优先级",
   DefaultProductionPriority: "默认生产优先级",
   DefaultStockpileMax: "默认最大库存",
   DefaultStockpileSettings: "默认库存输入系数",
   DeficitResources: "赤字资源",
   Democracy: "民主",
   DemolishAllBuilding: "批量拆除 %{building} （周围 %{tile} 地块内）",
   DemolishAllBuildingConfirmContent: "再次确认是否拆除 %{count} 座 %{name}？",
   DemolishAllBuildingConfirmTitle: "确认拆除 %{count} 座建筑吗？",
   DemolishBuilding: "拆除建筑",
   DennisRitchie: "丹尼斯·里奇",
   Deposit: "沉积资源",
   DepositTileCountDesc: "在 %{city} 有 %{count} 地块含有 %{deposit}",
   Dido: "狄多",
   Diplomacy: "外交",
   DistanceInfinity: "无限制",
   DistanceInTiles: "距离（地块）",
   DolmabahcePalace: "多尔玛巴赫切宫",
   Drilling: "钻井",
   DukeOfZhou: "周公旦",
   DuneOfPilat: "彼拉特沙丘",
   DuneOfPilatDesc: "当此奇观被发现后，在每一个时代时,都为上一个时代的时代智慧加成效果翻倍。",
   DynamicMultiplierTooltip: "此为动态乘数——其将不会影响劳动者与存储",
   Dynamite: "炸药",
   DynamiteWorkshop: "炸药工坊",
   DysonSphere: "戴森球",
   DysonSphereDesc: "所有建筑获得 +5 生产乘数。该奇观可被升级，并且每一次额外升级，为所有建筑提供 +1 生产乘数",
   EasterBunny: "复活节兔子",
   EasterBunnyDesc: "建造完成后，重生时将复制 10％ 的额外伟人，并在下轮游玩中建造完成复活节兔子时根据已的解锁时代诞生之前复制的伟人。该奇观仅可在四月建造",
   EastIndiaCompany: "东印度公司",
   EastIndiaCompanyDescV2:
      "该奇观累积您与玩家完成交易的总贸易价值。每消耗 2000 贸易价值获得一个周期, 使所有建筑每与一个运转的商队旅馆相邻获得 +0.5 生产乘数。该奇观可被升级，并且每一次额外升级获得 +0.5 生产乘数。当您完成了另一个玩家的一笔交易请求或您自己的一笔交易请求被完成时，通过交易所获得物品其价值才被计入（税收也被计入）。多次交易延长持续时间，可多重叠加加成",
   Education: "教育学",
   EffectiveGreatPeopleLevel: "生效永恒伟人等级",
   EffectiveGreatPeopleLevelDesc: "生效永恒伟人等级，为所有永恒伟人等级与时代智慧等级之和。它评估的是永恒伟人与时代智慧所提供的有效加成",
   Egyptian: "埃及",
   EiffelTower: "埃菲尔铁塔",
   EiffelTowerDesc: "与该奇观相邻的炼钢厂获得 +N 生产、劳动者能力以及存储乘数。 N 取决于埃菲尔铁塔相邻运转的炼钢厂数量",
   Elbphilharmonie: "易北爱乐厅",
   ElbphilharmonieDesc: "周围 3 地块范围内的所有建筑，被加成的建筑每相邻与被加成是不相同的阶且运转的建筑，被加成的建筑获得 +1 生产乘数",
   Electricity: "电能",
   Electrification: "电气化",
   ElectrificationPowerRequired: "所需功率",
   ElectrificationStatusActive: "运转中",
   ElectrificationStatusDesc: "无论建筑是否需要电能，都存在电气化可能。不同之处在于，需要电能的建筑电气化效率更高",
   ElectrificationStatusNoPowerV2: "电能不足",
   ElectrificationStatusNotActive: "未运转",
   ElectrificationStatusV2: "电气化状态",
   ElectrificationUpgrade: "解锁电气化。允许建筑消耗电能促进生产",
   Electrolysis: "电解作用",
   ElvisPresley: "艾维斯·皮礼士利",
   ElyseePalace: "爱丽舍宫",
   EmailDeveloper: "电邮开发者",
   Embassy: "大使馆",
   EmperorWuOfHan: "汉武帝",
   EmpireValue: "帝国价值",
   EmpireValueByHour: "帝国价值（按小时计算）",
   EmpireValueFromBuilding: "建筑的帝国价值",
   EmpireValueFromBuildingsStat: "来自建筑",
   EmpireValueFromResources: "资源的帝国价值",
   EmpireValueFromResourcesStat: "来自资源",
   EmpireValueIncrease: "帝国价值增速",
   EmptyTilePageBuildLastBuilding: "建造上一类建筑",
   EndConstruction: "取消建造",
   EndConstructionDescHTML: "当你取消建造，所有已被使用的资源<b>将不会返还</b>。",
   Engine: "发动机",
   Engineering: "工程学",
   English: "英国",
   Enlightenment: "启蒙运动",
   Enrichment: "富集",
   EnricoFermi: "恩里科·费米",
   EstimatedTimeLeft: "预计剩余时间",
   EuphratesRiver: "幼发拉底河",
   EuphratesRiverDesc:
      "从事生产（非运输）的忙碌劳动者占比每有 10% ，为所有不生产劳动者且为之前时代的解锁建筑，提供 +1 生产乘数（每个时代最大值如下：石器至铁器为1，古典至中世纪为2,文艺复兴至工业为3，世界大战至冷战为4，信息为5）。当空中花园在其旁建造完成，基于空中花园解锁后的每个时代，使空中花园获得 +1 效果。当此奇观被发现时，与该奇观相邻无沉积资源的地块生成水资源",
   ExpansionLevelX: "扩张 %{level}",
   Exploration: "勘探",
   Explorer: "探险家",
   ExplorerRangeUpgradeDesc: "增加探险家的探索范围至周围 %{range} 地块",
   ExploreThisTile: "派遣探险家",
   ExploreThisTileHTML: "探险家将探索<b>此地块与相邻地块</b>。探险家在 %{name} 中生成。还有 %{count} 位探险家",
   ExtraGreatPeople: "%{count} 额外伟人",
   ExtraGreatPeopleAtReborn: "重生时的额外伟人",
   ExtraTileInfoType: "额外地块信息",
   ExtraTileInfoTypeDesc: "选择每个地块下方显示的信息",
   ExtraTileInfoTypeEmpireValue: "帝国价值",
   ExtraTileInfoTypeNone: "不显示",
   ExtraTileInfoTypeStoragePercentage: "已用存储空间所占百分比",
   Faith: "信仰",
   Farming: "农业",
   FavoriteBuildingAdd: "加入偏好",
   FavoriteBuildingEmptyToast: "你没有任何偏好建筑",
   FavoriteBuildingRemove: "移出偏好",
   FeatureRequireQuaestorOrAbove: "此项功能需要财务官及以上等级",
   Festival: "节日",
   FestivalCycle: "节日可持续周期",
   FestivalTechTooltipV2: "正向幸福感（最大值为 50 ）可同时产出节日点数。每有 %{point} 节日点数，你的帝国可以开启一个周期的节日，节日将授予具有地区特色的独特加成。当前地区的节日——%{desc}",
   FestivalTechV2: "解锁节日——正向幸福感（最大值为 50 ）可同时产出节日点数。每有 %{point} 节日点数，你的帝国可以开启一个周期的节日，节日将授予具有地区特色的独特加成",
   Feudalism: "封建主义",
   Fibonacci: "斐波那契",
   FibonacciDescV2: "+%{idle} 闲置劳动者科学产出。+%{busy} 忙碌劳动者科学产出。永恒伟人斐波那契的升级需求，遵循斐波那契数列序列",
   FighterJet: "战斗机",
   FighterJetPlant: "战斗机工厂",
   FilterByAge: "根据时代筛选",
   FinancialArbitrage: "金融套利",
   FinancialLeverage: "财务杠杆",
   Fire: "火",
   Firearm: "火器",
   FirstTimeGuideNext: "下一步",
   FirstTimeTutorialWelcome: "欢迎来到放置文明",
   FirstTimeTutorialWelcome1HTML:
      "欢迎来到放置文明游戏。在本游戏中，你将运营属于你的帝国：<b>管理生产，解锁科技，与其他玩家开展资源贸易，获得伟人以及建造世界奇观</b>。<br><br>拖动鼠标进行移动。使用滚轮放大或缩小。单击一块空白地块以建造新建筑，单击一座建筑以查看它的详情。<br><br>诸如采石场与伐木场的一些建筑，需要建在资源地块上。开发者建议：将一座小棚屋（它可以提供劳动者）放置于迷雾边——建筑完成建造通常需要一些时间。在建造完成后，它将驱散其周围的迷雾。",
   FirstTimeTutorialWelcome2HTML:
      "建筑可以进行升级——这需要花费资源以及一些时间。当一座建筑处于升级中的状态，<b>它将不再生产</b>。这包括了生产劳动者的建筑，<b>所以永远不要同时升级你的所有建筑！</b><br><br>随着你帝国的发展，你将获得更多科学并以此解锁新的科技。当我们到那儿时，开发者将告诉你更多相关信息，但你可以前往“视图”→“研究”快速查看一下。<br><br>",
   FirstTimeTutorialWelcome3HTML: "现在你知道了所有的游戏基础，你可以开始建造你的帝国了。（简中译者：在“帮助”→“教程”内可进行回看。）但在开发者让你行动之前，你应该<b>为自己选择一个玩家昵称</b>并在游戏内的聊天室中打个招呼。我们有一个非常有帮助的社区（企鹅群907371958）：如果你有疑惑，不要害怕去寻求帮助！",
   Fish: "鱼",
   FishPond: "鱼塘",
   FlorenceNightingale: "弗洛伦斯·南丁格尔",
   FlorenceNightingaleDesc: "幸福感 +%{value} ",
   Flour: "面粉",
   FlourMill: "面粉磨坊",
   FontSizeScale: "字体大小比例",
   FontSizeScaleDescHTML: "更改游戏UI的字体大小比例。<b>若将比例设置大于 1x 可能会破坏某些UI布局</b>",
   ForbiddenCity: "紫禁城",
   ForbiddenCityDesc: "所有造纸作坊、作家协会以及印刷厂，获得 +1 生产、劳动者能力以及存储乘数",
   Forex: "外汇",
   ForexMarket: "外汇市场",
   FrankLloydWright: "弗兰克·劳埃德·赖特",
   FrankLloydWrightDesc: " +%{value} 建造者能力乘数",
   FrankWhittle: "弗兰克·惠特尔",
   FreeThisWeek: "本周免费",
   FreeThisWeekDescHTMLV2: "<b>每周</b>，可以免费游玩一支独特文明。本周免费游玩的文明为——<b>%{city}</b>",
   French: "法国",
   Frigate: "护卫舰",
   FrigateBuilder: "护卫舰船坞",
   Furniture: "家具",
   FurnitureWorkshop: "家具工坊",
   Future: "未来",
   GabrielGarciaMarquez: "加夫列尔·加西亚·马尔克斯",
   GabrielGarciaMarquezDesc: "幸福感 +%{value}",
   GalileoGalilei: "伽利略·伽利莱",
   GalileoGalileiDesc: "+%{value} 闲置劳动者科学产出",
   Galleon: "大帆船",
   GalleonBuilder: "大帆船船坞",
   Gameplay: "游戏设置",
   Garment: "服装",
   GarmentWorkshop: "制衣工坊",
   GasPipeline: "天然气管道",
   GasPowerPlant: "天然气发电厂",
   GatlingGun: "加特林机枪",
   GatlingGunFactory: "机枪工厂",
   Genetics: "遗传学",
   Geography: "地理学",
   GeorgeCMarshall: "乔治·卡特莱特·马歇尔",
   GeorgeWashington: "乔治·华盛顿",
   GeorgiusAgricola: "格奥尔格·阿格里科拉",
   German: "德国",
   Glass: "玻璃",
   Glassworks: "玻璃工坊",
   GlobalBuildingDefault: "全图建筑默认项",
   Globalization: "全球化",
   GoBack: "返回",
   Gold: "金",
   GoldenGateBridge: "金门大桥",
   GoldenGateBridgeDesc: "所有产出电能的建筑，获得 +1 生产乘数。为周围 2 地块范围提供电能",
   GoldenPavilion: "金阁寺",
   GoldenPavilionDesc: "周围 3 地块范围内的所有建筑，被加成的建筑每相邻产出是任一被加成对象消耗所需的建筑，被加成的建筑获得 +1 生产乘数（克隆实验室与克隆工厂除外）",
   GoldMiningCamp: "金矿",
   GordonMoore: "戈登·摩尔",
   GrandBazaar: "大巴扎",
   GrandBazaarDesc: "该奇观可管理你的所有市场，与该奇观相邻的市场拥有不同的交易项。与该奇观相邻的商队旅馆，获得 +5 生产与存储乘数",
   GrandBazaarFilters: "筛选",
   GrandBazaarFilterWarningHTML: "必须先进行筛选，才能显示市场交易项",
   GrandBazaarFilterYouGet: "进项",
   GrandBazaarFilterYouPay: "销项",
   GrandBazaarSeach: "搜索",
   GrandBazaarSearchGet: "获得",
   GrandBazaarSearchPay: "支付",
   GrandBazaarTabActive: "活跃",
   GrandBazaarTabTrades: "交易",
   GrandCanyon: "大峡谷",
   GrandCanyonDesc: "当此奇观被发现后，当前时代的已解锁建筑获得 +2 生产乘数。约翰·皮尔庞特·摩根效果翻倍（此生伟人与永恒伟人的效果翻倍，时代智慧除外）",
   GraphicsDriver: "显卡驱动程序：%{driver}",
   GreatDagonPagoda: "仰光大金寺",
   GreatDagonPagodaDescV2: "所有宝塔免去 -1 幸福感。其产生的科学，基于所有宝塔的信仰总产出",
   GreatMosqueOfSamarra: "萨迈拉大清真寺",
   GreatMosqueOfSamarraDescV2: "+1 建筑视野范围。随机显露 5 个未探明的资源地块，并在其上建造一座开采该资源的 10 等级建筑",
   GreatPeople: "伟人",
   GreatPeopleEffect: "效用",
   GreatPeopleFilter: "输入名字或时代进行伟人筛选",
   GreatPeopleName: "名字",
   GreatPeoplePermanentColumn: "永恒",
   GreatPeoplePermanentShort: "永恒",
   GreatPeoplePickPerRoll: "每次选择的伟人数",
   GreatPeopleThisRun: "此生伟人",
   GreatPeopleThisRunColumn: "此生",
   GreatPeopleThisRunShort: "此生",
   GreatPersonLevelRequired: "所需生效永恒伟人等级",
   GreatPersonLevelRequiredDescV2: "%{city} 文明需要的生效永恒伟人等级为 %{required} 。你当前等级为 %{current}",
   GreatPersonPromotionPromote: "提升",
   GreatPersonThisRunEffectiveLevel: "此轮拥有 %{count} 位 %{person}。新一位 %{person} 将拥有该效果的 1/%{effect} ",
   GreatPersonWildCardBirth: "确定",
   GreatSphinx: "狮身人面像",
   GreatSphinxDesc: "周围 2 地块范围，所有 II 阶及以上建筑，获得 +N 消费、生产乘数（ N 为其相邻相同类型建筑数量）",
   GreatWall: "长城",
   GreatWallDesc: "周围 1 地块范围内的所有建筑，获得 +N 生产、劳动者能力以及存储乘数。 N 为当前科技时代与建筑所属时代之间的差值。若建造完成后与紫禁城相邻，则范围增加至 2 地块",
   GreedyTransport: "重视建造/升级运输",
   GreedyTransportDescHTML: "这将使建筑持续运送资源，即使它已经具有足够资源进行当前升级。这将使建筑<b>更快地</b>进行多等级升级，但最终运送的资源将会<b>供大于求</b>",
   Greek: "希腊",
   GrottaAzzurra: "卡普里岛蓝洞",
   GrottaAzzurraDescV2: "当此奇观被发现时，所有 I 阶建筑，获得 +5 等级和 +1 生产、劳动者能力以及存储乘数",
   Gunpowder: "火药",
   GunpowderMill: "火药厂",
   GuyFawkesNightV2: "盖伊·福克斯之夜：东印度公司提供两倍加成效果。伦敦塔桥获取伟人速度加快 20% ",
   HagiaSophia: "圣索菲亚大教堂",
   HagiaSophiaDescV2: "+5 幸福感。生产能力为 0% 的建筑，免去 -1 幸福感。特殊效果：在游戏加载期间，临时提供额外幸福感以避免生产暂停",
   HallOfFame: "名人堂",
   HallOfSupremeHarmony: "太和殿",
   Hammurabi: "汉谟拉比",
   HangingGarden: "空中花园",
   HangingGardenDesc: "+1 建造者能力乘数。与该奇观相邻的引水渠获得 +1 生产、存储以及劳动者能力乘数",
   Happiness: "幸福感",
   HappinessFromBuilding: "来自建筑（奇观除外）",
   HappinessFromBuildingTypes: "来自具有良好库存的建筑种类",
   HappinessFromHighestTierBuilding: "来自最高阶的工作建筑",
   HappinessFromUnlockedAge: "来自已解锁时代",
   HappinessFromUnlockedTech: "来自已解锁科技",
   HappinessFromWonders: "来自奇观（包括自然奇观）",
   HappinessUncapped: "幸福感（实际值）",
   HarryMarkowitz: "哈里·马科维茨",
   HarunAlRashid: "哈伦·拉希德",
   Hatshepsut: "哈特谢普苏特",
   HatshepsutTemple: "哈特谢普苏特神庙",
   HatshepsutTempleDesc: "当完成建造时，在地图上揭示所有含水资源地块。与小麦农场相邻的每个含水资源地块，使小麦农场获得 +1 生产乘数",
   Headquarter: "总部",
   HedgeFund: "对冲基金",
   HelpMenu: "帮助",
   HenryFord: "亨利·福特",
   Herding: "放牧",
   Herodotus: "希罗多德",
   HighlightBuilding: "高亮 %{building}",
   HighlightMoreBuildings: "高亮更多贸易地块加成",
   HimejiCastle: "姬路城",
   HimejiCastleDesc: "所有轻快帆船船坞、大帆船船坞以及护卫舰船坞，获得 +1 生产、劳动者能力以及存储乘数",
   Hollywood: "好莱坞",
   HollywoodDesc: " +5 幸福感。周围 2 地块范围，所有消耗或产出文化且具有良好库存的建筑，获得 +1 幸福感",
   HolyEmpire: "神圣帝国",
   Homer: "荷马",
   Honor4UpgradeHTML: "<b>郑和</b>效果翻倍（此生伟人与永恒伟人的效果翻倍，时代智慧除外）",
   HonorLevelX: "荣耀 %{level}",
   Horse: "马",
   HorsebackRiding: "马术",
   House: "房屋",
   Housing: "住宅",
   Hut: "小棚屋",
   HydroDam: "水电坝",
   Hydroelectricity: "水力发电",
   HymanGRickover: "海曼·里科弗",
   IdeologyDescHTML: "从<b>自由主义、保守主义、社会主义以及共产主义</b>中选择一种作为你的帝国意识形态。一旦选择，你将<b>不能更换意识形态</b>。每种意识形态，你都能解锁更多增强",
   IMPei: "贝聿铭",
   IMPeiDesc: " +%{value} 建造者能力乘数",
   Imperialism: "帝国主义",
   ImperialPalace: "皇居",
   IndustrialAge: "工业时代",
   InformationAge: "信息时代",
   InputResourceForCloning: "克隆输入资源（无法再次用于克隆）",
   InternationalSpaceStation: "国际空间站",
   InternationalSpaceStationDesc: "所有建筑获得 +5 存储乘数。该奇观可被升级，并且每一次额外升级，为所有建筑提供 +1 存储乘数",
   Internet: "互联网",
   InternetServiceProvider: "互联网服务供应商",
   InverseSelection: "反选",
   Iron: "铁",
   IronAge: "铁器时代",
   Ironclad: "铁甲舰",
   IroncladBuilder: "铁甲舰船坞",
   IronForge: "铁锻铺",
   IronMiningCamp: "铁矿",
   IronTech: "铁",
   IsaacNewton: "艾萨克·牛顿",
   IsaacNewtonDescV2: "所有劳动者产出科学 +%{value} （忙碌劳动者占比高于 50% 且运输作业劳动者占比低于 50% 时生效）",
   IsambardKingdomBrunel: "伊桑巴德·金德姆·布鲁内尔",
   IsidoreOfMiletus: "米利都的伊西多尔",
   IsidoreOfMiletusDesc: "+%{value} 建造者能力乘数",
   Islam5UpgradeHTML: "解锁时，生成一批科学，生成量等同于<b>工业时代</b>最昂贵科技所需",
   IslamLevelX: "伊斯兰教 %{level}",
   ItsukushimaShrine: "严岛神社",
   ItsukushimaShrineDescV2: "每当一个时代的所有科技被解锁时，生成一批科学，生成量等同于所有科技被解锁时的下一时代最廉价科技所需",
   JamesWatson: "詹姆斯·杜威·沃森",
   JamesWatsonDesc: "忙碌劳动者产出科学 +%{value} ",
   JamesWatt: "詹姆斯·瓦特",
   Japanese: "日本",
   JetPropulsion: "喷气推进",
   JohannesGutenberg: "约翰内斯·谷登堡",
   JohannesKepler: "约翰内斯·开普勒",
   JohnCarmack: "约翰·卡马克",
   JohnDRockefeller: "约翰·D·洛克菲勒",
   JohnMcCarthy: "约翰·麦卡锡",
   JohnVonNeumann: "约翰·冯·诺伊曼",
   JohnVonNeumannDesc: "忙碌劳动者产出科学 +%{value} ",
   JoinDiscord: "加入 Discord",
   JosephPulitzer: "约瑟夫·普利策",
   Journalism: "新闻学",
   JPMorgan: "约翰·皮尔庞特·摩根",
   JRobertOppenheimer: "朱利叶斯·罗伯特·奥本海默",
   JuliusCaesar: "尤利乌斯·凯撒",
   Justinian: "查士丁尼",
   Kanagawa: "神奈川",
   KanagawaDesc: "当此奇观被发现后，所有当前时代的伟人，获得 +1 额外等级（芝诺比娅除外）",
   KarlMarx: "卡尔·马克思",
   Knight: "骑士",
   KnightCamp: "骑士营地",
   Koti: "💵瑞士货币",
   KotiInStorage: "已存储",
   KotiProduction: "💵瑞士货币",
   LandTrade: "陆上贸易",
   Language: "语言",
   Lapland: "拉普兰区",
   LaplandDesc: "当被发现时，显示完整地图。周围 2 地块范围内的所有建筑，获得 +N 生产乘数， N 为已解锁时代的数量。该自然奇观仅可被发现于十二月",
   LargeHadronCollider: "大型强子对撞机",
   LargeHadronColliderDescV2: "所有信息时代的伟人额外获得 +2 等级。该奇观可被升级，并且每一次额外升级，为所有信息时代的伟人提供 +1 等级",
   Law: "法律",
   Lens: "镜头",
   LensWorkshop: "镜头工坊",
   LeonardoDaVinci: "列奥纳多·达·芬奇",
   Level: "等级",
   LevelX: "等级 %{level}",
   Liberalism: "自由主义",
   LiberalismLevel3DescHTML: "对于仓库<b>输出</b>与<b>输入</b>的资源调用，免费运输",
   LiberalismLevel5DescHTML: "电气化效果<b>翻倍</b>",
   LiberalismLevelX: "自由主义 等级 %{level}",
   Library: "图书馆",
   LighthouseOfAlexandria: "亚历山大灯塔",
   LighthouseOfAlexandriaDesc: "与该奇观相邻的建筑，获得 +5 存储乘数",
   LinusPauling: "莱纳斯·鲍林",
   LinusPaulingDesc: "闲置劳动者产出科学 +%{value} ",
   Literature: "文学",
   LiveData: "实际数据",
   LocomotiveFactory: "机车工厂",
   Logging: "伐木作业",
   LoggingCamp: "伐木场",
   LouisSullivan: "路易斯·沙利文",
   LouisSullivanDesc: "+%{value} 建造者能力乘数",
   Louvre: "卢浮宫",
   LouvreDesc: "每累积10个额外伟人，诞生一个此生伟人（对于建设完成的前累积也生效）",
   Lumber: "木材",
   LumberMill: "锯木场",
   LunarNewYear: "农历新年：长城为建筑提供双倍加成。大报恩寺琉璃塔为所有此生伟人提供 +1 等级",
   LuxorTemple: "卢克索神庙",
   LuxorTempleDescV2: "+1 忙碌劳动者科学产出。选取一宗帝国宗教，每条选项都将解锁更多增强",
   Machinery: "机械装置",
   Magazine: "杂志",
   MagazinePublisher: "杂志出版商",
   Maglev: "磁悬浮设备",
   MaglevFactory: "磁悬浮工厂",
   MahatmaGandhi: "圣雄甘地",
   ManageAgeWisdom: "管理时代智慧",
   ManagedImport: "托管输入",
   ManagedImportDescV2: "此建筑将自动运入周围 %{range} 地块范围的产出资源。此建筑的资源运输无法手动更改。将无视最大输入距离",
   ManageGreatPeople: "伟人管理",
   ManagePermanentGreatPeople: "管理永恒伟人",
   ManageSave: "管理存档",
   ManageWonders: "奇观管理",
   Manhattan: "曼哈顿",
   ManhattanProject: "曼哈顿计划",
   ManhattanProjectDesc: "所有铀矿获得 +2 生产、劳动者能力以及存储乘数。对于铀浓缩厂和原子设施，其相邻每座运转的铀矿（需建在铀资源点上），都会使它们获得 +1 生产乘数",
   Marble: "石雕",
   Marbleworks: "石雕铺",
   MarcoPolo: "马可·波罗",
   MarieCurie: "玛丽·居里",
   MarinaBaySands: "滨海湾金沙酒店",
   MarinaBaySandsDesc: "所有建筑获得 +5 劳动者能力乘数。该奇观可被升级，并且每一次额外升级，为所有建筑提供 +1 劳动者能力乘数",
   Market: "市场",
   MarketDesc: "将一种资源交换为另一种资源，每小时更新一次可用资源。",
   MarketRefreshMessage: " %{count} 座市场的交易已经更新",
   MarketSell: "交易",
   MarketSettings: "市场设定",
   MarketValueDesc: "利润率（基于均价） %{value}",
   MarketYouGet: "进项",
   MarketYouPay: "销项",
   MartinLuther: "马丁·路德",
   MaryamMirzakhani: "玛丽亚姆·米尔扎哈尼",
   MaryamMirzakhaniDesc: "闲置劳动者产出科学 +%{value}",
   Masonry: "砖石",
   MatrioshkaBrain: "套脑",
   MatrioshkaBrainDescV2: "计算帝国价值时，允许科学纳入计算（ 5 科学= 1 帝国价值）。 +5 劳动者科学产出。该奇观可被升级，并且每一次额外升级，提供 +1 劳动者科学产出，所有产出科学的建筑 +1 生产乘数（不包括克隆实验室）",
   MausoleumAtHalicarnassus: "摩索拉斯陵墓",
   MausoleumAtHalicarnassusDescV2: "周围 2 地块范围内所有建筑，对于范围内建筑 “输出” 与 “输入” 的资源调用，免费运输",
   MaxExplorers: "最大探险家数量",
   MaxTransportDistance: "最大输入距离",
   Meat: "肉",
   Metallurgy: "冶金",
   Michelangelo: "米开朗基罗",
   MiddleAge: "中世纪",
   MiddleClick: "鼠标中键复制蓝图",
   MiddleClickDescHTML: "选取你要<b>复制蓝图的建筑</b>点击，别点击任何地块！直接<b>按鼠标中键</b>指着想要建设同类建筑的地块",
   MilitaryTactics: "军事战术",
   Milk: "奶",
   Moai: "复活节岛摩艾石像",
   MoaiDesc: "翻译时未实装",
   MobileOverride: "移动端",
   MogaoCaves: "莫高窟",
   MogaoCavesDescV3: "忙碌劳动者占比每有 10% 获得 +1 幸福感。与该奇观相邻产出信仰的建筑，免去 -1 幸福感",
   MonetarySystem: "货币体系",
   MontSaintMichel: "圣米歇尔山",
   MontSaintMichelDesc: "闲置劳动者产生额外的文化并为周围 2 地块范围内所有建筑提供 +1 存储乘数。该奇观可以使用产生额外的文化进行升级,并且每一次额外升级都为周围 2 地块范围内所有建筑额外提供 +1 存储乘数",
   Mosque: "清真寺",
   MotionPicture: "电影",
   MountArarat: "亚拉拉特山",
   MountAraratDesc: "周围 2 地块范围内所有的建筑，获得 +X 生产、劳动者能力以及存储乘数。X = 生效永恒伟人等级的立方根",
   MountFuji: "富士山",
   MountFujiDescV2: "当佩特拉在其旁建造完成，佩特拉额外获得 +8 小时扭曲时间存储。当游戏运行时，在佩特拉中每分钟生成 20 扭曲时间（不被佩特拉自身加速，游戏离线时不再生成）",
   MountSinai: "西奈山",
   MountSinaiDesc: "当此奇观被发现时，诞生一个当前时代的伟人。所有产出信仰的建筑，获得 +5 存储乘数",
   MountTai: "泰山",
   MountTaiDesc: "当此奇观被发现后，所有产出科学的建筑，获得 +1 生产乘数。孔子效果翻倍（此生伟人与永恒伟人的效果翻倍，时代智慧除外）。当此奇观被发现时，生成一批科学，生成量等同于已解锁最昂贵科技所需",
   MoveBuilding: "移动建筑",
   MoveBuildingFail: "选取地块无效",
   MoveBuildingNoTeleport: "你没有足够的搬运者",
   MoveBuildingSelectTile: "选择一个地块……",
   MoveBuildingSelectTileToastHTML: "在地图上选择<b>一个已被探索的空白地块</b>作为目标",
   Movie: "电影",
   MovieStudio: "电影制片厂",
   Museum: "博物馆",
   Music: "音乐",
   MusiciansGuild: "音乐家协会",
   MutualAssuredDestruction: "共同毁灭原则",
   MutualFund: "共同基金",
   Name: "名字",
   Nanotechnology: "纳米技术",
   NapoleonBonaparte: "拿破仑·波拿巴",
   NaturalGas: "天然气",
   NaturalGasWell: "天然气井",
   NaturalWonderName: "自然奇观：%{name}",
   NaturalWonders: "自然奇观",
   Navigation: "航海术",
   NebuchadnezzarII: "尼布甲尼撒二世",
   Neighbor: "邻国",
   Neighbors: "邻国",
   NeighborsDescHTML:
      "对于每个邻国拥有的地块，你得到<b> +%{neighbor} 生产乘数的贸易地块加成</b>，对于每个同盟国拥有的地块，你得到<b> +%{ally} 生产乘数的贸易地块加成</b>。要成为邻国的盟友，请选择与他们相同的旗帜（地球旗帜除外）",
   Neuschwanstein: "新天鹅堡",
   NeuschwansteinDesc: "建造奇观时，建造者能力乘数 +10 ",
   Newspaper: "报纸",
   NextExplorersIn: "新的探险家就位剩余时间",
   NextMarketUpdateIn: "下次市场更新剩余时间",
   NiagaraFalls: "尼亚加拉瀑布",
   NiagaraFallsDescV2: "当此奇观被发现后，所有仓库、市场以及商队旅馆，获得 +N 存储乘数， N 为已解锁时代数量。阿尔伯特·爱因斯坦每个等级为研究基金会提供 +1 生产乘数（包括时代智慧提供的等级，不受其他增强影响——比如百老汇）",
   NielsBohr: "尼尔斯·玻尔",
   NielsBohrDescV2: "所有劳动者产出科学 +%{value} （忙碌劳动者占比高于 50% 且运输作业劳动者占比低于 50% 时生效）",
   NileRiver: "尼罗河",
   NileRiverDesc: "当此奇观被发现后，哈特谢普苏特效果翻倍（此生伟人与永恒伟人的效果翻倍，时代智慧除外）。所有小麦农场，获得 +1 生产与存储乘数。与该奇观相邻的小麦农场，获得 +5 生产与存储乘数",
   NoPowerRequired: "此类建筑无电能需求",
   NothingHere: "此处无可显示项",
   NotProducingBuildings: "未运转建筑",
   NuclearFission: "核裂变",
   NuclearFuelRod: "核燃料棒",
   NuclearMissile: "核导弹",
   NuclearMissileSilo: "核导弹发射井",
   NuclearPowerPlant: "核电站",
   NuclearReactor: "核反应堆",
   NuclearSubmarine: "核潜艇",
   NuclearSubmarineYard: "核潜艇船坞",
   OdaNobunaga: "织田信长",
   OfflineErrorMessage: "你正处于离线状态，此操作需要网络连接",
   OfflineProduction: "离线生产",
   OfflineProductionTime: "离线生产时间",
   OfflineProductionTimeDescHTML: "对于 <b> 首个 %{time} 离线时间</b> , 您可以在这里选择分配最大离线生产时间。把 <b> 超出最大离线生产时间的离线时间 </b> 全部转化为扭曲时间（ 1 秒= 1 扭曲时间）",
   OfflineTime: "离线时间",
   Oil: "石油",
   OilPress: "榨油机",
   OilRefinery: "炼油厂",
   OilWell: "油井",
   Ok: "确认",
   Oktoberfest: "十月节（慕尼黑啤酒节）：楚格峰效果翻倍",
   Olive: "橄榄",
   OlivePlantation: "橄榄种植园",
   Olympics: "奥运会",
   OnlyAvailableWhenPlaying: "仅在游玩 %{city} 时可获取",
   OpenLogFolder: "打开日志文件夹",
   OpenSaveBackupFolder: "打开备份文件夹",
   OpenSaveFolder: "打开存档文件夹",
   Opera: "歌剧",
   OperationNotAllowedError: "此操作不被允许",
   Opet: "欧佩特：狮身人面像不再增加消费乘数",
   OpticalFiber: "光纤",
   OpticalFiberPlant: "光纤厂",
   Optics: "光学",
   OptionsMenu: "选项",
   OptionsUseModernUIV2: "使用抗锯齿字体",
   OsakaCastle: "大阪城",
   OsakaCastleDesc: "为周围 2 地块范围提供电能。允许产出科学的建筑使用电气化（包括克隆实验室）",
   OtherPlatform: "其他平台",
   Ottoman: "奥斯曼帝国",
   OttoVonBismarck: "奥托·冯·俾斯麦",
   OxfordUniversity: "牛津大学",
   OxfordUniversityDescV3: "其产生的科学，等于其它产出科学建筑总科学产出的 10% 。建造完成时，生成一批科学，生成量等同于已解锁最昂贵科技所需",
   PabloPicasso: "巴勃罗·毕加索",
   Pagoda: "宝塔",
   PaintersGuild: "画家协会",
   Painting: "绘画",
   PalmJumeirah: "朱美拉棕榈岛",
   PalmJumeirahDesc: "+10 建造者能力乘数。该奇观可被升级，并且每一次额外升级，提供 +2 建造者能力乘数",
   Pamukkale: "棉花城堡",
   PamukkaleDesc: "当此奇观被发现时，将每个伟人（万能伟人和媒介伟人除外）消耗 1 伟人碎片（可用伟人碎片需要大于0）转换为本轮游玩中的同一伟人（此生伟人）",
   Panathenaea: "泛雅典娜节：海神波塞冬为所有建筑提供 +1 生产乘数",
   Pantheon: "万神殿",
   PantheonDescV2: "周围 2 地块范围内的所有建筑，获得 +1 劳动者能力与存储乘数。其产生的科学，基于所有神社的信仰总产出",
   Paper: "纸张",
   PaperMaker: "造纸作坊",
   Parliament: "议会",
   Parthenon: "帕特农神庙",
   ParthenonDescV2: "诞生两位古典时代伟人，每位伟人有 4 项可选。所有音乐家协会与画家协会，获得 +1 生产、劳动者能力以及存储乘数，并免去 -1 幸福感",
   Passcode: "密码",
   PasscodeToastHTML: "<b>%{code}</b> 是你的密码并且 30 分钟内有效",
   PatchNotes: "更新日志",
   Peace: "和平",
   Peacekeeper: "维和组织",
   Penthouse: "都市",
   PercentageOfProductionWorkers: "生产作业劳动者所占百分比",
   Performance: "性能",
   PermanentGreatPeople: "永恒伟人",
   PermanentGreatPeopleAcquired: "获得的永恒伟人",
   PermanentGreatPeopleUpgradeUndo: "回退永恒伟人升级：这将使已升级部分转化回碎片——你将获得 %{amount} 碎片",
   Persepolis: "波斯波利斯",
   PersepolisDesc: "所有铜矿、伐木场以及采石场，获得 +1 生产、劳动者能力以及存储乘数",
   PeterHiggs: "彼得·希格斯",
   PeterHiggsDesc: "忙碌劳动者产出科学 +%{value}",
   Petra: "佩特拉",
   PetraDesc: "当你离线时生成扭曲时间，你可以用它来加速你的帝国",
   PetraOfflineTimeReconciliation: "与服务器脱机时间核准后，获得 %{count} 扭曲时间",
   Petrol: "汽油",
   PhiloFarnsworth: "菲洛·法恩斯沃斯",
   Philosophy: "哲学",
   Physics: "物理学",
   PierreDeCoubertin: "皮埃尔·德·顾拜旦",
   PinResourceTab: "悬浮窗",
   Pizza: "披萨",
   Pizzeria: "披萨店",
   PlanetaryRover: "行星探测车",
   Plastics: "塑料",
   PlasticsFactory: "塑料厂",
   PlatformAndroid: "安卓（Android）",
   PlatformiOS: "苹果（iOS）",
   PlatformSteam: "PC端（Steam）",
   PlatformSyncInstructionHTML: "如果你想将此设备上的进度同步到新设备，点击<b>同步至一台新设备</b>以此获取一次性密码。在你的新设备上，点击<b>连接一台设备</b>并输入一次性密码",
   Plato: "柏拉图",
   PlayerHandle: "玩家信息",
   PlayerHandleOffline: "你正处于离线状态",
   PlayerMapClaimThisTile: "宣称此地块",
   PlayerMapClaimTileCondition2: "未被反作弊系统查禁",
   PlayerMapClaimTileCondition3: "已解锁所需科技：%{tech}",
   PlayerMapClaimTileCondition4: "未宣称地块或已过改变宣称之冷却时间",
   PlayerMapClaimTileCooldownLeft: "冷却时间剩余：%{time}",
   PlayerMapClaimTileNoLongerReserved: "此地块已不再受保留。你可以驱逐 <b>%{name}</b> 并为你自己宣称此地块。",
   PlayerMapEstablishedSince: "宣称时间",
   PlayerMapLastSeenAt: "上次活跃",
   PlayerMapMapAllyTileBonus: "同盟国的贸易地块加成",
   PlayerMapMapNeighborTileBonus: "邻国的贸易地块加成",
   PlayerMapMapTileBonus: "贸易地块加成",
   PlayerMapMenu: "贸易",
   PlayerMapOccupyThisTile: "占领此地块",
   PlayerMapOccupyTileCondition1: "此地块需与您的宣称地块或占领地块相邻",
   PlayerMapPageGoBackToCity: "返回城市",
   PlayerMapSetYourTariff: "设定你的关税",
   PlayerMapTariff: "关税",
   PlayerMapTariffApply: "应用关税税率",
   PlayerMapTariffDesc: "每项经过你宣称地的贸易，都将会向你缴纳关税。如果你增加关税，将从每项途经贸易中获取更多，但贸易经过的数量会更少。",
   PlayerMapTileAvailableTilePoint: "可用地块点数",
   PlayerMapTileFromOccupying: "来自宣称地块或占领地块",
   PlayerMapTileFromOccupyingTooltipHTML: "宣称地块或占领地块每个将每小时生成<b>%{point}</b>地块点数（从第一个宣称地块开始，每个地块最多累计生成%{max}天）",
   PlayerMapTileFromRank: "来自账号等级",
   PlayerMapTileTilePoint: "地块点数（重新宣称将不保留生成的地块点数）",
   PlayerMapTileUsedTilePoint: "已使用地块点数",
   PlayerMapTileUsedTilePointTooltipHTML: "你需要<b> 1 地块点数</b>来宣称地块或占领地块",
   PlayerMapTradesFrom: "%{name} 的贸易订单",
   PlayerMapUnclaimedTile: "无宣称地块",
   PlayerMapYourTile: "宣称地",
   PlayerTrade: "玩家贸易",
   PlayerTradeAddSuccess: "订单已成功添加",
   PlayerTradeAddTradeCancel: "取消",
   PlayerTradeAmount: "数量",
   PlayerTradeCancelDescHTML: "撤回此项订单后，将返还 <b>%{res}</b> （收取 <b>%{percent}</b> 撤回费用。共 <b>%{discard}</b> 由于存储溢出而被丢弃）<br><b>你是否确认撤回订单？</b>",
   PlayerTradeCancelTrade: "撤回订单",
   PlayerTradeClaim: "认领",
   PlayerTradeClaimAll: "认领全部贸易订单",
   PlayerTradeClaimAllFailedMessageV2: "未能认领任何交易——存储空间是否已满?",
   PlayerTradeClaimAllMessageV2: "已认领：<b>%{resources}</b>",
   PlayerTradeClaimAvailable: "共 %{count} 项已完成订单可被认领",
   PlayerTradeClaimTileFirst: "在贸易地图上宣称你的地块",
   PlayerTradeClaimTileFirstWarning: "只有在贸易地图上宣称地块后，你才能与其他玩家进行贸易。",
   PlayerTradeClearAll: "清除所有填入",
   PlayerTradeClearFilter: "清除筛选",
   PlayerTradeDisabledBeta: "只有当测试版本发布后，你才能创建玩家贸易订单",
   PlayerTradeFill: "交付",
   PlayerTradeFill50: "交付 50%",
   PlayerTradeFill95: "交付 95%",
   PlayerTradeFillAmount: "交付数量",
   PlayerTradeFillAmountMaxV2: "填入最大值",
   PlayerTradeFillBy: "交单者",
   PlayerTradeFillPercentage: "填入百分比",
   PlayerTradeFillSuccessV2: "<b>%{success}/%{total}</b> 贸易已完成。已交付 <b>%{fillAmount} %{fillResource}</b> 并已接收 <b>%{receivedAmount} %{receivedResource}</b>",
   PlayerTradeFillTradeButton: "交付",
   PlayerTradeFillTradeTitle: "交付订单",
   PlayerTradeFilters: "筛选",
   PlayerTradeFiltersApply: "应用",
   PlayerTradeFiltersClear: "清空",
   PlayerTradeFilterWhatIHave: "根据我所拥有的进行筛选",
   PlayerTradeFrom: "订单来源",
   PlayerTradeIOffer: "支付",
   PlayerTradeIWant: "求购",
   PlayerTradeMaxAll: "所有填入最大",
   PlayerTradeMaxTradeAmountFilter: "最大求购数量",
   PlayerTradeMaxTradeExceeded: "已达账号等级规定最大订单数",
   PlayerTradeNewTrade: "新贸易",
   PlayerTradeNoFillBecauseOfResources: "资源不足，贸易未成交",
   PlayerTradeNoValidRoute: "未能在你和 %{name} 之间找到有效贸易路径",
   PlayerTradeOffer: "支付",
   PlayerTradePlaceTrade: "发出订单",
   PlayerTradePlayerNameFilter: "玩家名称",
   PlayerTradeResource: "资源",
   PlayerTradeStorageRequired: "所需存储空间",
   PlayerTradeTabImport: "输入",
   PlayerTradeTabPendingTrades: "待处理",
   PlayerTradeTabTrades: "贸易",
   PlayerTradeTariffTooltip: "收集自贸易关税",
   PlayerTradeWant: "求购",
   PlayerTradeYouGetGross: "所得（税前）：%{res}",
   PlayerTradeYouGetNet: "所得（税后）：%{res}",
   PlayerTradeYouPay: "支付：%{res}",
   Poem: "诗词",
   PoetrySchool: "诗派",
   Politics: "政治",
   PolytheismLevelX: "多神教 %{level}",
   PorcelainTower: "大报恩寺琉璃塔",
   PorcelainTowerDesc: "+5 幸福感。建造完成时，所有你重生时可获取的当前额外伟人，将可在本轮获取（他们与永恒伟人遵循相同的规则）并在获取后提供相应此生伟人加成（之后新增的额外伟人不可再次本轮获取）",
   PorcelainTowerMaxPickPerRoll: "倾向于最大化每次选择",
   PorcelainTowerMaxPickPerRollDescHTML: "建造完成大报恩寺琉璃塔后进行伟人选择时，倾向于最大化每次选择数量",
   Poseidon: "海神波塞冬",
   PoseidonDescV2: "当此奇观被发现后，每秒使与该奇观相邻的建造完成后建筑免费升级至等级 25 和获得 +N 生产、劳动者能力以及存储乘数。 N 为建筑阶级",
   PoultryFarm: "肉禽场",
   Power: "电能",
   PowerAvailable: "可获电能",
   PowerUsed: "已使用电能",
   PreciousMetal: "贵金属",
   Printing: "印刷",
   PrintingHouse: "印刷厂",
   PrintingPress: "印刷术",
   PrivateOwnership: "私有制度",
   Produce: "产出",
   ProduceResource: "产出：%{resource}",
   ProductionMultiplier: "生产乘数",
   ProductionPriority: "生产优先级",
   ProductionPriorityDescV4: "优先级决定了建筑运输和生产的顺序——一个更大的数字意味着一个建筑在其他建筑之前运输和生产",
   ProductionWorkers: "生产作业劳动者",
   Progress: "进程",
   ProgressTowardsNextGreatPerson: "距下位重生时出现的伟人之进程",
   ProgressTowardsTheNextGreatPerson: "距获得下一个伟人之进程",
   PromotionGreatPersonDescV2: "作为媒介消耗，提升同时代任一永恒伟人，使其转化为下一时代任一永恒伟人",
   ProphetsMosque: "麦地那清真寺",
   ProphetsMosqueDesc: "哈伦·拉希德效果翻倍（此生伟人与永恒伟人的效果翻倍，时代智慧除外）。其产生的科学，基于所有清真寺的信仰总产出",
   Province: "区域",
   ProvinceAegyptus: "埃及",
   ProvinceAfrica: "非洲",
   ProvinceAsia: "亚洲",
   ProvinceBithynia: "卑斯尼亚",
   ProvinceCantabri: "坎塔布里",
   ProvinceCappadocia: "卡帕多西亚",
   ProvinceCilicia: "奇利西亚",
   ProvinceCommagene: "科马吉内",
   ProvinceCreta: "克里特岛",
   ProvinceCyprus: "塞浦路斯",
   ProvinceCyrene: "昔兰尼",
   ProvinceGalatia: "加拉太",
   ProvinceGallia: "加利亚",
   ProvinceGalliaCisalpina: "南高卢",
   ProvinceGalliaTransalpina: "山外高卢",
   ProvinceHispania: "伊斯帕尼亚",
   ProvinceIllyricum: "伊利里库姆",
   ProvinceItalia: "意大利",
   ProvinceJudia: "犹地亚",
   ProvinceLycia: "利西亚",
   ProvinceMacedonia: "马其顿",
   ProvinceMauretania: "毛里塔尼亚",
   ProvinceNumidia: "努米底亚",
   ProvincePontus: "本都",
   ProvinceSardiniaAndCorsica: "撒丁岛和科西嘉岛",
   ProvinceSicillia: "西西里岛",
   ProvinceSophene: "索芬妮",
   ProvinceSyria: "叙利亚",
   PublishingHouse: "出版社",
   PyramidOfGiza: "吉萨金字塔",
   PyramidOfGizaDesc: "所有产出劳动者的建筑，获得 +1 生产乘数",
   QinShiHuang: "秦始皇",
   Radio: "广播",
   RadioStation: "广播站",
   Railway: "铁路",
   RamessesII: "拉美西斯二世",
   RamessesIIDesc: " +%{value} 建造者能力乘数",
   RandomColorScheme: "随机色彩方案",
   RapidFire: "速射",
   ReadFullPatchNotes: "阅读补丁说明",
   RebirthHistory: "重生的历史",
   RebirthTime: "重生时间",
   Reborn: "重生",
   RebornModalDescV3: "你将开始一座新的帝国，但你的所有<b>此生</b>伟人将成为永恒碎片，碎片可用来升级你的<b>永恒伟人等级</b>。你也可以获得额外伟人碎片，这基于你的<b>帝国总价值</b>",
   RebornOfflineWarning: "你已离线。你只能在连接上服务器时重生。",
   RebornTradeWarning: "你还有发出或可认领的贸易订单。<b>重生将抹去与你相关的所有订单。</b>你应该首先考虑取消及认领订单。",
   RedistributeAmongSelected: "在选定对象中重新分配",
   RedistributeAmongSelectedCap: "限额",
   RedistributeAmongSelectedImport: "运输量",
   Refinery: "精炼厂",
   Reichstag: "德国国会大厦",
   Religion: "宗教",
   ReligionBuddhism: "佛教",
   ReligionChristianity: "基督教",
   ReligionDescHTML: "从<b>基督教、伊斯兰教、佛教以及多神教</b>中选择一宗作为你的帝国宗教。一旦选择，你将<b>不能更换宗教</b>。每宗宗教，你都能解锁更多增强",
   ReligionIslam: "伊斯兰教",
   ReligionPolytheism: "多神教",
   Renaissance: "文艺复兴",
   RenaissanceAge: "文艺复兴时期",
   ReneDescartes: "勒内·笛卡尔",
   RequiredDeposit: "所需沉积资源",
   RequiredWorkersTooltipV2: "生产需求劳动力，等同于乘数后消耗与产出的所有资源之和（不包括动态乘数）（科学目前不纳入计算）",
   RequirePower: "需要电能",
   RequirePowerDesc: "此建筑需要建于含电地块上，且其可将电能扩展至其相邻地块",
   Research: "研究",
   ResearchFund: "研究基金会",
   ResearchLab: "研究实验室",
   ResearchMenu: "研究",
   ResourceAmount: "数量",
   ResourceBar: "资源栏",
   ResourceBarExcludeStorageFullHTML: "将 <b>存储已满</b> 的建筑从未运转建筑中排除",
   ResourceBarExcludeTurnedOffOrNoActiveTransportHTML: "将 <b>关闭</b> 的建筑从未运转建筑中排除",
   ResourceBarShowUncappedHappiness: "显示实际幸福感",
   ResourceCloneTooltip: "生产乘数仅适用于克隆资源（即额外副本）",
   ResourceColor: "资源色彩",
   ResourceExportBelowCap: "低于限额仍输出",
   ResourceExportBelowCapTooltip: "允许其它建筑从此建筑运送资源，即使其数量低于设置限额",
   ResourceExportToSameType: "输出至同类型",
   ResourceExportToSameTypeTooltip: "允许其它同类型建筑从此建筑运送资源",
   ResourceFromBuilding: "%{resource} 来自 %{building}",
   ResourceImport: "资源运送",
   ResourceImportCapacity: "资源运输能力（可分配运输量）",
   ResourceImportImportCapV2: "限额",
   ResourceImportImportCapV2Tooltip: "当达到限额时，此建筑将停止运输该资源",
   ResourceImportImportPerCycleV2: "运输量",
   ResourceImportImportPerCycleV2ToolTip: "每个周期运输的资源量",
   ResourceImportPartialWarningHTML: "资源运输需求总运输量已超最大运输量：<b>每项资源运输每周期将只运输部分</b>",
   ResourceImportResource: "资源",
   ResourceImportSettings: "运送资源：%{res}",
   ResourceImportStorage: "现存量",
   ResourceNeeded: "需要额外的%{resource} x %{amount}",
   ResourceTransportPreference: "运送倾向",
   RevealDeposit: "显露",
   Revolution: "革命",
   RhineGorge: "莱茵峡谷",
   RhineGorgeDesc: "周围 2 地块范围内的每座奇观 +2 幸福感",
   RichardFeynman: "理查德·费曼",
   RichardFeynmanDesc: "所有劳动者产出科学 +%{value} （忙碌劳动者占比高于 50% 且运输作业劳动者占比低于 50% 时生效）",
   RichardJordanGatling: "理查·乔登·加特林",
   Rifle: "步枪",
   RifleFactory: "步枪厂",
   Rifling: "膛线",
   Rijksmuseum: "国家博物馆",
   RijksmuseumDesc: "+5 幸福感。所有消耗或产出文化的建筑，获得 +1 生产、存储以及劳动者能力乘数",
   RoadAndWheel: "道路与车轮",
   RobertNoyce: "罗伯特·诺伊斯",
   Robocar: "自动驾驶型汽车",
   RobocarFactory: "自动驾驶型汽车工厂",
   Robotics: "机器人技术",
   RockefellerCenterChristmasTree: "洛克菲勒中心圣诞树",
   RockefellerCenterChristmasTreeDesc: "每个已解锁时代提供 +3 幸福感。该自然奇观仅可被发现于十二月",
   Rocket: "火箭",
   RocketFactory: "火箭工厂",
   Rocketry: "火箭学",
   Roman: "罗马",
   RomanForum: "罗马议院广场",
   RudolfDiesel: "鲁道夫·迪塞尔",
   Rurik: "留里克",
   RurikDesc: "幸福感 +%{value}",
   SagradaFamilia: "圣家族大教堂",
   SagradaFamiliaDesc: "周围 2 地块范围内的所有建筑，获得 +N 生产、劳动者能力以及存储乘数。 N 为该奇观相邻建筑最大阶差",
   SaintBasilsCathedral: "瓦西里升天教堂",
   SaintBasilsCathedralDescV2: "允许资源开采建筑在资源点相邻地块运转。与该奇观相邻的 I 阶建筑，获得 +1 生产、劳动者能力以及存储乘数",
   Saladin: "萨拉丁",
   Samsuiluna: "萨姆苏·伊路那",
   Sand: "沙",
   Sandpit: "采沙坑",
   SantaClausVillage: "圣诞老人村（罗瓦涅米）",
   SantaClausVillageDesc: "建造完成时，诞生一位当前时代伟人。该奇观可被升级，并且每一次额外升级，提供一位额外伟人。当从这座奇观进行伟人选择时，每位伟人有 4 项可选。该奇观仅可在十二月建造",
   SargonOfAkkad: "萨尔贡大帝",
   Satellite: "卫星",
   SatelliteFactory: "卫星工厂",
   SatoshiNakamoto: "中本聪",
   Saturnalia: "农神节：阿尔卑斯山不再增加消费乘数",
   SaveAndExit: "保存并退出",
   School: "学校",
   Science: "科学",
   ScienceFromBusyWorkers: "忙碌劳动者产出科学",
   ScienceFromIdleWorkers: "闲置劳动者产出科学",
   SciencePerBusyWorker: "每个忙碌劳动者",
   SciencePerIdleWorker: "每个闲置劳动者",
   ScrollSensitivity: "滚动灵敏度",
   ScrollSensitivityDescHTML: "调整滚动鼠标滚轮时的灵敏度。<b>调整值必须位于 0.01 与 100 之间。默认值为 1 </b>",
   ScrollWheelAdjustLevelTooltip: "当光标位于此位置时，可以使用滚轮调整等级",
   SeaTradeCost: "海上贸易损耗",
   SeaTradeUpgrade: "与玩家跨海贸易。每个海洋地块损耗：%{tariff}",
   SelectCivilization: "选择文明",
   SelectedAll: "全选",
   SelectedCount: "已选择 %{count} 项",
   Semiconductor: "半导体",
   SemiconductorFab: "半导体工厂",
   SendExplorer: "派遣探险家",
   SergeiKorolev: "谢尔盖·科罗廖夫",
   SetAsDefault: "设为默认",
   SetAsDefaultBuilding: "设为全部 %{building} 的默认",
   Shamanism: "萨满教",
   Shelter: "避难所",
   Shortcut: "快捷键",
   ShortcutBuildingPageSellBuildingV2: "拆除建筑",
   ShortcutBuildingPageToggleBuilding: "切换生产状态",
   ShortcutBuildingPageToggleBuildingSetAllSimilar: "切换生产状态并应用于所有相同建筑",
   ShortcutBuildingPageUpgrade1: "升级键 1 (+1)",
   ShortcutBuildingPageUpgrade2: "升级键 2 (+5)",
   ShortcutBuildingPageUpgrade3: "升级键 3 (+10)",
   ShortcutBuildingPageUpgrade4: "升级键 4 (+15)",
   ShortcutBuildingPageUpgrade5: "升级键 5 (+20)",
   ShortcutClear: "清除",
   ShortcutConflict: "你的快捷键与 %{name} 冲突",
   ShortcutNone: "无",
   ShortcutPressShortcut: "按下快捷键以设置……",
   ShortcutSave: "保存",
   ShortcutScopeBuildingPage: "建筑",
   ShortcutScopeConstructionPage: "建造/升级页面",
   ShortcutScopeEmptyTilePage: "空白地块",
   ShortcutScopePlayerMapPage: "贸易地图",
   ShortcutScopeTechPage: "研究",
   ShortcutScopeUnexploredPage: "探索设置",
   ShortcutTechPageGoBackToCity: "返回城市",
   ShortcutTechPageUnlockTech: "解锁选中科技",
   ShortcutUpgradePageCancelAllUpgrades: "取消所有升级",
   ShortcutUpgradePageCancelUpgrade: "取消升级",
   ShortcutUpgradePageDecreaseLevel: "减少预升级等级",
   ShortcutUpgradePageEndConstruction: "结束建造",
   ShortcutUpgradePageIncreaseLevel: "增加预升级等级",
   ShowTransportArrow: "显示运送指示",
   ShowTransportArrowDescHTML: "关闭将隐藏运送指示。它也许能 <i>轻微地</i> 改善在低端设备上的性能表现。此项性能表现改善，需<b>重启你的游戏</b>以起效。",
   ShowUnbuiltOnly: "仅显示尚未建造完成的建筑",
   Shrine: "神社",
   SidePanelWidth: "侧板宽度",
   SidePanelWidthDescHTML: "更改侧板的宽度。<b>需重启游戏以生效</b>",
   SiegeRam: "攻城槌",
   SiegeWorkshop: "攻城工坊",
   Silicon: "硅",
   SiliconSmelter: "硅冶炼厂",
   Skyscraper: "摩天大楼",
   Socialism: "社会主义",
   SocialismLevel4DescHTMLV2: "生成一批科学，生成量等同于<b>世界大战时期</b>最廉价科技所需",
   SocialismLevel5DescHTMLV2: "生成一批科学，生成量等同于<b>冷战时期</b>最廉价科技所需",
   SocialismLevelX: "社会主义 等级 %{level}",
   SocialNetwork: "社交网络",
   Socrates: "苏格拉底",
   SocratesDesc: "忙碌劳动者产出科学 +%{value}",
   Software: "软件",
   SoftwareCompany: "软件公司",
   Sound: "声音",
   SoundEffect: "音效",
   SourceGreatPerson: "伟人：%{person}",
   SourceGreatPersonPermanent: "永恒伟人：%{person}",
   SourceIdeology: "意识形态：%{ideology}",
   SourceReligion: "帝国宗教：%{religion}",
   SourceResearch: "已研究：%{tech}",
   SourceTradition: "帝国传统：%{tradition}",
   SpaceCenter: "航天中心",
   Spacecraft: "航天器",
   SpacecraftFactory: "航天器工厂",
   SpaceNeedle: "太空针塔",
   SpaceNeedleDesc: "每座建造完成的奇观 +1 幸福感",
   SpaceProgram: "太空计划",
   Sports: "体育运动",
   Stable: "马厩",
   Stadium: "体育场",
   StartFestival: "欢度佳节吧！",
   Stateship: "国家状态",
   StatisticsBuildings: "建筑",
   StatisticsBuildingsSearchText: "输入建筑名称进行搜索",
   StatisticsEmpire: "帝国",
   StatisticsExploration: "探索",
   StatisticsOffice: "统计局",
   StatisticsOfficeDesc: "提供帝国的统计数据。生成用于探索地图的探险家",
   StatisticsResources: "资源",
   StatisticsResourcesDeficit: "净值",
   StatisticsResourcesDeficitDesc: "生产：%{output} - 消费：%{input}",
   StatisticsResourcesRunOut: "耗尽时间",
   StatisticsResourcesSearchText: "输入资源名称进行搜索",
   StatisticsScience: "科学",
   StatisticsScienceFromBuildings: "来自建筑的科学",
   StatisticsScienceFromWorkers: "来自劳动者的科学",
   StatisticsScienceProduction: "产出科学",
   StatisticsStalledTransportation: "运输停滞",
   StatisticsTotalTransportation: "总运输量",
   StatisticsTransportation: "运输",
   StatisticsTransportationPercentage: "运输作业劳动者所占百分比",
   StatueOfLiberty: "自由女神像",
   StatueOfLibertyDesc: "与该奇观相邻建筑，获得 +N 生产、劳动者能力以及存储乘数。 N 为受加成建筑其相邻同类型建筑数量",
   StatueOfZeus: "宙斯神像",
   StatueOfZeusDesc: "与该奇观相邻无沉积的地块，随机生成并显露沉积资源（根据已解锁资源去随机，每种各一个不重复）。与该奇观相邻 I 阶建筑，获得 +5 生产与存储乘数",
   SteamAchievement: "Steam 成就",
   SteamAchievementDetails: "查看 Steam 成就",
   SteamEngine: "蒸汽机",
   Steamworks: "蒸汽机厂",
   Steel: "钢",
   SteelMill: "炼钢厂",
   StephenHawking: "史蒂芬·霍金",
   Stock: "股票",
   StockExchange: "证券交易所",
   StockMarket: "股票市场",
   StockpileDesc: "这座建筑将在每个生产周期运送 %{capacity}x 所需资源，直到最大库存",
   StockpileMax: "最大库存",
   StockpileMaxDesc: "一旦这座建筑有能满足其进行 %{cycle} 个生产周期所需的全部资源，其将停止运入资源",
   StockpileMaxUnlimited: "无限制",
   StockpileMaxUnlimitedDesc: "直到存储空间填满前，建筑将一直运入资源",
   StockpileSettings: "库存输入系数",
   Stone: "石头",
   StoneAge: "石器时代",
   Stonehenge: "巨石阵",
   StonehengeDesc: "所有消耗或产出石头的建筑，获得 +1 生产乘数",
   StoneQuarry: "采石场",
   StoneTool: "石器",
   StoneTools: "石器",
   Storage: "存储空间",
   StorageBaseCapacity: "基础存储能力",
   StorageMultiplier: "存储乘数",
   StorageUsed: "已用存储空间",
   StPetersBasilica: "圣彼得大教堂",
   StPetersBasilicaDescV2: "所有教堂获得 +5 存储乘数。其产生的科学，基于所有教堂的信仰总产出",
   Submarine: "潜艇",
   SubmarineYard: "潜艇船坞",
   SuleimanI: "苏莱曼一世",
   SummerPalace: "颐和园",
   SummerPalaceDesc: "与该奇观相邻的消耗或产出火药的建筑，免去 -1 幸福感。所有消耗或产出火药的建筑，获得 +1 生产、劳动者能力以及存储乘数",
   Supercomputer: "超级计算机",
   SupercomputerLab: "超算研究室",
   SupporterPackRequired: "需要支持者包",
   SupporterThankYou: "放置文明的持续运营，得益于以下支持者的慷慨捐助（随机显示）",
   SwissBank: "瑞士银行",
   SwissBankDescV2: "将选定资源从相邻仓库中转换为💵瑞士货币——价值 10M 的可交易资源货币。每个周期基础产出1个💵瑞士货币（可被生产乘数影响），该奇观可被升级，并且每一次额外升级增加 1 基础产出（无论基础产出提升还是生产乘数提升都只是提升每周期等价兑换最大总数量）。瑞士银行可以存储无限量的💵瑞士货币",
   Sword: "剑",
   SwordForge: "铸剑铺",
   SydneyOperaHouse: "悉尼歌剧院",
   SydneyOperaHouseDescV2: "翻译时未实装",
   SyncToANewDevice: "同步至一台新设备",
   Synthetics: "合成材料",
   TajMahal: "泰姬陵",
   TajMahalDescV2: "建造完成时，诞生一位古典时代伟人与一位中世纪伟人。等级 20 以上建筑升级时，获得 +5 建造者能力乘数",
   TangOfShang: "商汤",
   TangOfShangDesc: "闲置劳动者产出科学 +%{value}",
   Tank: "坦克",
   TankFactory: "坦克工厂",
   TechAge: "时代",
   TechGlobalMultiplier: "增强",
   TechHasBeenUnlocked: "已解锁 %{tech} ",
   TechProductionPriority: "解锁建筑优先级————允许对每座建筑设定生产优先级",
   TechResourceTransportPreference: "解锁建筑运送倾向————允许设定建筑如何运送其生产所需资源。",
   TechResourceTransportPreferenceAmount: "数量",
   TechResourceTransportPreferenceAmountTooltip: "此建筑将倾向于，从有更多存储现量的建筑中运送资源",
   TechResourceTransportPreferenceDefault: "默认",
   TechResourceTransportPreferenceDefaultTooltip: "不覆盖此资源的运输首选项，而是使用建筑的运输首选选项",
   TechResourceTransportPreferenceDistance: "距离",
   TechResourceTransportPreferenceDistanceTooltip: "此建筑将倾向于，从更近的建筑中运送资源",
   TechResourceTransportPreferenceOverrideTooltip: "此资源具有运输首选项覆盖：%{mode}",
   TechResourceTransportPreferenceStorage: "存储",
   TechResourceTransportPreferenceStorageTooltip: "此建筑将倾向于，从更高已用存储空间所占百分比的建筑中运送资源",
   TechStockpileMode: "解锁库存模式————允许调整每座建筑库存方案",
   Teleport: "搬运者",
   TeleportDescHTML: "<b>每 %{time} 秒</b>新增一批搬运者。每批搬运者可被用于<b>移动一座建筑（奇观除外）</b>一次",
   Television: "电视节目",
   TempleOfArtemis: "阿尔忒弥斯神庙",
   TempleOfArtemisDesc: "建造完成时，所有铸剑铺与军械库，获得 +5 等级。所有铸剑铺与军械库，获得 +1 生产、劳动者能力以及存储乘数",
   TempleOfHeaven: "天坛",
   TempleOfHeavenDesc: "所有等级 10 及以上建筑，获得 +1 劳动者能力乘数",
   TempleOfPtah: "卜塔神庙",
   TerracottaArmy: "兵马俑",
   TerracottaArmyDesc: "所有铁矿，获得 +1 生产乘数、劳动者能力乘数以及存储乘数。与铁锻铺相邻的每座运转铁矿（需建在铁资源点上），使铁锻铺获得 +1 生产乘数",
   Thanksgiving: "感恩节：华尔街为建筑提供双倍增强并可应用于共同基金、对冲基金以及比特币矿工。研究基金会获得 +5 生产乘数",
   Theater: "剧院",
   Theme: "主题",
   ThemeColor: "主题色彩",
   ThemeColorResearchBackground: "研究背景",
   ThemeColorReset: "重置为默认",
   ThemeColorResetBuildingColors: "重置建筑色彩",
   ThemeColorResetResourceColors: "重置资源色彩",
   ThemeInactiveBuildingAlpha: "未运转建筑亮度",
   ThemePremiumTile: "此贴图仅购买了支持者包的玩家使用",
   ThemeResearchHighlightColor: "研究高亮色彩",
   ThemeResearchLockedColor: "未解锁研究色彩",
   ThemeResearchUnlockedColor: "已解锁研究色彩",
   ThemeTransportIndicatorAlpha: "运送指示亮度",
   Theocracy: "神权",
   TheoreticalData: "理论的数据",
   ThePentagon: "五角大楼",
   ThePentagonDesc: "建造完成后，生成可用于移动一座建筑（奇观除外）的搬运者。周围 2 地块范围内的所有建筑，获得 +1 生产，劳动者能力和储存乘数",
   TheWhiteHouse: "白宫",
   ThomasEdison: "托马斯·爱迪生",
   ThomasGresham: "托马斯·格雷沙姆",
   Tile: "地块",
   TileBonusRefreshIn: "贸易地块加成将于 <b>%{time}</b> 后刷新",
   TimBernersLee: "蒂姆·伯纳斯-李",
   TimeWarp: "时间扭曲倍数",
   TimeWarpWarning: "以超出计算机处理能力的速度加速，可能会导致数据丢失。使用风险自负！",
   ToggleWonderEffect: "切换奇观效果",
   Tool: "工具",
   TopkapiPalace: "托普卡帕宫",
   TopkapiPalaceDesc: "周围 2 地块范围内的所有建筑，获得 +X 存储乘数。X = 被加成建筑的 50% 生产乘数（不包括动态乘数）",
   TotalEmpireValue: "帝国总价值",
   TotalEmpireValuePerCycle: "每周期帝国价值",
   TotalEmpireValuePerCyclePerGreatPeopleLevel: "每级伟人的每周期帝国价值",
   TotalEmpireValuePerWallSecond: "在线每秒帝国价值",
   TotalEmpireValuePerWallSecondPerGreatPeopleLevel: "每级伟人的在线每秒帝国价值",
   TotalGameTimeThisRun: "本轮游戏时长",
   TotalScienceRequired: "所需科学总量",
   TotalStorage: "总存储空间",
   TotalWallTimeThisRun: "本轮在线时长",
   TotalWallTimeThisRunTooltip: "与游戏时长不同，佩特拉的时间扭曲和离线生产不会对其造成影响",
   TotalWorkers: "劳动者总数",
   TowerBridge: "伦敦塔桥",
   TowerBridgeDesc: "建造完成后，每3600个周期（游戏时间为1小时）就会诞生一个已解锁时代的此生伟人（可被离线生产时间与扭曲倍速影响）",
   TowerOfBabel: "巴别塔",
   TowerOfBabelDesc: "可为与该奇观相邻的建筑所有同类型建筑提供 +2 生产乘数（多次相邻不可叠加），需满足同类型建筑中至少有一座运转中建筑与奇观相邻",
   TradeFillSound: "填充贸易的声音",
   TradeValue: "贸易价值",
   TraditionCommerce: "商业",
   TraditionCultivation: "教化",
   TraditionDescHTML: "从<b>教化、商业、扩张以及荣耀</b>中选择一项作为你的帝国传统。一旦选择，你将<b>不能更换传统</b>。每项传统，你都能解锁更多增强",
   TraditionExpansion: "扩张",
   TraditionHonor: "荣耀",
   Train: "火车",
   TranslationPercentage: "%{language}的翻译进度目前为%{percentage}。可在GitHub上帮助改进此语言翻译。（简中译者：为确保效率，建议优先与当前译者们联系；初次翻译时，请在翻译人员中，添加自己的游戏昵称。）",
   TranslatorCredit: "Shallowsing、JLQ1134、NaiKeSiTe  （企鹅群907371958）",
   Translators: "翻译人员",
   TransportAllocatedCapacityTooltip: "分配用于运送此资源的建造者能力",
   TransportationWorkers: "运输作业劳动者",
   TransportCapacity: "运输能力",
   TransportCapacityMultiplier: "运输能力乘数",
   TransportManualControlTooltip: "运输此资源以建造/升级",
   TransportPlanCache: "运输方案缓存",
   TransportPlanCacheDescHTML: "每个周期，每座建筑基于其设定计算最佳运输方案——此进程对 CPU 能力需求较高。开启此项，将尝试缓存运输方案，如果已缓存的运输方案仍然有效，则可达到减少 CPU 使用率与帧率下降的效果。<b>【实验性功能】</b>",
   TribuneUpgradeDescGreatPeopleWarning: "你本轮游玩拥有伟人。你应该<b>先重生</b>。升至财务官级别，将重置你的本轮游玩。",
   TribuneUpgradeDescGreatPeopleWarningTitle: "请先重生",
   TribuneUpgradeDescV4:
      "若你无计划体验<b>可选的</b>在线功能，作为保民官，你能游玩完整版游戏（永恒伟人与时代智慧无等级上限）。为获取不受限制的在线功能访问权限（交易限制、贸易地块加成数量与重置永恒伟人功能），你将需要提升至财务官。<b>这是一项反机器人措施，旨在让每个人都可以免费玩游戏。</b>然而，<b>当账号等级升至财务官的这一瞬间</b>，你能继承伟人的等级有一定限制：<ul><li>青铜、铁器以及古典时代最高可继承至等级 <b>3</b> </li><li>中世纪、文艺复兴时期以及工业时代最高可继承至等级 <b>2</b> </li><li>世界大战、冷战以及信息时代最高可继承至等级 <b>1</b> </li></ul>超过等级限制的伟人碎片和<b>时代智慧</b>等级将<b>不能</b>被继承。",
   TurnOffFullBuildings: "关闭所有满存储的 %{building} ",
   TurnOnTimeWarpDesc: "每秒消耗 %{speed} 扭曲时间，同时加速你的帝国，使其以 %{speed} 倍速度运行。",
   Tutorial: "教程",
   TutorialPlayerFlag: "选择你的玩家旗帜",
   TutorialPlayerHandle: "输入你的玩家昵称（请避免使用不友好昵称）",
   TV: "电视",
   TVStation: "电视台",
   UnclaimedGreatPersonPermanent: "你有未迎接的<b>永恒伟人</b>，点此欢迎。",
   UnclaimedGreatPersonThisRun: "你有未迎接的<b>此生伟人</b>，点此欢迎。",
   UnexploredTile: "未探索地块",
   UNGeneralAssemblyCurrent: "当前联合国大会 #%{id} 决议效果",
   UNGeneralAssemblyMultipliers: "<b>+%{count}</b> 生产、劳动者能力以及存储乘数。作用于：<b>%{buildings}</b>",
   UNGeneralAssemblyNext: "即将启用的联合国大会 #%{id} 决议效果",
   UNGeneralAssemblyVoteEndIn: "结束前可以随时更改投票。投票结束剩余时间：<b>%{time}</b>",
   UniqueBuildings: "独特建筑",
   UniqueTechMultipliers: "独特技术乘数",
   UnitedNations: "联合国",
   UnitedNationsDesc: "所有 IV、V 以及 VI 阶建筑，获得 +1 生产、劳动者能力以及存储乘数。加入联合国，可为每项决议的效果投票",
   University: "大学",
   UnlockableResearch: "可解锁研究",
   UnlockBuilding: "解锁",
   UnlockTechProgress: "进程",
   UnlockXHTML: "解锁 <b>%{name}</b>",
   Upgrade: "升级",
   UpgradeBuilding: "建筑升级",
   UpgradeBuildingNotProducingDescV2: "该建筑正在升级——<b>生产将停止，直到升级完成</b>",
   UpgradeTo: "升至等级 %{level}",
   Uranium: "铀",
   UraniumEnrichmentPlant: "铀浓缩厂",
   UraniumMine: "铀矿",
   Urbanization: "城市化",
   UserAgent: "用户代理：%{driver}",
   View: "查看",
   ViewMenu: "视图",
   ViewTechnology: "查看",
   Vineyard: "葡萄园",
   VirtualReality: "虚拟现实",
   Voltaire: "伏尔泰",
   WallOfBabylon: "巴比伦城墙",
   WallOfBabylonDesc: "所有建筑获得 +N 存储乘数， 每个时代提供的 N 值如下：（铁器为1，古典至中世纪为2,文艺复兴至工业为3，世界大战至冷战为4，信息为5）",
   WallStreet: "华尔街",
   WallStreetDesc: "周围 2 地块范围内，所有产出硬币、钞票、债券、股票以及外汇的建筑，获得 +N 生产乘数。N 为 1 至 5 的随机值，每座受加成建筑单独取值，并且该值随每次市场更新而改变。约翰·D·洛克菲勒效果翻倍（此生伟人与永恒伟人的效果翻倍，时代智慧除外）",
   WaltDisney: "华特·迪士尼",
   Warehouse: "仓库",
   WarehouseAutopilotSettings: "自动寻路设置",
   WarehouseAutopilotSettingsEnable: "启用自动寻路",
   WarehouseAutopilotSettingsRespectCapSetting: "需求存储 < 限额",
   WarehouseAutopilotSettingsRespectCapSettingTooltip: "自动寻路将只运输存储量低于限额的资源",
   WarehouseDesc: "运送特定资源并提供额外存储空间",
   WarehouseExtension: "解锁仓库、商队旅馆扩展（联动）模式。在玩家贸易时，允许与商队旅馆相邻的仓库参与",
   WarehouseSettingsAutopilotDesc: "这座仓库将会使用它的可用运输量，从无存储空间的建筑中运输资源。当前可用运输量为 %{capacity}",
   WarehouseUpgrade: "解锁仓库自动模式。仓库与其相邻建筑之间免费运输。",
   WarehouseUpgradeDesc: "这座仓库与其相邻建筑之间免费运输。",
   Warp: "扭曲时间",
   WarpSpeed: "扭曲倍速",
   Water: "水",
   WellStockedTooltip: "具有良好库存的建筑，即有足够资源进行生产的建筑。包括生产中、满库存或因缺少劳动者而无法生产的建筑",
   WernherVonBraun: "韦恩赫尔·冯·布劳恩",
   Westminster: "威斯敏斯特",
   Wheat: "小麦",
   WheatFarm: "小麦农场",
   WildCardGreatPersonDescV2: "作为万用伟人，可以被消耗并转化为同时代任一永恒伟人",
   WilliamShakespeare: "威廉·莎士比亚",
   Wine: "葡萄酒",
   Winery: "葡萄酒厂",
   WishlistSpaceshipIdle: "闲置太空飞船愿望单",
   Wonder: "奇观",
   WonderBuilderCapacityDescHTML: "建造奇观时的<b>建造者能力</b>，受解锁奇观的<b>时代</b>与<b>科技</b>影响。",
   WondersBuilt: "现有世界奇观",
   WondersUnlocked: "已解锁世界奇观",
   WonderUpgradeLevel: "奇观等级",
   Wood: "原木",
   Worker: "劳动者",
   WorkerCapacityMultiplier: "劳动者能力乘数",
   WorkerHappinessPercentage: "幸福感乘数",
   WorkerMultiplier: "劳动者能力",
   WorkerPercentagePerHappiness: "每点幸福感提供 %{value}% 乘数",
   Workers: "劳动者",
   WorkersAvailableAfterHappinessMultiplier: "劳动者总数（幸福感乘数后）",
   WorkersAvailableBeforeHappinessMultiplier: "劳动者总数（幸福感乘数前）",
   WorkersBusy: "忙碌劳动者",
   WorkerScienceProduction: "劳动者产出科学",
   WorkersRequiredAfterMultiplier: "需求劳动者",
   WorkersRequiredBeforeMultiplier: "需求劳动力",
   WorkersRequiredForProductionMultiplier: "单位劳动者生产能力",
   WorkersRequiredForTransportationMultiplier: "单位劳动者运输能力",
   WorkersRequiredInput: "运输",
   WorkersRequiredOutput: "生产",
   WorldWarAge: "世界大战",
   WorldWideWeb: "万维网",
   WritersGuild: "作家协会",
   Writing: "写作",
   WuZetian: "女皇武则天",
   WuZetianDesc: "运输能力乘数 +%{value}",
   Xuanzang: "玄奘",
   YangtzeRiver: "长江",
   YangtzeRiverDesc: "当此奇观被发现后，所有消耗水的建筑，获得 +1 生产、劳动者能力以及存储乘数。郑和效果翻倍（此生伟人与永恒伟人的效果翻倍，时代智慧除外）；女皇武则天作为永恒伟人，其每级使长江为所有建筑提供 +1 存储乘数",
   YearOfTheSnake: "巳蛇",
   YearOfTheSnakeDesc: "建造完成后，进入一个新时代时，由每一个已解锁时代获取一个伟人改为获取相同数量的当代伟人。周围 2 地块范围内的所有建筑，获得 +1 生产乘数。该奇观可被升级，并且每一次额外升级为周围 2 地块范围内的所有建筑提供 +1 生产乘数。该奇观只能在农历新年期间（1.20 ~ 2.10）建造",
   YellowCraneTower: "黄鹤楼",
   YellowCraneTowerDesc: "选择伟人时 +1 可选项。周围 1 地块范围内的所有建筑，获得 +1 生产、劳动者能力以及存储乘数。若建造完成后与长江相邻，则范围增加至 2 地块",
   YuriGagarin: "尤里·加加林",
   ZagrosMountains: "扎格罗斯山脉",
   ZagrosMountainsDesc: "所有低于 5 生产乘数且与该奇观相邻的建筑，获得 +2 生产乘数。尼布甲尼撒二世效果翻倍（此生伟人与永恒伟人的效果翻倍，时代智慧除外）",
   ZahaHadid: "扎哈·哈迪德",
   ZahaHadidDesc: "+%{value} 建造者能力乘数",
   Zenobia: "芝诺比娅",
   ZenobiaDesc: "佩特拉扭曲时间存储 +%{value} 小时",
   ZhengHe: "郑和",
   ZigguratOfUr: "乌尔大塔庙",
   ZigguratOfUrDescV2: "每有正向 10 幸福感（最大值为 50），为所有不生产劳动者且为之前时代的解锁建筑，提供 +1 生产乘数（每个时代最大值如下：古典至中世纪为2,文艺复兴至工业为3，世界大战至冷战为4，信息为5）。奇观（包括自然奇观）不再提供 +1 幸福感。效果可关闭。",
   Zoroaster: "琐罗亚斯德",
   Zugspitze: "楚格峰",
   ZugspitzeDesc: "对于每个已解锁时代，获得一项点数。点数可被用于提升任意（本轮诞生的）此生伟人的额外等级",
};
