const path = require("path");
const { execSync } = require("child_process");
const { readdirSync, unlinkSync, renameSync } = require("fs");
const rootPath = path.resolve(path.join(__dirname, "../"));

const CountryCode = {
   EARTH: "Earth",
   AD: "Andorra",
   AE: "United Arab Emirates",
   AF: "Afghanistan",
   AG: "Antigua And Barbuda",
   AL: "Albania",
   AM: "Armenia",
   AO: "Angola",
   AR: "Argentina",
   AS: "American Samoa",
   AT: "Austria",
   AU: "Australia",
   AW: "Aruba",
   AX: "Aland Islands",
   AZ: "Azerbaijan",
   BA: "Bosnia And Herzegovina",
   BB: "Barbados",
   BD: "Bangladesh",
   BE: "Belgium",
   BF: "Burkina Faso",
   BG: "Bulgaria",
   BH: "Bahrain",
   BI: "Burundi",
   BJ: "Benin",
   BM: "Bermuda",
   BN: "Brunei Darussalam",
   BO: "Bolivia",
   BR: "Brazil",
   BS: "Bahamas",
   BT: "Bhutan",
   BW: "Botswana",
   BY: "Belarus",
   BZ: "Belize",
   CA: "Canada",
   CD: "Congo, Democratic Republic",
   CF: "Central African Republic",
   CG: "Congo",
   CH: "Switzerland",
   CI: 'Cote D"Ivoire',
   CL: "Chile",
   CM: "Cameroon",
   CN: "China",
   CO: "Colombia",
   CR: "Costa Rica",
   CU: "Cuba",
   CV: "Cape Verde",
   CY: "Cyprus",
   CZ: "Czech Republic",
   DE: "Germany",
   DJ: "Djibouti",
   DK: "Denmark",
   DM: "Dominica",
   DO: "Dominican Republic",
   DZ: "Algeria",
   EC: "Ecuador",
   EE: "Estonia",
   EG: "Egypt",
   EH: "Western Sahara",
   ER: "Eritrea",
   ES: "Spain",
   ET: "Ethiopia",
   FI: "Finland",
   FJ: "Fiji",
   FM: "Micronesia, Federated States Of",
   FO: "Faroe Islands",
   FR: "France",
   GA: "Gabon",
   GB: "United Kingdom",
   GD: "Grenada",
   GE: "Georgia",
   GH: "Ghana",
   GI: "Gibraltar",
   GL: "Greenland",
   GM: "Gambia",
   GN: "Guinea",
   GQ: "Equatorial Guinea",
   GR: "Greece",
   GT: "Guatemala",
   GW: "Guinea-Bissau",
   GY: "Guyana",
   HK: "Hong Kong",
   HN: "Honduras",
   HR: "Croatia",
   HT: "Haiti",
   HU: "Hungary",
   ID: "Indonesia",
   IE: "Ireland",
   IL: "Israel",
   IN: "India",
   IO: "British Indian Ocean Territory",
   IQ: "Iraq",
   IR: "Iran, Islamic Republic Of",
   IS: "Iceland",
   IT: "Italy",
   JM: "Jamaica",
   JO: "Jordan",
   JP: "Japan",
   KE: "Kenya",
   KG: "Kyrgyzstan",
   KH: "Cambodia",
   KI: "Kiribati",
   KM: "Comoros",
   KN: "Saint Kitts And Nevis",
   KP: "North Korea",
   KR: "Korea",
   KW: "Kuwait",
   KY: "Cayman Islands",
   KZ: "Kazakhstan",
   LA: 'Lao People"s Democratic Republic',
   LB: "Lebanon",
   LC: "Saint Lucia",
   LI: "Liechtenstein",
   LK: "Sri Lanka",
   LR: "Liberia",
   LS: "Lesotho",
   LT: "Lithuania",
   LU: "Luxembourg",
   LV: "Latvia",
   LY: "Libyan Arab Jamahiriya",
   MA: "Morocco",
   MC: "Monaco",
   MD: "Moldova",
   ME: "Montenegro",
   MG: "Madagascar",
   MH: "Marshall Islands",
   MK: "Macedonia",
   ML: "Mali",
   MM: "Myanmar",
   MN: "Mongolia",
   MO: "Macao",
   MR: "Mauritania",
   MT: "Malta",
   MU: "Mauritius",
   MV: "Maldives",
   MW: "Malawi",
   MX: "Mexico",
   MY: "Malaysia",
   MZ: "Mozambique",
   NA: "Namibia",
   NE: "Niger",
   NG: "Nigeria",
   NI: "Nicaragua",
   NL: "Netherlands",
   NO: "Norway",
   NP: "Nepal",
   NR: "Nauru",
   NZ: "New Zealand",
   OM: "Oman",
   PA: "Panama",
   PE: "Peru",
   PF: "French Polynesia",
   PG: "Papua New Guinea",
   PH: "Philippines",
   PK: "Pakistan",
   PL: "Poland",
   PR: "Puerto Rico",
   PS: "Palestinian Territory, Occupied",
   PT: "Portugal",
   PW: "Palau",
   PY: "Paraguay",
   QA: "Qatar",
   RO: "Romania",
   RS: "Serbia",
   RU: "Russian Federation",
   RW: "Rwanda",
   SA: "Saudi Arabia",
   SB: "Solomon Islands",
   SC: "Seychelles",
   SD: "Sudan",
   SE: "Sweden",
   SG: "Singapore",
   SI: "Slovenia",
   SK: "Slovakia",
   SL: "Sierra Leone",
   SM: "San Marino",
   SN: "Senegal",
   SO: "Somalia",
   SR: "Suriname",
   ST: "Sao Tome And Principe",
   SV: "El Salvador",
   SY: "Syrian Arab Republic",
   SZ: "Swaziland",
   TD: "Chad",
   TG: "Togo",
   TH: "Thailand",
   TJ: "Tajikistan",
   TL: "Timor-Leste",
   TM: "Turkmenistan",
   TN: "Tunisia",
   TO: "Tonga",
   TR: "Turkey",
   TT: "Trinidad And Tobago",
   TV: "Tuvalu",
   TW: "Taiwan",
   TZ: "Tanzania",
   UA: "Ukraine",
   UG: "Uganda",
   US: "United States",
   UY: "Uruguay",
   UZ: "Uzbekistan",
   VA: "Holy See (Vatican City State)",
   VC: "Saint Vincent And Grenadines",
   VE: "Venezuela",
   VN: "Vietnam",
   VU: "Vanuatu",
   WS: "Samoa",
   YE: "Yemen",
   ZA: "South Africa",
   ZM: "Zambia",
   ZW: "Zimbabwe",
};

const flagsPath = path.join(rootPath, "src", "textures", "flags");
const files = getAllFiles(flagsPath);
files.forEach((f) => {
   // execSync(`magick ${f} -resize 32x32 ${f}`);
   const oldFileName = path.basename(f, ".png");
   const dirName = path.dirname(f);
   renameSync(f, path.join(dirName, `Flag_${oldFileName.toUpperCase()}.png`));
});

function getAllFiles(dir) {
   const paths = readdirSync(dir, { withFileTypes: true });
   const files = paths.map((dirent) => {
      const res = path.resolve(dir, dirent.name);
      return dirent.isDirectory() ? getAllFiles(res) : res;
   });
   return files.flat();
}
