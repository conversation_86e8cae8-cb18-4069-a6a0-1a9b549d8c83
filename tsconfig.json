{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext", "WebWorker"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "noImplicitOverride": true, "verbatimModuleSyntax": true}, "include": ["src"], "exclude": ["**/node_modules", "**/.*/"], "references": [{"path": "./tsconfig.node.json"}, {"path": "./shared/tsconfig.json"}]}